package main

import (
	"context"
	"flag"
	"fmt"
	"music/internal/middleware"
	"os"
	"os/signal"
	"syscall"
	"time"

	"music/internal/config"
	"music/internal/handler"
	"music/internal/service"
	"music/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

// 常量定义
const (
	AppName    = "音乐服务API"
	AppVersion = "1.0.0"
	BuildTime  = "2025-03-17"
)

// 命令行参数
var (
	configFile = flag.String("f", "etc/service.yaml", "配置文件路径")
	version    = flag.Bool("v", false, "显示版本信息")
)

// 初始化配置
func initConfig() config.Config {
	flag.Parse()

	if envConfigFile := os.Getenv("CONFIG_FILE"); envConfigFile != "" {
		if envConfigFile == "env" {
			*configFile = "./etc/service.yaml"
		}
		fmt.Println("使用环境变量中的配置文件路径:", envConfigFile)
	}

	if *version {
		fmt.Printf("%s v%s (构建时间: %s)\n", AppName, AppVersion, BuildTime)
		os.Exit(0)
	}

	fmt.Printf("正在启动 %s v%s...\n", AppName, AppVersion)
	fmt.Printf("使用配置文件: %s\n", *configFile)

	var c config.Config
	conf.MustLoad(*configFile, &c)

	if err := c.Validate(); err != nil {
		logx.Errorf("配置验证失败: %+v", err)
	}

	return c
}

// 打印服务信息
func printServerInfo(host string, port int) {
	fmt.Printf("\n========================================\n")
	fmt.Printf("🚀 服务已启动! 访问地址:\n")
	fmt.Printf("   API服务: http://%s:%d/api\n", host, port)
	fmt.Printf("========================================\n\n")
}

// 设置服务器路由
func setupServer(server *rest.Server, ctx *svc.ServiceContext) {
	// 注册API路由
	handler.RegisterHandlers(server, ctx)
	logx.Info("API路由注册完成")
}

// 创建 oss 监控
func createOssMonitorService(ctx context.Context, svcCtx *svc.ServiceContext) {
	// 创建OSS监控配置

	// 正确的
	ossConfig := service.OssMonitorConfig{
		Endpoint:        "oss-cn-guangzhou.aliyuncs.com",  // OSS终端节点
		AccessKeyID:     "LTAI5t7U2Cq9mSiDypLaqjor",       // 请替换为真实的AccessKeyID
		AccessKeySecret: "******************************", // 请替换为真实的AccessKeySecret
		BucketName:      "ktv-playrecord",                 // 存储桶名称
		//MonitorFolder:   "upload",                     // 监控导入文件夹
		MonitorFolder:   "uploadTest",    // 监控导入文件夹 测试
		ProcessedFolder: "processedTest", // 处理完成的文件夹
		FailedFolder:    "failedTest",    // 处理失败的文件夹
		LocalTempDir:    "./file/temp",   // 本地临时目录
		CronSpec:        "0 */1 * * * *", // 每5分钟执行一次
		//CronSpec: "0 0 13 * * *", // 每天13点执行
	}

	ossMonitor, err := service.NewOssMonitorService(ossConfig, svcCtx.PlayRecordModel)
	if err != nil {
		logx.Errorf("创建OSS监控服务失败: %v", err)
		return
	}

	if err := ossMonitor.Start(); err != nil {
		logx.Errorf("启动OSS监控服务失败: %v", err)
		return
	}

	// 优雅关闭
	go func() {
		<-ctx.Done()
		ossMonitor.Stop()
	}()
}

func main() {
	// 初始化配置
	c := initConfig()

	// 配置日志
	logx.MustSetup(c.Log)
	logx.Info("日志系统初始化完成")

	logx.AddWriter(logx.NewWriter(os.Stdout))

	// 初始化服务上下文
	ctx := svc.NewServiceContext(c)
	logx.Info("服务上下文初始化完成")

	// 创建服务器上下文
	serverCtx, serverCancel := context.WithCancel(context.Background())
	defer serverCancel()

	// 创建HTTP服务器
	server := rest.MustNewServer(c.RestConf, rest.WithCors())
	defer server.Stop()
	logx.Info("HTTP服务器创建完成")

	// 使用 CORS 中间件
	server.Use(middleware.CorsMiddleware)

	// 启动 OSS监控服务
	//createOssMonitorService(serverCtx, ctx)

	// 设置路由
	setupServer(server, ctx)

	// 打印服务信息
	printServerInfo(c.Host, c.Port)

	// 启动服务器
	go func() {
		logx.Infof("开始监听 %s:%d", c.Host, c.Port)
		server.Start()
	}()

	// 优雅退出处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-serverCtx.Done():
		logx.Info("服务已停止")
	case sig := <-sigChan:
		logx.Infof("收到信号 %s, 正在关闭服务...", sig.String())

		_, cancel := context.WithTimeout(serverCtx, 10*time.Second)
		defer cancel()

		server.Stop()
		logx.Info("服务已优雅关闭")
	}

	fmt.Println("音乐服务已安全退出")
}
