# 优化后的项目结构

music-service/
├── api/                           # API 定义文件，按功能模块组织
│   ├── auth/
│   │   └── auth.api
│   ├── content/                   # 内容相关API
│   │   ├── song.api
│   │   └── hot.api
│   ├── business/                  # 业务相关API
│   │   ├── cp.api
│   │   ├── customer.api
│   │   └── disposition.api
│   └── stats/                     # 统计相关API
│       └── playrecord.api
│
├── cmd/                           # 命令行入口
│   ├── api/                       # API服务入口
│   │   └── main.go
│   └── worker/                    # 后台任务入口
│       └── main.go
│
├── configs/                       # 配置文件目录
│   ├── dev/                       # 开发环境配置
│   │   └── api.yaml
│   ├── prod/                      # 生产环境配置
│   │   └── api.yaml
│   └── test/                      # 测试环境配置
│       └── api.yaml
│
├── internal/                      # 内部实现，按功能模块组织
│   ├── config/                    # 配置结构定义
│   │   └── config.go
│   ├── domain/                    # 领域模型和业务规则
│   │   ├── auth/
│   │   ├── song/
│   │   ├── playrecord/
│   │   └── customer/
│   ├── handler/                   # HTTP处理器，按功能模块组织
│   │   ├── auth/
│   │   ├── content/
│   │   │   ├── song/
│   │   │   └── hot/
│   │   ├── business/
│   │   │   ├── cp/
│   │   │   ├── customer/
│   │   │   └── disposition/
│   │   └── stats/
│   │       └── playrecord/
│   ├── logic/                     # 业务逻辑，按功能模块组织
│   │   ├── auth/
│   │   ├── content/
│   │   │   ├── song/
│   │   │   └── hot/
│   │   ├── business/
│   │   │   ├── cp/
│   │   │   ├── customer/
│   │   │   └── disposition/
│   │   └── stats/
│   │       └── playrecord/
│   ├── middleware/                # 中间件
│   │   ├── auth.go
│   │   ├── cors.go
│   │   └── error.go
│   ├── model/                     # 数据模型，按功能模块组织
│   │   ├── auth/
│   │   ├── content/
│   │   ├── business/
│   │   └── stats/
│   ├── service/                   # 外部服务集成
│   │   ├── oss/
│   │   └── redis/
│   └── svc/                       # 服务上下文
│       └── servicecontext.go
│
├── pkg/                           # 可重用的公共包
│   ├── auth/                      # 认证相关
│   ├── cache/                     # 缓存相关
│   ├── constants/                 # 常量定义
│   ├── errors/                    # 错误定义
│   ├── logger/                    # 日志工具
│   ├── storage/                   # 存储相关
│   │   └── oss/
│   └── utils/                     # 通用工具函数
│       ├── file.go
│       ├── time.go
│       └── validator.go
│
├── scripts/                       # 脚本文件
│   ├── build/                     # 构建脚本
│   │   ├── build.sh
│   │   └── generate.sh
│   └── deploy/                    # 部署脚本
│       ├── docker/
│       │   ├── api/
│       │   │   └── Dockerfile
│       │   └── worker/
│       │       └── Dockerfile
│       └── k8s/                   # Kubernetes配置
│
├── storage/                       # 本地存储目录
│   ├── logs/                      # 日志目录
│   ├── uploads/                   # 上传文件目录
│   └── exports/                   # 导出文件目录
│
├── test/                          # 测试文件
│   ├── integration/               # 集成测试
│   └── unit/                      # 单元测试
│
├── .dockerignore
├── .gitignore
├── go.mod
├── go.sum
├── Makefile                       # 构建脚本
└── README.md                      # 项目说明