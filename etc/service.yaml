Name: music-api
Host: 0.0.0.0
Port: 8888
Timeout: 360000  # API超时时间，单位毫秒

Auth:
  AccessSecret: your-secret-key-12345
  AccessExpire: 43200  # 12小时过期

MysqlOut:
  DataSource: root:123456@tcp(127.0.0.1:3306)/ktv?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

MysqlIn:
  DataSource: root:123456@tcp(127.0.0.1:3306)/music?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
#  DataSource: ktv_user:ZAQ!2wsx@tcp(rm-7xvw6sojr7n6781uaxo.mysql.rds.aliyuncs.com:3306)/ktv-basic?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Log:
  Mode: file
  Path: logs
  Level: info
  Compress: false
  KeepDays: 7

Redis:
   Host: "localhost:6379"  # Redis服务器地址
   Type: node              # Redis类型: node(单节点), cluster(集群)
   Pass: ""                # Redis密码，可选
   DB: 0                   # 数据库编号

File:
  UploadPath: "./file/upload"
  ExportPath: "./file/exports"

YjxOss:
  EndPoint: "http://oss-cn-beijing.aliyuncs.com"
  AccessKeyId: "LTAI5tRJpJKEkr54ceE5nuYK"
  AccessKeySecret: "******************************"
  BucketName: "shengwangshuju"
  PlatformOssPath: "shengwangshiyongshuju/platform/"
  AnchorOssPath: "shengwangshiyongshuju/host/"
  LicenseOssPath: "shengwangshiyongshuju/drm/"