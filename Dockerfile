# 阶段一: 构建阶段
FROM golang:1.19-alpine AS builder

# 设置工作目录
WORKDIR /build

# 安装依赖
RUN apk add --no-cache git ca-certificates tzdata && \
    update-ca-certificates

# 复制Go模块定义
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o music-service main.go

# 阶段二: 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata && \
    update-ca-certificates && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制可执行文件
COPY --from=builder /build/music-service /app/
COPY --from=builder /build/etc/api.yaml.example /app/etc/api.yaml

# 暴露端口
EXPOSE 8888

# 设置环境变量
ENV TZ=Asia/Shanghai

# 运行服务
CMD ["/app/music-service", "-f", "/app/etc/api.yaml"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget -q -O- http://localhost:8888/api/health || exit 1

# 元数据
LABEL maintainer="Your Name <<EMAIL>>" \
      version="1.0.0" \
      description="音乐服务API容器"
