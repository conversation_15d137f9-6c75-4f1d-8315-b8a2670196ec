package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"music/internal/client"
	"music/internal/config"
)

func main() {
	// 测试配置
	conf := &config.Config{
		YjxOss: struct {
			EndPoint        string
			AccessKeyId     string
			AccessKeySecret string
			BucketName      string
			PlatformOssPath string
			AnchorOssPath   string
			LicenseOssPath  string
		}{
			EndPoint:        "http://oss-cn-beijing.aliyuncs.com",
			AccessKeyId:     "LTAI5tRJpJKEkr54ceE5nuYK",
			AccessKeySecret: "******************************",
			BucketName:      "shengwangshuju",
			PlatformOssPath: "shengwangshiyongshuju/platform/",
			AnchorOssPath:   "shengwangshiyongshuju/host/",
			LicenseOssPath:  "shengwangshiyongshuju/drm/",
		},
	}

	// 创建OSS客户端
	ossClient, err := client.NewYjxOssClient(conf)
	if err != nil {
		log.Fatalf("创建OSS客户端失败: %v", err)
	}

	fmt.Println("OSS客户端创建成功!")

	// 创建一个测试文件
	testFileName := "test_export.txt"
	testFilePath := filepath.Join(".", testFileName)
	
	// 写入测试内容
	testContent := fmt.Sprintf("测试导出文件 - %s", time.Now().Format("2006-01-02 15:04:05"))
	err = os.WriteFile(testFilePath, []byte(testContent), 0644)
	if err != nil {
		log.Fatalf("创建测试文件失败: %v", err)
	}
	defer os.Remove(testFilePath) // 清理测试文件

	fmt.Printf("创建测试文件: %s\n", testFilePath)

	// 测试上传到平台目录
	ossPath, err := ossClient.UploadFileToplatform(testFilePath, testFileName)
	if err != nil {
		log.Fatalf("上传文件到OSS失败: %v", err)
	}

	fmt.Printf("文件上传成功，OSS路径: %s\n", ossPath)

	// 生成预签名URL
	downloadUrl, err := ossClient.GeneratePresignedURL(ossPath, 24*time.Hour)
	if err != nil {
		log.Fatalf("生成下载URL失败: %v", err)
	}

	fmt.Printf("下载URL生成成功: %s\n", downloadUrl)

	// 获取公共URL
	publicUrl := ossClient.GetPublicURL(ossPath)
	fmt.Printf("公共URL: %s\n", publicUrl)

	fmt.Println("OSS集成测试完成!")
}
