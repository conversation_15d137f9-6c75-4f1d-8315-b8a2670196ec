CREATE TABLE `cp` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版权方名称',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1开启 0关闭',
  `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '过期时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='版权方表';


CREATE TABLE `customer` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `vid` int unsigned NOT NULL DEFAULT '0' COMMENT '客户应用vid',
  `cid` int unsigned NOT NULL DEFAULT '0' COMMENT '客户cid',
  `ip` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户ip白名单，*代表任意ip可访问',
  `appid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '选填',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1开启 0关闭',
  `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '过期时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `vid` (`vid`)
) ENGINE=InnoDB AUTO_INCREMENT=503 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户表';


CREATE TABLE `disposition_detail` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置编号，多条对应主配置表一条',
  `vid` int unsigned NOT NULL DEFAULT '0' COMMENT 'vid',
  `resource_types` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1,2,3,4' COMMENT '歌曲类型：1原唱&伴奏，2伴奏，3原唱，4MV（含多音轨），5MV（无多音轨）。可设置多值，如："1,2,3"',
  `cp_id` int unsigned NOT NULL DEFAULT '1' COMMENT '版权方id',
  `billing_type` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '付费方式：1套餐包，2后付费',
  `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '过期时间',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1开启 0关闭',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `vid` (`vid`) USING BTREE,
  KEY `order_code` (`order_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=906 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限配置明细表';

CREATE TABLE `disposition` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置唯一编号',
  `vid` int unsigned NOT NULL DEFAULT '0' COMMENT 'vid',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1开启 0关闭',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_code` (`order_code`),
  KEY `vid` (`vid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=482 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限配置主表';


CREATE TABLE `song_hot` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `hot_type` int unsigned DEFAULT '0' COMMENT '榜单类型',
  `song_code` bigint unsigned NOT NULL DEFAULT '0' COMMENT '歌曲编号',
  `vid` int unsigned NOT NULL DEFAULT '0' COMMENT 'vid',
  `num` int unsigned NOT NULL DEFAULT '0' COMMENT '排序号（当hot_type<=1时为热度值）',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `type_code_vid` (`song_code`,`vid`,`hot_type`) USING BTREE,
  KEY `num` (`num`) USING BTREE,
  KEY `vid` (`vid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=58834 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='热歌表';

CREATE TABLE `song_hot_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `hot_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '榜单类型',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '榜单名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 0关闭 1开启',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `hot_typ` (`hot_type`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='热歌榜单类型表';


CREATE TABLE `song` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `import_id` int unsigned NOT NULL DEFAULT '0' COMMENT '导入id，年月日时，如2024121201',
  `type` tinyint NOT NULL DEFAULT '1' COMMENT '歌曲类型：1原唱&伴奏，2伴奏，3原唱，4多音轨',
  `vendor_id` int unsigned NOT NULL DEFAULT '0' COMMENT '版权方id（cp的id）',
  `song_code` bigint unsigned NOT NULL DEFAULT '0' COMMENT '歌曲唯一编号，生成规则：微秒时间戳去除首位的1，末尾后加0',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '歌曲名称',
  `singer` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '歌手名称',
  `vendor_song_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版权方侧的歌曲id',
  `vendor_release_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '作品发布时间',
  `song_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '歌曲存储路径，命名规则（同一首歌不同的码率/副歌片段路径相同，文件名不同）：release/drm/plaintext/$vendor_song_id.mp4',
  `drm_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '歌曲drm路径，命名规则： release/drm/$vendor_song_id_1.mp4',
  `license_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'drm的license密钥，与songcode一一对应',
  `poster_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报路径，命名规则：pic/release/jpg/3/长_宽/$vendor_song_id.jpg',
  `lyric_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '歌词路径，命名规则（这里{LYRIC}固定不变）：release/lyric/{LYRIC}/1/$vendor_song_id',
  `lyric_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '歌词类型：0 zip格式，1 lrc格式，2 vtt格式。可多个，如"1,2"',
  `pitch_type` tinyint NOT NULL DEFAULT '2' COMMENT '支持打分：1是，2否',
  `pitchs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打分类型，可多个，如"1,2"',
  `high_part` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '副歌片段',
  `high_part_type` tinyint NOT NULL DEFAULT '1' COMMENT '副歌类型，1无副歌，2机器校验，3人工校验',
  `quality_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '码率，可多个，如“1,2,3”',
  `tag_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签id，可多个，如“1,2,3”',
  `duration` int unsigned NOT NULL DEFAULT '0' COMMENT '歌曲时长，秒',
  `close_time` int NOT NULL DEFAULT '0' COMMENT '下架时间戳（status设为0时的时间戳+3600s）',
  `hot_num` int NOT NULL DEFAULT '0' COMMENT '热度值',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：1上架 0下架',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `song_code` (`song_code`) USING BTREE,
  KEY `update_time` (`update_time`) USING BTREE,
  KEY `vendor_song_id` (`vendor_song_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=656197 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='歌曲表';

-- 歌曲播放记录表
CREATE TABLE IF NOT EXISTS `song_play_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `lts` bigint(20) NOT NULL COMMENT '时间戳，必须存在',
  `vid` bigint(20) NOT NULL COMMENT 'vid信息',
  `unique_id` varchar(64) NOT NULL COMMENT '唯一ID',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP（隐私字段，不推送）',
  `cpid` bigint(20) NOT NULL COMMENT '版权方ID',
  `song_code` bigint(20) NOT NULL COMMENT '歌曲编号',
  `song_duration` int(11) NOT NULL DEFAULT '0' COMMENT '歌曲时长，单位秒',
  `play_duration` int(11) NOT NULL DEFAULT '0' COMMENT '播放时长，单位秒',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间，单位秒',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间，单位秒',
  `playback_pos` int(11) NOT NULL DEFAULT '0' COMMENT '停播位置，单位秒',
  `play_num` int(11) NOT NULL DEFAULT '0' COMMENT '有效次数',
  `scene_type` int(11) NOT NULL DEFAULT '0' COMMENT '场景类型',
  `play_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '播放类型（1:DRM, 2:明文, 3:副歌）',
  `free_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否免费（1:免费, 2:不免费）',
  `record_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '记录类型（1:API上报, 2:SDK上报）',
  `use_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型（1:主播, 2:平台）',
  `uid` varchar(64) DEFAULT NULL COMMENT '用户ID（隐私字段，不推送）',
  `channel_id` varchar(64) DEFAULT NULL COMMENT '频道ID（隐私字段，不推送）',
  `push_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推送状态（0:未推送, 1:已推送）',
  `push_time` bigint(20) DEFAULT NULL COMMENT '推送时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_id` (`unique_id`),
  KEY `idx_lts` (`lts`),
  KEY `idx_vid` (`vid`),
  KEY `idx_cpid` (`cpid`),
  KEY `idx_song_code` (`song_code`),
  KEY `idx_push_status` (`push_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歌曲播放记录表';
