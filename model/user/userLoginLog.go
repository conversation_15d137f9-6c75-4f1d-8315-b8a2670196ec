package user

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	userLoginLogFieldNames  = strings.Join(userLoginLogRows, ",")
	userLoginLogRows        = []string{"id", "user_id", "user_uuid", "ip", "user_agent", "opt", "create_time"}
	ErrUserLoginLogNotFound = errors.New("登录日志不存在")
)

type (
	UserLoginLogModel interface {
		FindOne(id int64) (*UserLoginLog, error)
		FindByUserId(userId int64, page, pageSize int64) ([]*UserLoginLog, int64, error)
		FindLatestByUserId(userId int64) (*UserLoginLog, error)
		FindByUserUuid(userUuid string, page, pageSize int64) ([]*UserLoginLog, int64, error)
		FindLatestByUserUuid(userUuid string) (*UserLoginLog, error)
		Insert(data *UserLoginLog) (sql.Result, error)
	}

	defaultUserLoginLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UserLoginLog struct {
		Id         int64     `db:"id"`
		UserId     int64     `db:"user_id"`
		UserUuid   string    `db:"user_uuid"`
		Ip         string    `db:"ip"`
		UserAgent  string    `db:"user_agent"`
		Opt        string    `db:"opt"`
		CreateTime time.Time `db:"create_time"`
	}
)

func NewUserLoginLogModel(conn sqlx.SqlConn) UserLoginLogModel {
	return &defaultUserLoginLogModel{
		conn:  conn,
		table: "user_login_log",
	}
}

func (m *defaultUserLoginLogModel) FindOne(id int64) (*UserLoginLog, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", userLoginLogFieldNames, m.table)
	var resp UserLoginLog
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserLoginLogNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserLoginLogModel) FindByUserId(userId int64, page, pageSize int64) ([]*UserLoginLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where user_id = ? order by id desc limit ?, ?", userLoginLogFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where user_id = ?", m.table)

	var resp []*UserLoginLog
	var count int64
	err := m.conn.QueryRows(&resp, query, userId, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, userId)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultUserLoginLogModel) FindByUserUuid(userUuid string, page, pageSize int64) ([]*UserLoginLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where user_uuid = ? order by id desc limit ?, ?", userLoginLogFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where user_uuid = ?", m.table)

	var resp []*UserLoginLog
	var count int64
	err := m.conn.QueryRows(&resp, query, userUuid, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, userUuid)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultUserLoginLogModel) FindLatestByUserId(userId int64) (*UserLoginLog, error) {
	query := fmt.Sprintf("select %s from %s where user_id = ? order by id desc limit 1", userLoginLogFieldNames, m.table)
	var resp UserLoginLog
	err := m.conn.QueryRow(&resp, query, userId)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserLoginLogNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserLoginLogModel) FindLatestByUserUuid(userUuid string) (*UserLoginLog, error) {
	query := fmt.Sprintf("select %s from %s where user_uuid = ? order by id desc limit 1", userLoginLogFieldNames, m.table)
	var resp UserLoginLog
	err := m.conn.QueryRow(&resp, query, userUuid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserLoginLogNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserLoginLogModel) Insert(data *UserLoginLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(userLoginLogRows[1:], ","))
	return m.conn.Exec(query, data.UserId, data.UserUuid, data.Ip, data.UserAgent, data.Opt, time.Now())
}
