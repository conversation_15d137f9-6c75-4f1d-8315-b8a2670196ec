package user

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"golang.org/x/crypto/bcrypt"
)

var (
	userFieldNames         = strings.Join(userRows, ",")
	userRows               = []string{"id", "uuid", "username", "password", "nickname", "email", "phone", "role", "remark", "status", "update_time", "create_time"}
	ErrUserNotFound        = errors.New("用户不存在")
	ErrUserAlreadyExists   = errors.New("用户已存在")
	ErrPhoneAlreadyExists  = errors.New("账号已存在")
	ErrUserPasswordInvalid = errors.New("密码错误")
	ErrUserStatusInvalid   = errors.New("用户状态无效")
	ErrGenerateTokenFailed = errors.New("生成Token失败")
)

const (
	DefaultPw = "aobai123"
)

type (
	UserModel interface {
		FindOne(id int64) (*User, error)
		FindOneByUuid(uuid string) (*User, error)
		FindOneByUsername(username string) (*User, error)
		FindOneByPhone(phone string) (*User, error)
		FindAll(page, pageSize int64) ([]*User, error)
		Count() (int64, error)
		Insert(data *User) (sql.Result, error)
		Update(data *User) error
		Delete(id int64) error
		FindByConditions(username, phone string, role int64, page, pageSize int64) ([]*User, int64, error)
		UpdatePw(userId int64, newPw string) error
	}

	defaultUserModel struct {
		conn  sqlx.SqlConn
		table string
	}

	User struct {
		Id         int64     `db:"id"`
		Uuid       string    `db:"uuid"` // 新增的UUID字段
		Username   string    `db:"username"`
		Password   string    `db:"password"`
		Nickname   string    `db:"nickname"`
		Email      string    `db:"email"`
		Phone      string    `db:"phone"`
		Role       int64     `db:"role"` //  1业务员，2管理员，3超级管理员
		Remark     string    `db:"remark"`
		Status     int64     `db:"status"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
	}
)

func NewUserModel(conn sqlx.SqlConn) UserModel {
	return &defaultUserModel{
		conn:  conn,
		table: "user",
	}
}

func (m *defaultUserModel) FindOne(id int64) (*User, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", userFieldNames, m.table)
	var resp User
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserModel) FindOneByUuid(uuid string) (*User, error) {
	query := fmt.Sprintf("select %s from %s where uuid = ? limit 1", userFieldNames, m.table)
	var resp User
	err := m.conn.QueryRow(&resp, query, uuid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserModel) FindOneByUsername(username string) (*User, error) {
	query := fmt.Sprintf("select %s from %s where username = ? limit 1", userFieldNames, m.table)
	var resp User
	err := m.conn.QueryRow(&resp, query, username)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserModel) FindOneByPhone(phone string) (*User, error) {
	query := fmt.Sprintf("select %s from %s where phone = ? limit 1", userFieldNames, m.table)
	var resp User
	err := m.conn.QueryRow(&resp, query, phone)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserModel) FindAll(page, pageSize int64) ([]*User, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s order by id desc limit ?, ?", userFieldNames, m.table)
	var resp []*User
	err := m.conn.QueryRows(&resp, query, offset, pageSize)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultUserModel) Count() (int64, error) {
	query := fmt.Sprintf("select count(*) from %s", m.table)
	var count int64
	err := m.conn.QueryRow(&count, query)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultUserModel) Insert(data *User) (sql.Result, error) {
	// 生成UUID
	if data.Uuid == "" {
		data.Uuid = uuid.New().String()
	}

	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(userRows[1:], ","))
	now := time.Now()

	return m.conn.Exec(query, data.Uuid, data.Username, data.Password, data.Nickname, data.Email,
		data.Phone, data.Role, data.Remark, data.Status, now, now)
}

func (m *defaultUserModel) Update(data *User) error {
	query := fmt.Sprintf("update %s set username = ?, nickname = ?, email = ?, phone = ?, role = ?, remark = ?, status = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.Username, data.Nickname, data.Email, data.Phone, data.Role, data.Remark, data.Status, data.Id)
	return err
}

func (m *defaultUserModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

// 验证密码是否匹配
func (u *User) VerifyPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// 添加按条件查询用户的方法
func (m *defaultUserModel) FindByConditions(username, phone string, role int64, page, pageSize int64) ([]*User, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 构建条件
	conditions := []string{}
	args := []interface{}{}

	if username != "" {
		conditions = append(conditions, "username like ?")
		args = append(args, "%"+username+"%")
	}

	if phone != "" {
		conditions = append(conditions, "phone like ?")
		args = append(args, "%"+phone+"%")
	}

	if role > 0 {
		conditions = append(conditions, "role = ?")
		args = append(args, role)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询
	query := fmt.Sprintf("SELECT %s FROM %s%s ORDER BY update_time DESC LIMIT ?, ?", userFieldNames, m.table, whereClause)
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)

	var users []*User
	var count int64

	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&users, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return users, count, nil
}

func (m *defaultUserModel) UpdatePw(userId int64, newPw string) error {
	query := fmt.Sprintf("update %s set password = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, newPw, userId)
	return err
}
