package user

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	userDisplaySettingFieldNames  = strings.Join(userDisplaySettingRows, ",")
	userDisplaySettingRows        = []string{"id", "user_uuid", "module", "display_columns", "update_time", "create_time"}
	ErrUserDisplaySettingNotFound = errors.New("用户展示设置不存在")
)

type (
	UserDisplaySettingModel interface {
		FindOne(id int64) (*UserDisplaySetting, error)
		FindByUserUuidAndModule(userUuid, module string) (*UserDisplaySetting, error)
		Insert(data *UserDisplaySetting) (sql.Result, error)
		Update(data *UserDisplaySetting) error
		Delete(id int64) error
	}

	defaultUserDisplaySettingModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UserDisplaySetting struct {
		Id             int64     `db:"id"`
		UserUuid       string    `db:"user_uuid"`
		Module         string    `db:"module"`          // 模块，如 'song', 'cp', 'customer' 等
		DisplayColumns string    `db:"display_columns"` // JSON格式存储的展示列
		UpdateTime     time.Time `db:"update_time"`
		CreateTime     time.Time `db:"create_time"`
	}
)

func NewUserDisplaySettingModel(conn sqlx.SqlConn) UserDisplaySettingModel {
	return &defaultUserDisplaySettingModel{
		conn:  conn,
		table: "user_display_setting",
	}
}

func (m *defaultUserDisplaySettingModel) FindOne(id int64) (*UserDisplaySetting, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", userDisplaySettingFieldNames, m.table)
	var resp UserDisplaySetting
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserDisplaySettingNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserDisplaySettingModel) FindByUserUuidAndModule(userUuid, module string) (*UserDisplaySetting, error) {
	query := fmt.Sprintf("select %s from %s where user_uuid = ? and module = ? limit 1", userDisplaySettingFieldNames, m.table)
	var resp UserDisplaySetting
	err := m.conn.QueryRow(&resp, query, userUuid, module)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrUserDisplaySettingNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserDisplaySettingModel) Insert(data *UserDisplaySetting) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)",
		m.table, strings.Join(userDisplaySettingRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.UserUuid, data.Module, data.DisplayColumns, now, now)
}

func (m *defaultUserDisplaySettingModel) Update(data *UserDisplaySetting) error {
	query := fmt.Sprintf("update %s set display_columns = ?, update_time = ? where id = ?", m.table)
	now := time.Now()
	_, err := m.conn.Exec(query, data.DisplayColumns, now, data.Id)
	return err
}

func (m *defaultUserDisplaySettingModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}
