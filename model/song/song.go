package song

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"music/internal/types"
	"music/model/cp"
	"music/model/log"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	songFieldNames = strings.Join(songRows, ",")
	songRows       = []string{"id", "import_id", "type", "vendor_id", "song_code", "name", "singer",
		"vendor_song_id", "vendor_release_time", "song_path", "drm_path", "license_key", "poster_path",
		"lyric_path", "lyric_type", "pitch_type", "pitchs", "high_part", "high_part_type", "quality_level",
		"tag_ids", "duration", "close_time", "hot_num", "status", "update_time", "create_time"}
	ErrSongNotFound      = errors.New("歌曲不存在")
	ErrSongStatusInvalid = errors.New("歌曲已下架")
)

type (
	SongModel interface {
		FindOne(id int64) (*Song, error)
		Find(data *Song, closeTimeBegin, closeTimeEnd int64, createTimeBegin, createTimeEnd string, orderByCreateTime, orderByUpdateTime, orderByCloseTime string, page, pageSize int64) ([]*Song, int64, error)
		FindOneBySongCode(songCode int64) (*Song, error)
		FindOneBySongName(songName string) (*Song, error)
		FindByCpId(cpId, page, pageSize int64) ([]*Song, int64, error)
		FindByNameOrSongCode(name string, songCode int64) ([]*Song, error)
		FindAll(page, pageSize int64) ([]*Song, error)
		Insert(data *Song) (sql.Result, error)
		Update(data *Song) error
		UpdateStatus(id int64, status int) error
		ListedSongs(songCodes []int64, status int64) ([]*log.OperationLog, error)
		Delete(id int64) error
		FindSongCode(status int) ([]int64, error)
		FindByName(name string) ([]*Song, error)
		FindForHot(data *Song, withOutSong []int64, page, pageSize int64) ([]*Song, int64, error)
		BatchImport(songs []*Song) (successCount int, failedSongs map[int]string, err error)
		FindByCodes(songCodes []int64) ([]*Song, error)
	}

	defaultSongModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Song struct {
		Id                int64     `db:"id"`
		ImportId          int64     `db:"import_id"`
		Type              int64     `db:"type"`
		VendorId          int64     `db:"vendor_id"`
		SongCode          int64     `db:"song_code"`
		Name              string    `db:"name"`
		Singer            string    `db:"singer"`
		VendorSongId      string    `db:"vendor_song_id"`
		VendorReleaseTime string    `db:"vendor_release_time"`
		SongPath          string    `db:"song_path"`
		DrmPath           string    `db:"drm_path"`
		LicenseKey        string    `db:"license_key"`
		PosterPath        string    `db:"poster_path"`
		LyricPath         string    `db:"lyric_path"`
		LyricType         string    `db:"lyric_type"`
		PitchType         int64     `db:"pitch_type"`
		Pitchs            string    `db:"pitchs"`
		HighPart          string    `db:"high_part"`
		HighPartType      int64     `db:"high_part_type"`
		QualityLevel      string    `db:"quality_level"`
		TagIds            string    `db:"tag_ids"`
		Duration          int64     `db:"duration"`
		CloseTime         int64     `db:"close_time"`
		HotNum            int64     `db:"hot_num"`
		Status            int64     `db:"status"`
		UpdateTime        time.Time `db:"update_time"`
		CreateTime        time.Time `db:"create_time"`
	}
)

func NewSongModel(conn sqlx.SqlConn) SongModel {
	return &defaultSongModel{
		conn:  conn,
		table: "song",
	}
}

func (m *defaultSongModel) FindOne(id int64) (*Song, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", songFieldNames, m.table)
	var resp Song
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrSongNotFound
	default:
		return nil, err
	}
}

func (m *defaultSongModel) Find(data *Song, closeTimeBegin, closeTimeEnd int64, createTimeBegin, createTimeEnd string, orderByCreateTime, orderByUpdateTime, orderByCloseTime string, page, pageSize int64) ([]*Song, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// Build query conditions based on non-empty fields in data
	conditions := []string{}
	args := []interface{}{}

	if data.SongCode > 0 {
		conditions = append(conditions, "song_code = ?")
		args = append(args, data.SongCode)
	}
	if data.Name != "" {
		conditions = append(conditions, "name like ?")
		args = append(args, "%"+data.Name+"%")
	}
	if data.Singer != "" {
		conditions = append(conditions, "singer like ?")
		args = append(args, "%"+data.Singer+"%")
	}
	if data.VendorSongId != "" {
		conditions = append(conditions, "vendor_song_id like ?")
		args = append(args, "%"+data.VendorSongId+"%")
	}
	if data.VendorId > 0 {
		conditions = append(conditions, "vendor_id = ?")
		args = append(args, data.VendorId)
	}
	if data.Status >= 0 && data.Status != 99 {
		conditions = append(conditions, "status = ?")
		args = append(args, data.Status)
	}
	if data.Type > 0 {
		conditions = append(conditions, "type = ?")
		args = append(args, data.Type)
	}
	if data.HighPartType > 0 {
		conditions = append(conditions, "high_part_type = ?")
		args = append(args, data.HighPartType)
	}
	if data.TagIds != "" {
		conditions = append(conditions, "tag_ids like ?")
		args = append(args, "%"+data.TagIds+"%")
	}
	if data.LyricType != "" {
		conditions = append(conditions, "lyric_type = ?")
		args = append(args, data.LyricType)
	}
	if data.Pitchs != "" {
		conditions = append(conditions, "pitchs like ?")
		args = append(args, "%"+data.Pitchs+"%")
	}

	if closeTimeBegin != 0 && closeTimeEnd != 0 {
		conditions = append(conditions, " close_time between  ? and ? ")
		args = append(args, closeTimeBegin, closeTimeEnd)
	}

	if createTimeBegin != "" && createTimeEnd != "" {
		conditions = append(conditions, " create_time between  ? and ? ")
		args = append(args, fmt.Sprintf("%s %s", createTimeBegin, "00:00:00"), fmt.Sprintf("%s %s", createTimeEnd, "23:59:59"))
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = " create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = " create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = " update_time " + orderByUpdateTime
	}

	if orderByCloseTime != "" {
		orderfield = " close_time " + orderByCloseTime
	}

	// Query for results with pagination
	query := fmt.Sprintf("SELECT %s FROM %s%s ORDER BY %s, id DESC LIMIT ?, ?",
		songFieldNames, m.table, whereClause, orderfield)
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)

	var resp []*Song
	var count int64

	// Append pagination parameters
	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	// Get total count
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultSongModel) FindOneBySongCode(songCode int64) (*Song, error) {
	query := fmt.Sprintf("select %s from %s where song_code = ? limit 1", songFieldNames, m.table)
	var resp Song
	err := m.conn.QueryRow(&resp, query, songCode)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrSongNotFound
	default:
		return nil, err
	}
}

func (m *defaultSongModel) FindOneBySongName(songName string) (*Song, error) {
	query := fmt.Sprintf("select %s from %s where `name` = ? limit 1", songFieldNames, m.table)
	var resp Song
	err := m.conn.QueryRow(&resp, query, songName)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrSongNotFound
	default:
		return nil, err
	}
}

func (m *defaultSongModel) Insert(data *Song) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(songRows[1:], ","))
	now := time.Now()

	return m.conn.Exec(query, data.ImportId, data.Type, data.VendorId, data.SongCode, data.Name,
		data.Singer, data.VendorSongId, data.VendorReleaseTime, data.SongPath, data.DrmPath,
		data.LicenseKey, data.PosterPath, data.LyricPath, data.LyricType, data.PitchType,
		data.Pitchs, data.HighPart, data.HighPartType, data.QualityLevel, data.TagIds,
		data.Duration, data.CloseTime, data.HotNum, data.Status, now, now)
}

func (m *defaultSongModel) Update(data *Song) error {
	query := fmt.Sprintf(`update %s set import_id = ?, type = ?, vendor_id = ?, name = ?, singer = ?, 
		vendor_song_id = ?, vendor_release_time = ?, song_path = ?, drm_path = ?, license_key = ?, 
		poster_path = ?, lyric_path = ?, lyric_type = ?, pitch_type = ?, pitchs = ?, high_part = ?, 
		high_part_type = ?, quality_level = ?, tag_ids = ?, duration = ?, hot_num = ?, status = ? 
		where id = ?`, m.table)

	_, err := m.conn.Exec(query, data.ImportId, data.Type, data.VendorId, data.Name, data.Singer,
		data.VendorSongId, data.VendorReleaseTime, data.SongPath, data.DrmPath, data.LicenseKey,
		data.PosterPath, data.LyricPath, data.LyricType, data.PitchType, data.Pitchs, data.HighPart,
		data.HighPartType, data.QualityLevel, data.TagIds, data.Duration, data.HotNum, data.Status,
		data.Id)
	return err
}

func (m *defaultSongModel) UpdateStatus(id int64, status int) error {
	var closeTime int64 = 0
	if status == 0 {
		closeTime = time.Now().Unix() + 3600 // 下架时间为当前时间+1小时
	}
	query := fmt.Sprintf("update %s set status = ?, close_time = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, status, closeTime, id)
	return err
}

func (m *defaultSongModel) _ListedSongs(songCodes []int64, status int64) ([]*log.OperationLog, error) {
	logs := make([]*log.OperationLog, 0)
	err := m.conn.Transact(func(tx sqlx.Session) (err error) {
		for _, songCode := range songCodes {

			// 获取旧数据
			oldSong := &Song{}
			newSong := &Song{}
			oldSongQuery := fmt.Sprintf("select * from %s where song_code = ?", m.table)
			oldSongErr := tx.QueryRow(&oldSong, oldSongQuery, songCode)
			if oldSongErr != nil {
				return oldSongErr
			}

			if status == types.Down {
				// 判断 当前歌曲是否 存在close_time  如果存在 则不更新
				if oldSong.CloseTime != 0 && oldSong.Status == types.Down {
					continue
				}

				// 下架时间为当前时间+1小时
				closeTime := time.Now().Unix() + 3600

				// 更新歌曲状态
				query := fmt.Sprintf("update %s set status = ?, close_time = ? where song_code = ?", m.table)
				_, listedErr := tx.Exec(query, status, closeTime, songCode)
				if listedErr != nil {
					return listedErr
				}

			} else if status == types.Up {
				query := fmt.Sprintf("update %s set status = ? and close_time = 0 where song_code = ?", m.table)
				_, listedErr := tx.Exec(query, status, songCode)
				if listedErr != nil {
					return listedErr
				}
			}

			newSongQuery := fmt.Sprintf("select * from %s where song_code = ?", m.table)
			newSongErr := tx.QueryRow(&newSong, newSongQuery, songCode)
			if newSongErr != nil {
				return newSongErr
			}

			// 记录操作日志
			oldValue, _ := json.Marshal(oldSong)
			newValue, _ := json.Marshal(newSong)

			// 创建操作日志
			operationLog := &log.OperationLog{
				Module:     "song",
				Operation:  types.LogOperationListed,
				EntityId:   oldSong.Id,
				EntityName: oldSong.Name,
				OldValue:   string(oldValue),
				NewValue:   string(newValue),
			}

			logs = append(logs, operationLog)
		}
		return nil
	})

	return logs, err
}

func (m *defaultSongModel) ListedSongs(songCodes []int64, status int64) ([]*log.OperationLog, error) {
	logs := make([]*log.OperationLog, 0)

	for _, songCode := range songCodes {
		//queryFieldName := strings.Join(songRows[:len(songRows)-2], ",")

		// 获取旧数据
		oldSong := &Song{}
		newSong := &Song{}
		oldSong, err := m.FindOneBySongCode(songCode)
		if err != nil {
			return logs, err
		}

		if status == types.Down {
			// 判断 当前歌曲是否 存在close_time  如果存在 则不更新
			if oldSong.CloseTime != 0 && oldSong.Status == types.Down {
				continue
			}

			// 下架时间为当前时间+1小时
			closeTime := time.Now().UTC().Unix() + 3600

			// 更新歌曲状态
			query := fmt.Sprintf("update %s set status = ?, close_time = ? ,update_time = ? where song_code = ?", m.table)
			_, listedErr := m.conn.Exec(query, status, closeTime, time.Now().UTC(), songCode)
			if listedErr != nil {
				return logs, listedErr
			}

		} else if status == types.Up {
			query := fmt.Sprintf("update %s set status = ? , close_time = 0, create_time = ? , update_time = ?  where song_code = ?", m.table)
			_, listedErr := m.conn.Exec(query, status, time.Now().UTC(), time.Now().UTC(), songCode)
			if listedErr != nil {
				return logs, listedErr
			}
		}

		newSong, err = m.FindOneBySongCode(songCode)
		if err != nil {
			return logs, err
		}

		// 记录操作日志
		oldValue, _ := json.Marshal(oldSong)
		newValue, _ := json.Marshal(newSong)

		// 创建操作日志
		operationLog := &log.OperationLog{
			Module:     "song",
			Operation:  types.LogOperationListed,
			EntityId:   oldSong.Id,
			EntityName: oldSong.Name,
			OldValue:   string(oldValue),
			NewValue:   string(newValue),
		}

		logs = append(logs, operationLog)
	}

	return logs, nil
}

func (m *defaultSongModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultSongModel) FindByNameOrSongCode(name string, songCode int64) ([]*Song, error) {

	// Build query conditions based on non-empty fields in data
	conditions := []string{}
	args := []interface{}{}

	if songCode > 0 {
		conditions = append(conditions, "song_code = ?")
		args = append(args, songCode)
	}
	if name != "" {
		conditions = append(conditions, "name like ?")
		args = append(args, "%"+name+"%")
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	query := fmt.Sprintf("select %s from %s %s", songFieldNames, m.table, whereClause)
	var resp []*Song
	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, errors.New(types.ErrQuerySongInfo)
	}
	return resp, nil
}

func (m *defaultSongModel) FindSongCode(status int) ([]int64, error) {
	query := fmt.Sprintf("select song_code from %s where status = ?", m.table)
	var resp []*int64
	err := m.conn.QueryRows(&resp, query, status)
	if err != nil {
		return nil, errors.New(types.ErrQuerySongInfo)
	}

	songCodes := make([]int64, 0)
	for _, songCode := range resp {
		songCodes = append(songCodes, *songCode)
	}
	return songCodes, nil
}

func (m *defaultSongModel) FindByName(name string) ([]*Song, error) {
	query := fmt.Sprintf("select %s from %s where name = ?", songFieldNames, m.table)
	var resp []*Song
	err := m.conn.QueryRows(&resp, query, name)
	if err != nil {
		return nil, errors.New(types.ErrQuerySongInfo)
	}
	return resp, nil
}

func (m *defaultSongModel) FindForHot(data *Song, withOutSong []int64, page, pageSize int64) ([]*Song, int64, error) {
	var offset int64
	if page > 0 && pageSize > 0 {
		offset = (page - 1) * pageSize
	}

	// Build query conditions based on non-empty fields in data
	conditions := []string{}
	args := []interface{}{}

	if data.SongCode > 0 {
		conditions = append(conditions, "song_code = ?")
		args = append(args, data.SongCode)
	}
	if data.Name != "" {
		conditions = append(conditions, "name like ?")
		args = append(args, "%"+data.Name+"%")
	}
	if data.Singer != "" {
		conditions = append(conditions, "singer like ?")
		args = append(args, "%"+data.Singer+"%")
	}
	if data.VendorId > 0 {
		conditions = append(conditions, "vendor_id = ?")
		args = append(args, data.VendorId)
	}
	if data.Status > 0 {
		conditions = append(conditions, "status = ?")
		args = append(args, data.Status)
	}
	if data.Type > 0 {
		conditions = append(conditions, "type = ?")
		args = append(args, data.Type)
	}
	if data.HighPartType > 0 {
		conditions = append(conditions, "high_part_type = ?")
		args = append(args, data.HighPartType)
	}
	if data.TagIds != "" {
		conditions = append(conditions, "tag_ids like ?")
		args = append(args, "%"+data.TagIds+"%")
	}
	if data.LyricType != "" {
		conditions = append(conditions, "lyric_type = ?")
	}
	if data.Pitchs != "" {
		conditions = append(conditions, "pitchs like ?")
		args = append(args, "%"+data.Pitchs+"%")
	}

	if len(withOutSong) > 0 {
		conditions = append(conditions, "song_code NOT IN (?)")
		args = append(args, withOutSong)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	queryArgs := args

	limit := ""

	if offset > 0 {
		limit = " LIMIT ?, ?"
		queryArgs = append(args, offset, pageSize)
	}

	// Query for results with pagination
	query := fmt.Sprintf("SELECT %s FROM %s%s ORDER BY update_time DESC, id DESC %s",
		songFieldNames, m.table, whereClause, limit)
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)

	var resp []*Song
	var count int64

	// Append pagination parameters
	if offset > 0 {
		queryArgs = append(args, offset, pageSize)
	}
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	// Get total count
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultSongModel) FindByCpId(cpId, page, pageSize int64) ([]*Song, int64, error) {

	args := []interface{}{}

	args = append(args, cpId)

	limitField := ""

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		args = append(args, offset, pageSize)
		limitField = " LIMIT ?, ?"
	}

	query := fmt.Sprintf("select %s from %s where vendor_id = ? %s", songFieldNames, m.table, limitField)
	var resp []*Song
	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, 0, err
	}

	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s where vendor_id = ?", m.table)
	var count int64
	err = m.conn.QueryRow(&count, countQuery, cpId)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

// BatchImport 批量导入歌曲数据
// 返回成功导入的数量、失败记录及可能的错误
func (m *defaultSongModel) BatchImport(songs []*Song) (successCount int, failedSongs map[int]string, err error) {
	failedSongs = make(map[int]string)

	for i, song := range songs {
		// 检查必要字段
		if song.SongCode <= 0 {
			// 当前时间时间戳 去除 首位1 末尾 加 0
			timestamp := time.Now().UnixMicro()
			// 转为字符串
			timestampStr := strconv.FormatInt(timestamp, 10)

			// 如果首位是 1，则去除
			if len(timestampStr) > 0 && timestampStr[0] == '1' {
				timestampStr = timestampStr[1:]
			}

			// 末尾添加 0
			timestampStr = timestampStr + "0"
			// 如果需要转回整数
			code, _ := strconv.ParseInt(timestampStr, 10, 64)

			song.SongCode = code
		}

		if song.Name == "" {
			failedSongs[i] = "歌曲名称不能为空"
			continue
		}

		// 检查歌曲编码是否已存在
		existingSong, err := m.FindOneBySongCode(song.SongCode)
		if err == nil && existingSong != nil {
			failedSongs[i] = fmt.Sprintf("歌曲编号 %d 已存在", song.SongCode)
			continue
		} else if err != nil && !errors.Is(err, ErrSongNotFound) {
			failedSongs[i] = fmt.Sprintf("检查歌曲编号时出错: %s", err.Error())
			continue
		}

		// 检查歌曲编码是否已存在
		// 构建 版权方句柄
		cpModel := cp.NewCpModel(m.conn)

		existingCp, err := cpModel.FindOne(song.VendorId)
		if err != nil && !errors.Is(err, ErrSongNotFound) {
			failedSongs[i] = fmt.Sprintf("检查版权方时出错: %s", err.Error())
			continue
		} else if err == nil && existingCp == nil {
			failedSongs[i] = fmt.Sprintf("检查版权方时出错: %s", "版权方不存在")
			continue
		}

		// 检查歌曲名是否已存在
		/*	existingSong, err = m.FindOneBySongName(song.Name)
			if err == nil && existingSong != nil {
				failedSongs[i] = fmt.Sprintf("歌曲名 %s 已存在", song.Name)
				continue
			} else if err != nil && !errors.Is(err, ErrSongNotFound) {
				// 查询出错但不是"未找到"错误
				failedSongs[i] = fmt.Sprintf("检查歌曲名时出错: %s", err.Error())
				continue
			}*/

		// 设置默认值
		if song.Status != types.Up && song.Status != types.Down {
			song.Status = types.Up
		}

		// 设置创建和更新时间
		//song.CreateTime = time.Now()
		song.UpdateTime = time.Now()

		// 设置 import_id
		if song.ImportId == 0 {
			song.ImportId, _ = strconv.ParseInt(time.Now().Format("2006010215"), 10, 64)
		}

		// 插入数据
		_, err = m.Insert(song)
		if err != nil {
			failedSongs[i] = fmt.Sprintf("数据库插入失败: %s", err.Error())
			continue
		}

		successCount++

		// sleep 1 nano second
		time.Sleep(1 * time.Nanosecond)
	}

	return successCount, failedSongs, nil
}

func (m *defaultSongModel) FindAll(page, pageSize int64) ([]*Song, error) {
	var limit string
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		limit = fmt.Sprintf(" limit %d, %d", offset, pageSize)
	} else {
		limit = ""
	}

	query := fmt.Sprintf("select %s from %s order by id %s", songFieldNames, m.table, limit)
	var resp []*Song
	err := m.conn.QueryRows(&resp, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// FindByCodes 根据歌曲编码列表查询歌曲
func (m *defaultSongModel) FindByCodes(songCodes []int64) ([]*Song, error) {
	if len(songCodes) == 0 {
		return []*Song{}, nil
	}

	// 构建IN查询条件
	placeholders := make([]string, len(songCodes))
	args := make([]interface{}, len(songCodes))
	for i, code := range songCodes {
		placeholders[i] = "?"
		args[i] = code
	}

	query := fmt.Sprintf("SELECT %s FROM %s WHERE song_code IN (%s)", songFieldNames, m.table, strings.Join(placeholders, ","))
	var resp []*Song
	err := m.conn.QueryRows(&resp, query, args...)
	return resp, err
}
