package disposition

import (
	"database/sql"
	"errors"
	"fmt"
	"music/internal/middleware"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	dispositionDetailFieldNames = strings.Join(dispositionDetailRows, ",")
	dispositionDetailRows       = []string{"id", "order_code", "vid", "resource_types", "cp_id", "billing_type",
		"end_time", "remark", "status", "update_time", "create_time"}
	ErrDispositionDetailNotFound = errors.New("配置明细不存在")
)

type (
	DispositionDetailModel interface {
		FindOne(id int64) (*DispositionDetail, error)
		FindByOrderCode(orderCode string) ([]*DispositionDetail, error)
		FindValidByVid(vid int64) ([]*DispositionDetailWithCp, error)
		Insert(data *DispositionDetail) (sql.Result, error)
		Update(data *DispositionDetail) error
		Delete(id int64) error
		DeleteByOrderCode(orderCode string) error
		CloseOrOpenByOrderCode(orderCode string, status int64) error
		CloseOrOpen(id, status int64) error
		CloseOrOpenByIds(id []int64, status int64) error
		Find(orderCode string, cpId, status int64, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*DispositionDetail, int64, error)
	}

	defaultDispositionDetailModel struct {
		conn  sqlx.SqlConn
		table string
	}

	DispositionDetail struct {
		Id            int64     `db:"id"`
		OrderCode     string    `db:"order_code"`
		Vid           int64     `db:"vid,default=0"`
		ResourceTypes string    `db:"resource_types"`
		CpId          int64     `db:"cp_id,default=1"`
		BillingType   int64     `db:"billing_type,default=2"`
		EndTime       int64     `db:"end_time"`
		Remark        string    `db:"remark"`
		Status        int64     `db:"status,default=1"`
		UpdateTime    time.Time `db:"update_time"`
		CreateTime    time.Time `db:"create_time"`
	}

	DispositionDetailWithCp struct {
		DispositionDetail
		CpName string `db:"cp_name"`
	}
)

func NewDispositionDetailModel(conn sqlx.SqlConn) DispositionDetailModel {
	return &defaultDispositionDetailModel{
		conn:  conn,
		table: "disposition_detail",
	}
}

func (m *defaultDispositionDetailModel) FindOne(id int64) (*DispositionDetail, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", dispositionDetailFieldNames, m.table)
	var resp DispositionDetail
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultDispositionDetailModel) FindByOrderCode(orderCode string) ([]*DispositionDetail, error) {
	query := fmt.Sprintf("select %s from %s where order_code = ?", dispositionDetailFieldNames, m.table)
	var resp []*DispositionDetail
	err := m.conn.QueryRows(&resp, query, orderCode)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultDispositionDetailModel) FindValidByVid(vid int64) ([]*DispositionDetailWithCp, error) {
	now := time.Now().Unix()
	query := fmt.Sprintf(`select d.%s, c.name as cp_name 
		from %s d 
		left join cp c on d.cp_id = c.id 
		where d.vid = ? and d.status = 1 and (d.end_time = 0 or d.end_time > ?) 
		order by d.id desc`,
		dispositionDetailFieldNames, m.table)
	var resp []*DispositionDetailWithCp
	err := m.conn.QueryRows(&resp, query, vid, now)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultDispositionDetailModel) Insert(data *DispositionDetail) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(dispositionDetailRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.OrderCode, data.Vid, data.ResourceTypes, data.CpId,
		data.BillingType, data.EndTime, data.Remark, data.Status, now, now)
}

func (m *defaultDispositionDetailModel) Update(data *DispositionDetail) error {
	query := fmt.Sprintf("update %s set resource_types = ?, cp_id = ?, billing_type = ?, end_time = ?, remark = ? , vid = ? where id = ?",
		m.table)
	_, err := m.conn.Exec(query, data.ResourceTypes, data.CpId, data.BillingType,
		data.EndTime, data.Remark, data.Vid, data.Id)
	return err
}

func (m *defaultDispositionDetailModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultDispositionDetailModel) DeleteByOrderCode(orderCode string) error {
	query := fmt.Sprintf("delete from %s where order_code = ?", m.table)
	_, err := m.conn.Exec(query, orderCode)
	return err
}

func (m *defaultDispositionDetailModel) CloseOrOpenByOrderCode(orderCode string, status int64) error {
	query := fmt.Sprintf("update %s set status = ? ,update_time = ? where order_code = ? ", m.table)
	// Convert orderCode slice to a comma-separated string
	_, err := m.conn.Exec(query, status, time.Now(), orderCode)
	return err
}

func (m *defaultDispositionDetailModel) CloseOrOpen(id, status int64) error {
	query := fmt.Sprintf("update %s set status = ?, end_time = ?  ,update_time = ? where id = ? ", m.table)
	// Convert orderCode slice to a comma-separated string
	endTime, _ := middleware.DateToUnix(time.Now().Format(time.DateOnly))
	_, err := m.conn.Exec(query, status, endTime, time.Now(), id)
	return err
}

func (m *defaultDispositionDetailModel) CloseOrOpenByIds(ids []int64, status int64) error {
	if len(ids) == 0 {
		return nil
	}

	conditions := []string{}
	args := make([]string, 0)

	if len(ids) > 0 {

		for _, id := range ids {
			args = append(args, strconv.FormatInt(id, 10))
		}

		conditions = append(conditions, fmt.Sprintf("CAST(id AS CHAR) in (%s)",
			strings.Join(args, ",")))
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	endTime, _ := middleware.DateToUnix(time.Now().Format(time.DateOnly))

	query := fmt.Sprintf("update %s set status = ?, end_time = ? ,update_time = ? %s ", m.table, whereClause)
	// Convert orderCode slice to a comma-separated string
	_, err := m.conn.Exec(query, status, endTime, time.Now())
	return err
}

func (m *defaultDispositionDetailModel) Find(orderCode string, cpId, status int64, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*DispositionDetail, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	conditions := []string{}
	args := []interface{}{}

	if orderCode != "" {
		conditions = append(conditions, "order_code like ?")
		args = append(args, "%"+orderCode+"%")
	}
	if cpId > 0 {
		conditions = append(conditions, "cp_id = ?")
		args = append(args, cpId)
	}
	if status >= 0 && status != 99 {
		conditions = append(conditions, "status = ?")
		args = append(args, status)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " where " + strings.Join(conditions, " and ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = " create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = " create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = " update_time " + orderByUpdateTime
	}

	query := fmt.Sprintf("select %s from %s %s order by %s limit ?, ?", dispositionDetailFieldNames, m.table, whereClause, orderfield)
	var resp []*DispositionDetail

	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	countQuery := fmt.Sprintf("select count(*) from %s %s", m.table, whereClause)
	var count int64
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}
