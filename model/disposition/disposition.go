package disposition

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	dispositionFieldNames  = strings.Join(dispositionRows, ",")
	dispositionRows        = []string{"id", "order_code", "vid", "remark", "status", "update_time", "create_time"}
	ErrDispositionNotFound = errors.New("配置不存在")
)

type (
	DispositionModel interface {
		FindOne(id int64) (*Disposition, error)
		FindOneByOrderCode(orderCode string) (*Disposition, error)
		FindByVid(vid int64, page, pageSize int64) ([]*Disposition, error)
		CountByVid(vid int64) (int64, error)
		Insert(data *Disposition) (sql.Result, error)
		Update(data *Disposition) error
		Delete(id int64) error
		Find(orderCode string, vid, status int64, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*Disposition, int64, error)
		CloseOrOpen(id, status int64) error
	}

	defaultDispositionModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Disposition struct {
		Id         int64     `db:"id"`
		OrderCode  string    `db:"order_code"`
		Vid        int64     `db:"vid"`
		Remark     string    `db:"remark"`
		Status     int64     `db:"status"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
	}
)

func NewDispositionModel(conn sqlx.SqlConn) DispositionModel {
	return &defaultDispositionModel{
		conn:  conn,
		table: "disposition",
	}
}

func (m *defaultDispositionModel) FindOne(id int64) (*Disposition, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", dispositionFieldNames, m.table)
	var resp Disposition
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrDispositionNotFound
	default:
		return nil, err
	}
}

func (m *defaultDispositionModel) FindOneByOrderCode(orderCode string) (*Disposition, error) {
	query := fmt.Sprintf("select %s from %s where order_code = ? limit 1", dispositionFieldNames, m.table)
	var resp Disposition
	err := m.conn.QueryRow(&resp, query, orderCode)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultDispositionModel) FindByVid(vid int64, page, pageSize int64) ([]*Disposition, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where vid = ? order by id desc limit ?, ?", dispositionFieldNames, m.table)
	var resp []*Disposition
	err := m.conn.QueryRows(&resp, query, vid, offset, pageSize)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultDispositionModel) CountByVid(vid int64) (int64, error) {
	query := fmt.Sprintf("select count(*) from %s where vid = ?", m.table)
	var count int64
	err := m.conn.QueryRow(&count, query, vid)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultDispositionModel) Insert(data *Disposition) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(dispositionRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.OrderCode, data.Vid, data.Remark, data.Status, now, now)
}

func (m *defaultDispositionModel) Update(data *Disposition) error {
	query := fmt.Sprintf("update %s set order_code =?, vid = ?,  remark = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.OrderCode, data.Vid, data.Remark, data.Id)
	return err
}

func (m *defaultDispositionModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultDispositionModel) Find(orderCode string, vid, status int64, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*Disposition, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	conditions := []string{}
	args := []interface{}{}
	if orderCode != "" {
		conditions = append(conditions, " order_code  like ?")
		args = append(args, "%"+orderCode+"%")
	}
	if vid != 0 {
		conditions = append(conditions, " vid = ?")
		args = append(args, vid)
	}
	if status >= 0 && status != 99 {
		conditions = append(conditions, " status = ?")
		args = append(args, status)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = " create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = " create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = " update_time " + orderByUpdateTime
	}

	query := fmt.Sprintf(
		"select %s from %s%s order by %s limit ?, ?", dispositionFieldNames, m.table, whereClause, orderfield)
	var resp []*Disposition
	var count int64
	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)
	// Get total count
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}
	return resp, count, nil
}

func (m *defaultDispositionModel) CloseOrOpen(id, status int64) error {
	query := fmt.Sprintf("update %s set status = ? ,update_time = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, status, time.Now(), id)
	return err
}
