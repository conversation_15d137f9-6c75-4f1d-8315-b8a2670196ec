package cp

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	cpFieldNames       = strings.Join(cpRows, ",")
	cpRows             = []string{"id", "name", "remark", "status", "end_time", "update_time", "create_time"}
	ErrCpNotFound      = errors.New("版权方不存在")
	ErrCpStatusInvalid = errors.New("版权方已被禁用")
)

type (
	CpModel interface {
		FindOne(id int64) (*Cp, error)
		FindAll(page, pageSize int64) ([]*Cp, error)
		Count() (int64, error)
		Insert(data *Cp) (sql.Result, error)
		Update(data *Cp) error
		Delete(id int64) error
		UpdateStatus(id, status, endTime int64) error
		Find(name string, beginEndTime, endEndTime, status int64, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*Cp, int64, error)
		FindOneByName(name string) (*Cp, error)
		FindByIds(ids []int64) ([]*Cp, error)
	}

	defaultCpModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Cp struct {
		Id         int64     `db:"id"`
		Name       string    `db:"name"`
		Remark     string    `db:"remark"`
		Status     int64     `db:"status"`
		EndTime    int64     `db:"end_time"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
	}
)

func NewCpModel(conn sqlx.SqlConn) CpModel {
	return &defaultCpModel{
		conn:  conn,
		table: "cp",
	}
}

func (m *defaultCpModel) FindOne(id int64) (*Cp, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", cpFieldNames, m.table)
	var resp Cp
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrCpNotFound
	default:
		return nil, err
	}
}

func (m *defaultCpModel) FindAll(page, pageSize int64) ([]*Cp, error) {
	var limit string
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		limit = fmt.Sprintf(" limit %d, %d", offset, pageSize)
	} else {
		limit = ""
	}

	query := fmt.Sprintf("select %s from %s order by id %s", cpFieldNames, m.table, limit)
	var resp []*Cp
	err := m.conn.QueryRows(&resp, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultCpModel) Count() (int64, error) {
	query := fmt.Sprintf("select count(*) from %s", m.table)
	var count int64
	err := m.conn.QueryRow(&count, query)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultCpModel) Insert(data *Cp) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(cpRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.Name, data.Remark, data.Status, data.EndTime, now, now)
}

func (m *defaultCpModel) Update(data *Cp) error {
	query := fmt.Sprintf("update %s set name = ?, remark = ?, status = ?, end_time = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.Name, data.Remark, data.Status, data.EndTime, data.Id)
	return err
}

func (m *defaultCpModel) UpdateStatus(id, status, endTime int64) error {
	query := fmt.Sprintf("update %s set status = ?, end_time = ?  where id = ?", m.table)
	_, err := m.conn.Exec(query, status, endTime, id)
	return err
}

func (m *defaultCpModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultCpModel) Find(name string, beginEndTime, endEndTime, status int64, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*Cp, int64, error) {

	offset := (page - 1) * pageSize

	var conditions []string
	var args []interface{}

	if name != "" {
		conditions = append(conditions, "name like ?")
		args = append(args, "%"+name+"%")
	}
	if beginEndTime > 0 {
		conditions = append(conditions, "end_time >= ?")
		args = append(args, beginEndTime)
	}
	if endEndTime > 0 {
		conditions = append(conditions, "end_time <= ?")
		args = append(args, endEndTime)
	}
	if status >= 0 && status != 99 {
		conditions = append(conditions, "status = ?")
		args = append(args, status)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = " create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = " create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = " update_time " + orderByUpdateTime
	}

	query := fmt.Sprintf("SELECT %s FROM %s%s ORDER BY %s, id DESC LIMIT ?, ?",
		cpFieldNames, m.table, whereClause, orderfield)
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)

	var resp []*Cp
	var count int64

	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultCpModel) FindOneByName(name string) (*Cp, error) {
	query := fmt.Sprintf("select %s from %s where name = ? limit 1", cpFieldNames, m.table)
	var resp Cp
	err := m.conn.QueryRow(&resp, query, name)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultCpModel) FindByIds(ids []int64) ([]*Cp, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	// 构建 IN 查询的占位符
	placeholders := make([]string, len(ids))
	args := make([]interface{}, len(ids))
	for i, id := range ids {
		placeholders[i] = "?"
		args[i] = id
	}

	query := fmt.Sprintf("SELECT %s FROM %s WHERE id IN (%s)",
		cpFieldNames,
		m.table,
		strings.Join(placeholders, ","))

	var resp []*Cp
	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
