-- 复合查询索引
ALTER TABLE song_play_record ADD INDEX idx_play_type_vid_lts (play_type, vid, lts);
ALTER TABLE song_play_record ADD INDEX idx_play_type_song_code_lts (play_type, song_code, lts);


ALTER TABLE song_play_record ADD INDEX idx_play_type_vid_start_time (play_type, vid, start_time);
ALTER TABLE song_play_record ADD INDEX idx_play_type_song_code_start_time (play_type, song_code, start_time);

-- 分组查询索引
ALTER TABLE song_play_record ADD INDEX idx_lts_play_type (lts, play_type);
ALTER TABLE song_play_record ADD INDEX idx_cp_id_lts (cp_id, lts);

ALTER TABLE song_play_record ADD INDEX idx_start_time_play_type (start_time, play_type);
ALTER TABLE song_play_record ADD INDEX idx_cp_id_start_time (cp_id, start_time);



-- 客户分组查询索引
ALTER TABLE song_play_record ADD INDEX idx_lts_play_type_vid (lts, play_type, vid);
ALTER TABLE song_play_record ADD INDEX idx_lts_play_type_use_type (lts, play_type, use_type);

ALTER TABLE song_play_record ADD INDEX idx_start_time_play_type_vid (start_time, play_type, vid);
ALTER TABLE song_play_record ADD INDEX idx_start_time_play_type_use_type (start_time, play_type, use_type);

-- 详细记录查询索引
ALTER TABLE song_play_record ADD INDEX idx_lts_vid_play_type (lts, vid, play_type);
ALTER TABLE song_play_record ADD INDEX idx_lts_vid_scene_type (lts, vid, scene_type);

ALTER TABLE song_play_record ADD INDEX idx_start_time_vid_play_type (start_time, vid, play_type);
ALTER TABLE song_play_record ADD INDEX idx_start_time_vid_scene_type (start_time, vid, scene_type);