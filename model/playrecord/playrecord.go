package playrecord

import (
	"database/sql"
	"errors"
	"fmt"
	"music/internal/middleware"
	"music/internal/types"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	playRecordFieldNames = strings.Join(playRecordRows, ",")
	playRecordRows       = []string{"id", "lts", "vid", "unique_id", "ifnull(request_ip,\"\")", "cp_id", "song_code", "song_duration",
		"play_duration", "start_time", "end_time", "playback_pos", "play_num", "scene_type", "play_type", "free_type",
		"record_type", "use_type", "ifnull(uid,\"\")", "ifnull(channel_id,\"\")", "push_status", "ifnull(push_time,0)", "create_time"}

	insertPlayRecordFieldNames = strings.Join(insertPlayRecordRows, ",")
	insertPlayRecordRows       = []string{"id", "lts", "vid", "unique_id", "request_ip", "cp_id", "song_code", "song_duration",
		"play_duration", "start_time", "end_time", "playback_pos", "play_num", "scene_type", "play_type", "free_type",
		"record_type", "use_type", "uid", "channel_id", "push_status", "push_time", "create_time"}

	sumPlayRecordCustomerFieldNames = strings.Join(sumPlayRecordCustomerRows, ",")
	sumPlayRecordCustomerRows       = []string{"vid", "ANY_VALUE(use_type) as use_type", "ANY_VALUE(play_type) as play_type", "sum(play_num) as play_num_total", "sum(play_duration) as play_duration_total"}

	sumPlayRecordSongFieldNames = strings.Join(sumPlayRecordSongRows, ",")
	sumPlayRecordSongRows       = []string{"song_code", "ANY_VALUE(cp_id) as cp_id", "ANY_VALUE(play_duration) as play_duration", "ANY_VALUE(play_type) as play_type", "sum(play_num) as play_num_total", "sum(play_duration) as play_duration_total"}

	playRecordDetailFieldNames = strings.Join(playRecordDetailRows, " , ")
	playRecordDetailRows       = []string{"DATE_FORMAT(FROM_UNIXTIME(start_time), '%Y-%m-%d %H:%i:%S') as lts_time", "play_type", "song_code", "song_duration", "cp_id",
		"vid", "use_type", "play_duration", "start_time", "end_time", "playback_pos", "play_num",
		"scene_type", "record_type", "free_type"}

	ErrPlayRecordNotFound = errors.New("播放记录不存在")
	ErrEmptyLtsTime       = errors.New("未传入正确的播放时间")
	ErrEmptyPlayType      = errors.New("未传入正确的用户类型")
)

type (
	PlayRecordModel interface {
		Insert(data *PlayRecord) (sql.Result, error)
		FindOne(id int64) (*PlayRecord, error)
		FindOneByUniqueId(uniqueId string) (*PlayRecord, error)
		FindByVid(vid int64, page, pageSize int64) ([]*PlayRecord, int64, error)
		FindBySongCode(songCode int64, page, pageSize int64) ([]*PlayRecord, int64, error)
		FindByPushStatus(pushStatus int, limit int64) ([]*PlayRecord, error)
		UpdatePushStatus(id int64, pushStatus int, pushTime int64) error
		BatchUpdatePushStatus(ids []int64, pushStatus int, pushTime int64) error
		Count(condition map[string]interface{}) (int64, error)
		FindByConditions(conditions map[string]interface{}, dateRange *DateRange, page, pageSize int64) ([]*PlayRecord, int64, error)

		// FindByPlayTypeGroupByLts 歌曲调用量
		FindByPlayTypeGroupByLts(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error)

		// FindGroupBySong 歌曲分组
		FindGroupBySong(ltsTime string, playType int, songCode []int64, page, pageSize int64) ([]*SumPlaySongRecord, int64, error)
		FindDetailGroupBySong(ltsTime string, songCode []int64, vid []int64, req *types.SumPlayRecordDetailByVidReq) ([]*PlayRecordDetail, int64, error)

		// FindGroupByCustomer 版权方分组
		FindGroupByCustomer(ltsTime string, playType int, vid []int64, useType, page, pageSize int64) ([]*SumPlayCustomerRecord, int64, error)
		FindDetailGroupByCustomer(ltsTime string, vid []int64, playType, useType, sceneType, freeType, page, pageSize int64) ([]*PlayRecordDetail, int64, error)

		// v2
		FindByPlayTypeGroupByLtsV2(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error)

		FindGroupBySongV2(ltsTime string, playType int, songCode []int64, page, pageSize int64) ([]*SumPlaySongRecord, int64, error)
		FindDetailGroupBySongV2(ltsTime string, songCode []int64, vid []int64, req *types.SumPlayRecordDetailByVidReq) ([]*PlayRecordDetail, int64, error)

		FindGroupByCustomerV2(ltsTime string, playType int, vid []int64, useType, page, pageSize int64) ([]*SumPlayCustomerRecord, int64, error)
		FindDetailGroupByCustomerV2(ltsTime string, vid []int64, playType, useType, sceneType, freeType, page, pageSize int64) ([]*PlayRecordDetail, int64, error)

		// v3
		FindByPlayTypeGroupByLtsV3(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error)

		// v4
		FindByPlayTypeGroupByLtsV4(ltsTimeBegin, ltsTimeEnd int64, playType int, vid []int64, useType int64, songcodes []int64, page, pageSize int64) ([]*SumPlayRecordLts, int64, error)

		// 添加批量导入方法
		BatchImport(records []*PlayRecord) (successCount int, failedRecords map[int]string, err error)
		BatchImportGoroutineWithTx(records []*PlayRecord) (successCount int, failedRecords map[int]string, err error)
		// 获取 最新的播放记录
		GetLatestPlayRecord(playType int) (*PlayRecord, error)

		// 获取 播放量  以 客户端分组
		FindByPlayTypeGroupByLtsRangeForExport(ltsTimeBegin, ltsTimeEnd int64, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error)
		FindByPlayTypeGroupByLtsDatesForExport(ltsTimeBegin, ltsTimeEnd int64, dates []string, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error)
		FindByPlayTypeGroupByLtsDatesForExportV2(ltsTimeBegin, ltsTimeEnd int64, dates []string, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error)

		FindByPlayTypeGroupByStartTimeRangeForExport(ltsTimeBegin, ltsTimeEnd int64, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error)
		FindByPlayTypeGroupByStartTimeDatesForExportV2(ltsTimeBegin, ltsTimeEnd int64, dates []string, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error)
		FindByPlayTypeGroupByStartTimeDatesForExportV3(ltsTimeBegin, ltsTimeEnd int64, dates []string, playType int, vid []int64, useType int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error)

		FindSongCodeByVid(ltsTime string, playType int, vid int64, page, pageSize int64) ([]int64, int64, error)

		GetConn() sqlx.SqlConn // 添加获取连接的方法
	}

	// 日期范围结构
	DateRange struct {
		StartDate int64
		EndDate   int64
	}

	defaultPlayRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	PlayRecord struct {
		Id           int64     `db:"id"`
		Lts          int64     `db:"lts"`
		Vid          int64     `db:"vid"`
		UniqueId     string    `db:"unique_id"`
		RequestIp    string    `db:"request_ip"`
		CpId         int64     `db:"cp_id"`
		SongCode     int64     `db:"song_code"`
		SongDuration int       `db:"song_duration"`
		PlayDuration int       `db:"play_duration"`
		StartTime    int64     `db:"start_time"`
		EndTime      int64     `db:"end_time"`
		PlaybackPos  int       `db:"playback_pos"`
		PlayNum      int       `db:"play_num"`
		SceneType    int       `db:"scene_type"`
		PlayType     int       `db:"play_type"`
		FreeType     int       `db:"free_type"`
		RecordType   int       `db:"record_type"`
		UseType      int       `db:"use_type"`
		Uid          string    `db:"uid"`
		ChannelId    string    `db:"channel_id"`
		PushStatus   int       `db:"push_status"`
		PushTime     int64     `db:"push_time"`
		CreateTime   time.Time `db:"create_time"`
	}

	// SumPlayRecord
	SumPlayRecord struct {
		// 日期
		LtsTime  string `db:"lts_time"`
		PlayType int64  `db:"play_type"`

		// 歌曲信息
		SongCode     int64  `db:"song_code"`
		SongName     string `db:"song_name"`
		SongDuration int64  `db:"song_duration"`
		CpId         int64  `db:"cp_id"`
		CpName       string `db:"cp_name"`
		PlayNum      int64  `db:"play_num"`

		// 客户信息
		Vid          int64  `db:"vid"`
		CustomerName string `db:"customer_name"`
		UseType      int64  `db:"use_type"`

		// 统计数据
		PlayDurationTotal int64 `db:"play_duration_total"`
		PlayNumTotal      int64 `db:"play_num_total"`
	}

	SumPlayCustomerRecord struct {
		// 客户信息
		Vid int64 `db:"vid"`

		UseType int64 `db:"use_type"`

		PlayType int64 `db:"play_type"`

		// 统计数据
		PlayDurationTotal int64 `db:"play_duration_total"`
		PlayNumTotal      int64 `db:"play_num_total"`
	}

	SumPlaySongRecord struct {
		PlayType int64 `db:"play_type"`

		// 歌曲信息
		SongCode     int64 `db:"song_code"`
		SongDuration int64 `db:"song_duration"`
		CpId         int64 `db:"cp_id"`

		// 统计数据
		PlayDurationTotal int64 `db:"play_duration_total"`
		PlayNumTotal      int64 `db:"play_num_total"`
	}

	SumPlayRecordLts struct {
		// 日期
		LtsTime  string `db:"lts_time"`
		PlayType int64  `db:"play_type"`

		// 统计数据
		PlayDurationTotal int64 `db:"play_duration_total"`
		PlayNumTotal      int64 `db:"play_num_total"`
	}

	PlayRecordDetail struct {
		// 日期
		LtsTime  string `db:"lts_time"`
		PlayType int64  `db:"play_type"`

		// 歌曲信息
		SongCode     int64 `db:"song_code"`
		SongDuration int64 `db:"song_duration"`
		CpId         int64 `db:"cp_id"`

		// 客户信息
		Vid     int64 `db:"vid"`
		UseType int64 `db:"use_type"`

		// 播放信息
		PlayDuration int64 `db:"play_duration"`
		StartTime    int64 `db:"start_time"`
		EndTime      int64 `db:"end_time"`
		PlayBackPos  int64 `db:"playback_pos"`
		PlayNum      int64 `db:"play_num"`
		SceneType    int64 `db:"scene_type"`
		RecordType   int64 `db:"record_type"`
		FreeType     int64 `db:"free_type"`
	}

	SumPlayRecordLtsForExport struct {
		// 日期
		LtsTime  string `db:"lts_time"`
		PlayType int64  `db:"play_type"`
		Vid      int64  `db:"vid"`
		// CustomerName string `db:"customer_name"`
		// 统计数据
		PlayDurationTotal int64 `db:"play_duration_total"`
		PlayNumTotal      int64 `db:"play_num_total"`
	}
)

func NewPlayRecordModel(conn sqlx.SqlConn) PlayRecordModel {
	return &defaultPlayRecordModel{
		conn:  conn,
		table: "song_play_record",
	}
}

// GetConn 获取数据库连接
func (m *defaultPlayRecordModel) GetConn() sqlx.SqlConn {
	return m.conn
}

func (m *defaultPlayRecordModel) Insert(data *PlayRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(insertPlayRecordRows[1:len(insertPlayRecordRows)-1], ","))

	return m.conn.Exec(query, data.Lts, data.Vid, data.UniqueId, data.RequestIp, data.CpId, data.SongCode,
		data.SongDuration, data.PlayDuration, data.StartTime, data.EndTime, data.PlaybackPos, data.PlayNum,
		data.SceneType, data.PlayType, data.FreeType, data.RecordType, data.UseType, data.Uid, data.ChannelId,
		data.PushStatus, data.PushTime)
}

func (m *defaultPlayRecordModel) FindOne(id int64) (*PlayRecord, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", playRecordFieldNames, m.table)
	var resp PlayRecord
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrPlayRecordNotFound
	default:
		return nil, err
	}
}

func (m *defaultPlayRecordModel) FindOneByUniqueId(uniqueId string) (*PlayRecord, error) {
	query := fmt.Sprintf("select %s from %s where unique_id = ? limit 1", playRecordFieldNames, m.table)
	var resp PlayRecord
	err := m.conn.QueryRow(&resp, query, uniqueId)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrPlayRecordNotFound
	default:
		return nil, err
	}
}

func (m *defaultPlayRecordModel) FindByVid(vid int64, page, pageSize int64) ([]*PlayRecord, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where vid = ? order by id desc limit ?, ?", playRecordFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(id) from %s where vid = ?", m.table)

	var resp []*PlayRecord
	var count int64
	err := m.conn.QueryRows(&resp, query, vid, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, vid)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultPlayRecordModel) FindBySongCode(songCode int64, page, pageSize int64) ([]*PlayRecord, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where song_code = ? order by id desc limit ?, ?", playRecordFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(id) from %s where song_code = ?", m.table)

	var resp []*PlayRecord
	var count int64
	err := m.conn.QueryRows(&resp, query, songCode, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, songCode)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultPlayRecordModel) FindByPushStatus(pushStatus int, limit int64) ([]*PlayRecord, error) {
	if limit <= 0 {
		limit = 100
	}

	query := fmt.Sprintf("select %s from %s where push_status = ? order by id asc limit ?", playRecordFieldNames, m.table)
	var resp []*PlayRecord
	err := m.conn.QueryRows(&resp, query, pushStatus, limit)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (m *defaultPlayRecordModel) UpdatePushStatus(id int64, pushStatus int, pushTime int64) error {
	query := fmt.Sprintf("update %s set push_status = ?, push_time = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, pushStatus, pushTime, id)
	return err
}

func (m *defaultPlayRecordModel) BatchUpdatePushStatus(ids []int64, pushStatus int, pushTime int64) error {
	if len(ids) == 0 {
		return nil
	}

	placeholders := strings.Repeat("?,", len(ids))
	placeholders = placeholders[:len(placeholders)-1]

	query := fmt.Sprintf("update %s set push_status = ?, push_time = ? where id in (%s)", m.table, placeholders)
	args := []interface{}{pushStatus, pushTime}
	for _, id := range ids {
		args = append(args, id)
	}

	_, err := m.conn.Exec(query, args...)
	return err
}

func (m *defaultPlayRecordModel) Count(condition map[string]interface{}) (int64, error) {
	query := fmt.Sprintf("select count(id) from %s", m.table)
	var args []interface{}

	if len(condition) > 0 {
		query += " where "
		clauses := []string{}

		for k, v := range condition {
			clauses = append(clauses, fmt.Sprintf("%s = ?", k))
			args = append(args, v)
		}

		query += strings.Join(clauses, " AND ")
	}

	var count int64
	err := m.conn.QueryRow(&count, query, args...)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// 高级查询方法，支持多条件组合
func (m *defaultPlayRecordModel) FindByConditions(conditions map[string]interface{}, dateRange *DateRange, page, pageSize int64) ([]*PlayRecord, int64, error) {
	// 参数验证与默认值设置
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 构建基础查询 - 使用命名变量提高可读性
	baseQuerySQL := fmt.Sprintf("SELECT %s FROM %s", playRecordFieldNames, m.table)
	countQuerySQL := fmt.Sprintf("SELECT COUNT(*) FROM %s", m.table)

	// 构建WHERE条件
	whereConditions := make([]string, 0, len(conditions)+2) // 预分配容量
	args := make([]interface{}, 0, len(conditions)+4)       // 预分配容量

	// 处理查询条件 - 增加对非法值的检查
	for field, value := range conditions {
		// 跳过空值条件
		if value == nil {
			continue
		}

		// 根据字段类型进行处理
		switch field {
		case "song_name", "singer", "vendor_name":
			// 这些字段需要关联查询，跳过
			continue
		default:
			// 常规字段 - 增加类型检查
			whereConditions = append(whereConditions, fmt.Sprintf("%s = ?", field))
			args = append(args, value)
		}
	}

	// 处理日期范围
	if dateRange != nil {
		if dateRange.StartDate > 0 {
			whereConditions = append(whereConditions, "lts >= ?")
			args = append(args, dateRange.StartDate)
		}
		if dateRange.EndDate > 0 {
			whereConditions = append(whereConditions, "lts <= ?")
			args = append(args, dateRange.EndDate)
		}
	}

	// 组装完整SQL
	if len(whereConditions) > 0 {
		whereClause := " WHERE " + strings.Join(whereConditions, " AND ")
		baseQuerySQL += whereClause
		countQuerySQL += whereClause
	}

	// 添加排序和分页
	baseQuerySQL += " ORDER BY id DESC LIMIT ?, ?"

	// 执行查询
	var resp []*PlayRecord
	var count int64

	// 先查总数 - 使用命名变量记录开始时间以便于性能分析
	startTime := time.Now()
	err := m.conn.QueryRow(&count, countQuerySQL, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("count query failed: %w", err)
	}

	// 如果没有记录，提前返回
	if count == 0 {
		return []*PlayRecord{}, 0, nil
	}

	// 查询详细记录
	queryArgs := append(args, offset, pageSize)
	err = m.conn.QueryRows(&resp, baseQuerySQL, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("data query failed: %w", err)
	}

	// 记录查询耗时（实际项目中可使用性能监控系统）
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("PlayRecord query slow: %dms, conditions: %v", queryTime.Milliseconds(), conditions)
	}

	return resp, count, nil
}

// BatchImport 批量导入播放记录
// 返回成功导入的数量、失败记录及可能的错误
func (m *defaultPlayRecordModel) BatchImport(records []*PlayRecord) (successCount int, failedRecords map[int]string, err error) {
	failedRecords = make(map[int]string)

	for i, record := range records {
		// 检查必要字段
		if record.SongCode <= 0 {
			failedRecords[i] = "歌曲编码不能为空或小于等于0"
			continue
		}

		if record.Lts <= 0 {
			failedRecords[i] = "播放时间戳不能为空或小于等于0"
			continue
		}

		if record.Vid <= 0 {
			failedRecords[i] = "客户ID不能为空或小于等于0"
			continue
		}

		// 生成唯一ID (如果没有)
		if record.UniqueId == "" {
			// 使用歌曲编码+时间戳+随机数生成唯一ID
			randNum := time.Now().UnixNano() % 10000
			record.UniqueId = fmt.Sprintf("%d_%d_%d", record.SongCode, record.Lts, randNum)
		}

		// 检查记录是否已存在
		existingRecord, err := m.FindOneByUniqueId(record.UniqueId)
		if err == nil && existingRecord != nil {
			failedRecords[i] = fmt.Sprintf("重复记录，唯一ID: %s 已存在", record.UniqueId)
			continue
		} else if err != nil && err != ErrPlayRecordNotFound {
			// 查询出错但不是"未找到"错误
			failedRecords[i] = fmt.Sprintf("检查记录是否存在时出错: %s", err.Error())
			continue
		}

		// 设置默认值
		if record.CreateTime.IsZero() {
			record.CreateTime = time.Now()
		}

		if record.PlayNum == 0 {
			record.PlayNum = 1 // 默认播放次数为1
		}

		// 插入数据
		_, err = m.Insert(record)
		if err != nil {
			failedRecords[i] = fmt.Sprintf("数据库插入失败: %s", err.Error())
			continue
		}

		successCount++
	}

	return successCount, failedRecords, nil
}

// GetLatestPlayRecord 获取最新的播放记录
func (m *defaultPlayRecordModel) GetLatestPlayRecord(playType int) (*PlayRecord, error) {
	query := fmt.Sprintf("SELECT %s FROM %s WHERE play_type = ? ORDER BY lts DESC LIMIT 1", playRecordFieldNames, m.table)
	var resp PlayRecord
	err := m.conn.QueryRow(&resp, query, playType)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultPlayRecordModel) FindSongCodeByVid(ltsTime string, playType int, vid int64, page, pageSize int64) ([]int64, int64, error) {
	var limitQuery string
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		limitQuery = fmt.Sprintf("LIMIT %d, %d", offset, pageSize)
	}

	// 将日期字符串转换为时间戳范围
	startTs, endTs, err := middleware.ConvertDateToUTCTimestampRangeStartTime(ltsTime)
	if err != nil {
		return nil, 0, err
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE lts BETWEEN ? AND ? AND play_type = ?")

	args := []interface{}{startTs, endTs, playType}

	if vid > 0 {
		whereBuilder.WriteString(" AND vid = ?")
		args = append(args, vid)
	}

	whereClause := whereBuilder.String()

	query := fmt.Sprintf("SELECT DISTINCT song_code FROM %s FORCE INDEX(idx_lts_play_type) %s %s", m.table, whereClause, limitQuery)
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT song_code) FROM %s FORCE INDEX(idx_lts_play_type) %s", m.table, whereClause)

	var resp []int64
	var count int64
	err = m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}
