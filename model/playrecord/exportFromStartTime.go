package playrecord

import (
	"fmt"
	"music/internal/types"
	"strings"
)

/*
var exportUtcSql = "select  vid, DATE_FORMAT(FROM_UNIXTIME(lts / 1000), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type from %s where play_type = ? and lts BETWEEN ? AND ? group by vid ,lts_time order by lts_time "
var exportBjSql = "select  vid, DATE_FORMAT(FROM_UNIXTIME(lts / 1000  + 28800 ), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type from %s where play_type = ? and lts BETWEEN ? AND ? group by vid ,lts_time order by lts_time "
var exportUtcDatesSql = "SELECT  vid,  DATE_FORMAT(FROM_UNIXTIME(MAX(lts)/1000), '%%Y-%%m-%%d') AS lts_time,  SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total,  MAX(play_type) AS play_type FROM %s WHERE play_type = ? AND lts BETWEEN ? AND ? AND lts_time in ( ? ) GROUP BY vid,lts_time ORDER BY lts_time"
var exportBjDatesSql = "select  vid, DATE_FORMAT(FROM_UNIXTIME(lts / 1000  + 28800 ), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type from %s where play_type = ? and lts BETWEEN ? AND ? AND lts_time in ( ? )  group by vid ,lts_time order by lts_time "
*/

var exportStartTimeUtcSql = "select vid, DATE_FORMAT(FROM_UNIXTIME(start_time), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type from %s %s group by vid, lts_time order by lts_time"
var exportStartTimeBjSql = "select vid, DATE_FORMAT(FROM_UNIXTIME(start_time + 28800), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type from %s %s group by vid, lts_time order by lts_time"
var exportStartTimeUtcDatesSql = "SELECT vid, DATE_FORMAT(FROM_UNIXTIME(MAX(start_time)), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type FROM %s %s GROUP BY vid, lts_time ORDER BY lts_time"
var exportStartTimeBjDatesSql = "select vid, DATE_FORMAT(FROM_UNIXTIME(start_time + 28800), '%%Y-%%m-%%d') AS lts_time, SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, MAX(play_type) AS play_type from %s %s Group by vid, lts_time order by lts_time"

func (m *defaultPlayRecordModel) FindByPlayTypeGroupByStartTimeRangeForExport(startTimeBegin, startTimeEnd int64, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error) {

	// 构建WHERE条件
	whereConditions := make([]string, 0) // 预分配容量
	args := make([]interface{}, 0)       // 预分配容量

	whereConditions = append(whereConditions, "play_type = ?")
	args = append(args, playType)

	whereConditions = append(whereConditions, "start_time BETWEEN ? AND ?")
	args = append(args, startTimeBegin)
	args = append(args, startTimeEnd)

	if vid != 0 {
		whereConditions = append(whereConditions, "vid = ?")
		args = append(args, vid)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = " WHERE " + strings.Join(whereConditions, " AND ")
	}

	var query string
	if timeZoneType == types.UTC {
		query = fmt.Sprintf(exportUtcSql, m.table, whereClause)
	} else {
		query = fmt.Sprintf(exportBjSql, m.table, whereClause)
	}

	var resp []*SumPlayRecordLtsForExport

	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (m *defaultPlayRecordModel) FindByPlayTypeGroupByStartTimeDatesForExport(startTimeBegin, startTimeEnd int64, dates []string, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error) {

	// 构建WHERE条件
	whereConditions := make([]string, 0) // 预分配容量
	args := make([]interface{}, 0)       // 预分配容量

	whereConditions = append(whereConditions, "play_type = ?")
	args = append(args, playType)

	whereConditions = append(whereConditions, "start_time BETWEEN ? AND ?")
	args = append(args, startTimeBegin)
	args = append(args, startTimeEnd)

	// 处理日期范围
	if len(dates) > 0 {
		if timeZoneType == types.UTC {
			whereConditions = append(whereConditions, fmt.Sprintf(" DATE_FORMAT(FROM_UNIXTIME(MAX(start_time)), '%%Y-%%m-%%d') in (%s)",
				strings.Join(strings.Split(strings.Repeat("?", len(dates)), ""), ",")))
		} else {
			whereConditions = append(whereConditions, fmt.Sprintf(" DATE_FORMAT(FROM_UNIXTIME(MAX(start_time) + 28800), '%%Y-%%m-%%d') in (%s)",
				strings.Join(strings.Split(strings.Repeat("?", len(dates)), ""), ",")))
		}
		for _, date := range dates {
			args = append(args, date)
		}
	}

	if vid != 0 {
		whereConditions = append(whereConditions, "vid = ?")
		args = append(args, vid)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = " WHERE " + strings.Join(whereConditions, " AND ")
	}

	var query string
	if timeZoneType == types.UTC {
		query = fmt.Sprintf(exportUtcDatesSql, m.table, whereClause)
	} else {
		query = fmt.Sprintf(exportBjDatesSql, m.table, whereClause)
	}

	var resp []*SumPlayRecordLtsForExport

	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (m *defaultPlayRecordModel) FindByPlayTypeGroupByStartTimeDatesForExportV2(startTimeBegin, startTimeEnd int64, dates []string, playType int, vid int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error) {
	// 构建WHERE条件
	whereConditions := make([]string, 0) // 预分配容量
	args := make([]interface{}, 0)       // 预分配容量

	whereConditions = append(whereConditions, "play_type = ?")
	args = append(args, playType)

	whereConditions = append(whereConditions, "start_time BETWEEN ? AND ?")
	args = append(args, startTimeBegin)
	args = append(args, startTimeEnd)

	if vid != 0 {
		whereConditions = append(whereConditions, "vid = ?")
		args = append(args, vid)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = " WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 构建日期过滤条件 - 使用HAVING子句而不是WHERE子句
	havingClause := ""
	if len(dates) > 0 {
		placeholders := make([]string, len(dates))
		for i := range dates {
			placeholders[i] = "?"
		}

		if timeZoneType == types.UTC {
			havingClause = fmt.Sprintf(" HAVING lts_time IN (%s) ", strings.Join(placeholders, ","))
		} else {
			havingClause = fmt.Sprintf(" HAVING lts_time IN (%s) ", strings.Join(placeholders, ","))
		}

		for _, date := range dates {
			args = append(args, date)
		}
	}

	// 构建完整查询
	var query string
	if timeZoneType == types.UTC {
		query = fmt.Sprintf("SELECT vid, DATE_FORMAT(FROM_UNIXTIME(start_time - 28800), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type FROM %s USE INDEX(idx_start_time_play_type) %s "+
			"GROUP BY vid, lts_time %s ORDER BY lts_time",
			m.table, whereClause, havingClause)
	} else {
		query = fmt.Sprintf("SELECT vid, DATE_FORMAT(FROM_UNIXTIME(start_time), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type FROM %s USE INDEX(idx_start_time_play_type) %s "+
			"GROUP BY vid, lts_time %s ORDER BY lts_time",
			m.table, whereClause, havingClause)
	}

	var resp []*SumPlayRecordLtsForExport
	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (m *defaultPlayRecordModel) FindByPlayTypeGroupByStartTimeDatesForExportV3(startTimeBegin, startTimeEnd int64, dates []string, playType int, vid []int64, useType int64, timeZoneType types.TimeZoneType) ([]*SumPlayRecordLtsForExport, error) {
	// 构建WHERE条件
	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	args := make([]interface{}, 0) // 预分配容量

	whereBuilder.WriteString("WHERE play_type = ? AND start_time BETWEEN ? AND ?")
	args = append(args, playType)
	args = append(args, startTimeBegin)
	args = append(args, startTimeEnd)

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理useType条件
	if useType > 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, useType)
	}

	// 构建WHERE子句
	whereClause := whereBuilder.String()

	// 构建日期过滤条件 - 使用HAVING子句而不是WHERE子句
	havingClause := ""
	if len(dates) > 0 {
		placeholders := make([]string, len(dates))
		for i := range dates {
			placeholders[i] = "?"
		}

		if timeZoneType == types.UTC {
			havingClause = fmt.Sprintf(" HAVING lts_time IN (%s) ", strings.Join(placeholders, ","))
		} else {
			havingClause = fmt.Sprintf(" HAVING lts_time IN (%s) ", strings.Join(placeholders, ","))
		}

		for _, date := range dates {
			args = append(args, date)
		}
	}

	// 构建完整查询
	var query string
	if timeZoneType == types.UTC {
		query = fmt.Sprintf("SELECT vid, DATE_FORMAT(FROM_UNIXTIME(start_time - 28800), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type FROM %s USE INDEX(idx_start_time_vid_play_type) %s "+
			"GROUP BY vid, lts_time %s ORDER BY lts_time",
			m.table, whereClause, havingClause)
	} else {
		query = fmt.Sprintf("SELECT vid, DATE_FORMAT(FROM_UNIXTIME(start_time), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type FROM %s USE INDEX(idx_start_time_vid_play_type) %s "+
			"GROUP BY vid, lts_time %s ORDER BY lts_time",
			m.table, whereClause, havingClause)
	}

	var resp []*SumPlayRecordLtsForExport
	err := m.conn.QueryRows(&resp, query, args...)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
