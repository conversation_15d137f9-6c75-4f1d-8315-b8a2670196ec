package playrecord

import (
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

var (
	insertPlayRecordFieldNamesForBatch = strings.Join(insertPlayRecordRowsForBatch, ",")
	insertPlayRecordRowsForBatch       = []string{"lts", "vid", "unique_id", "cp_id", "song_code", "song_duration",
		"play_duration", "start_time", "end_time", "playback_pos", "play_num", "scene_type", "play_type", "free_type",
		"record_type", "use_type"}
)

// BatchImportGoroutineWithTx 使用协程和事务批量导入播放记录
func (m *defaultPlayRecordModel) BatchImportGoroutineWithTx(records []*PlayRecord) (successCount int, failedRecords map[int]string, err error) {
	if len(records) == 0 {
		return 0, make(map[int]string), nil
	}

	failedRecords = make(map[int]string)
	var mu sync.Mutex // 用于保护 failedRecords
	var wg sync.WaitGroup

	// 控制并发数量
	maxWorkers := 5
	batchSize := 200 // 每个事务处理的记录数

	// 将记录分成多个批次
	batches := make([][]*PlayRecord, 0)
	batchIndexes := make([][]int, 0) // 存储每个批次中记录的原始索引

	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}

		// 创建批次及其索引
		batch := records[i:end]
		indexes := make([]int, len(batch))
		for j := range batch {
			indexes[j] = i + j
		}

		batches = append(batches, batch)
		batchIndexes = append(batchIndexes, indexes)
	}

	// 使用信号量控制并发数
	semaphore := make(chan struct{}, maxWorkers)

	// 原子计数器用于记录成功数量
	var atomicSuccessCount int32

	// 处理每个批次
	for batchNum, batch := range batches {
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量

		go func(batchNum int, batch []*PlayRecord, indexes []int) {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			// 为每个批次创建一个事务
			commonSql, err := m.conn.RawDB()
			if err != nil {
				mu.Lock()
				for _, idx := range indexes {
					failedRecords[idx] = fmt.Sprintf("创建事务失败: %s", err.Error())
				}
				mu.Unlock()
				return
			}

			tx, err := commonSql.Begin()
			if err != nil {
				mu.Lock()
				for _, idx := range indexes {
					failedRecords[idx] = fmt.Sprintf("创建事务失败: %s", err.Error())
				}
				mu.Unlock()
				return
			}

			// 处理批次中的每条记录
			batchSuccessCount := 0
			batchFailedIndexes := make([]int, 0)
			batchFailedReasons := make([]string, 0)

			for i, record := range batch {
				originalIdx := indexes[i]

				// 验证记录
				if err := validateRecord(record); err != nil {
					batchFailedIndexes = append(batchFailedIndexes, originalIdx)
					batchFailedReasons = append(batchFailedReasons, err.Error())
					continue
				}

				// 生成唯一ID (如果没有)
				if record.UniqueId == "" {
					randNum := time.Now().UnixNano() % 10000
					record.UniqueId = fmt.Sprintf("%d_%d_%d", record.SongCode, record.Lts, randNum)
				}

				// 设置默认值
				if record.CreateTime.IsZero() {
					record.CreateTime = time.Now()
				}

				// 准备参数
				args := []interface{}{
					record.Lts, record.Vid, record.UniqueId,
					record.CpId, record.SongCode, record.SongDuration, record.PlayDuration,
					record.StartTime, record.EndTime, record.PlaybackPos, record.PlayNum,
					record.SceneType, record.PlayType, record.FreeType, record.RecordType,
					record.UseType,
				}

				// 构建插入SQL
				query := fmt.Sprintf(
					"INSERT INTO %s (%s) VALUES (%s)",
					m.table,
					insertPlayRecordFieldNamesForBatch,
					createPlaceholders(len(args)))

				// 执行插入
				_, err := tx.Exec(query, args...)
				if err != nil {
					// 检查是否是唯一键冲突
					if strings.Contains(err.Error(), "Duplicate entry") {
						batchFailedIndexes = append(batchFailedIndexes, originalIdx)
						batchFailedReasons = append(batchFailedReasons, fmt.Sprintf("重复记录: %s", err.Error()))
					} else {
						batchFailedIndexes = append(batchFailedIndexes, originalIdx)
						batchFailedReasons = append(batchFailedReasons, fmt.Sprintf("插入失败: %s", err.Error()))
					}
					continue
				}

				batchSuccessCount++
			}

			// 如果批次中有成功的记录，提交事务
			if batchSuccessCount > 0 {
				if err := tx.Commit(); err != nil {
					// 提交失败，所有记录都标记为失败
					mu.Lock()
					for _, idx := range indexes {
						failedRecords[idx] = fmt.Sprintf("提交事务失败: %s", err.Error())
					}
					mu.Unlock()
				} else {
					// 提交成功，更新成功计数
					atomic.AddInt32(&atomicSuccessCount, int32(batchSuccessCount))

					// 记录失败的记录
					if len(batchFailedIndexes) > 0 {
						mu.Lock()
						for i, idx := range batchFailedIndexes {
							failedRecords[idx] = batchFailedReasons[i]
						}
						mu.Unlock()
					}
				}
			} else {
				// 批次中没有成功的记录，回滚事务
				tx.Rollback()

				// 记录所有失败的记录
				mu.Lock()
				for i, idx := range batchFailedIndexes {
					failedRecords[idx] = batchFailedReasons[i]
				}
				mu.Unlock()
			}
		}(batchNum, batch, batchIndexes[batchNum])
	}

	// 等待所有协程完成
	wg.Wait()

	// 转换原子计数器为返回值
	successCount = int(atomicSuccessCount)

	return successCount, failedRecords, nil
}

// validateRecord 验证记录的必要字段
func validateRecord(record *PlayRecord) error {
	if record.SongCode <= 0 {
		return fmt.Errorf("歌曲编码不能为空或小于等于0")
	}

	if record.Lts <= 0 {
		return fmt.Errorf("播放时间戳不能为空或小于等于0")
	}

	if record.Vid <= 0 {
		return fmt.Errorf("客户ID不能为空或小于等于0")
	}

	return nil
}

// createPlaceholders 创建SQL占位符
func createPlaceholders(count int) string {
	placeholders := make([]string, count)
	for i := range placeholders {
		placeholders[i] = "?"
	}
	return strings.Join(placeholders, ",")
}
