package playrecord

import (
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/middleware"
	"music/internal/types"
	"strings"
	"time"
)

// FindByPlayTypeGroupByLts 根据播放类型分组查询  日期分组 YYYY-MM-DD
func (m *defaultPlayRecordModel) FindByPlayTypeGroupByLts(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 构建WHERE条件
	whereConditions := make([]string, 0) // 预分配容量
	args := make([]interface{}, 0)       // 预分配容量

	whereConditions = append(whereConditions, "play_type = ?")
	args = append(args, playType)

	whereConditions = append(whereConditions, "lts >= ?")
	args = append(args, ltsTimeBegin)
	whereConditions = append(whereConditions, "lts <= ?")
	args = append(args, ltsTimeEnd)

	if vid != 0 {
		whereConditions = append(whereConditions, "vid = ?")
		args = append(args, vid)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = " WHERE " + strings.Join(whereConditions, " AND ")
	}

	//query := fmt.Sprintf("select FROM_UNIXTIME(lts, '%%Y-%%m-%%d') as lts_time, sum(play_num) as play_num_total, sum(play_duration) as play_duration_total, song_code,cpid,vid from %s where play_type = ? and lts >= ? and lts <= ? group by lts_time order by lts_time limit ?, ?", m.table)
	query := fmt.Sprintf("select DATE_FORMAT(FROM_UNIXTIME(lts / 1000 -28800),  '%%Y-%%m-%%d') as lts_time, sum(play_num) as play_num_total, sum(play_duration) as play_duration_total, ANY_VALUE(play_type) as play_type from %s  %s group by lts_time order by lts_time limit ?, ?", m.table, whereClause)
	countQuery := fmt.Sprintf("select  count(*)  from ( select  DATE_FORMAT(FROM_UNIXTIME(lts / 1000 -28800), '%%Y-%%m-%%d') as lts_time from %s  %s GROUP BY lts_time ) as t", m.table, whereClause)

	var resp []*SumPlayRecordLts
	var count int64
	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

// FindGroupByCustomer 根据歌曲编码查询 并按歌曲编码分组  前置 条件  日期
/* ltsTime 和 playType 为 固定 传参   */
func (m *defaultPlayRecordModel) FindGroupByCustomer(ltsTime string, playType int, vid []int64, useType, page, pageSize int64) ([]*SumPlayCustomerRecord, int64, error) {
	offset := (page - 1) * pageSize

	// 将日期字符串转换为时间戳范围
	startTs, endTs, err := middleware.ConvertDateToUTCTimestampRangeStartTime(ltsTime)
	if err != nil {
		return nil, 0, err
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE start_time BETWEEN ? AND ? AND play_type = ?")

	args := []interface{}{startTs, endTs, playType}

	// 处理useType条件
	if useType != 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, useType)
	}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	whereClause := whereBuilder.String()

	// 使用FORCE INDEX提示优化器使用lts索引
	query := fmt.Sprintf("SELECT vid, ANY_VALUE(use_type) as use_type, ANY_VALUE(play_type) as play_type, "+
		"SUM(play_num) as play_num_total, SUM(play_duration) as play_duration_total "+
		"FROM %s FORCE INDEX(idx_start_time_play_type) %s GROUP BY vid LIMIT ?, ?",
		m.table, whereClause)

	// 优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT vid) FROM %s FORCE INDEX(idx_start_time_play_type) %s",
		m.table, whereClause)

	var resp []*SumPlayCustomerRecord
	var count int64

	// 执行查询
	queryArgs := append(args, offset, pageSize)
	err = m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户分组数据失败: %w", err)
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户分组总数失败: %w", err)
	}

	return resp, count, nil
}

func (m *defaultPlayRecordModel) FindDetailGroupByCustomer(ltsTime string, vid []int64, playType, useType, sceneType, freeType, page, pageSize int64) ([]*PlayRecordDetail, int64, error) {
	// 参数验证
	if ltsTime == "" {
		return nil, 0, ErrEmptyLtsTime
	}
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 将日期字符串转换为时间戳范围
	startTs, endTs, err := middleware.ConvertDateToUTCTimestampRangeStartTime(ltsTime)
	if err != nil {
		return nil, 0, err
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE start_time BETWEEN ? AND ?")

	args := []interface{}{startTs, endTs}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理其他条件
	if playType != 0 {
		whereBuilder.WriteString(" AND play_type = ?")
		args = append(args, playType)
	}

	if useType != 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, useType)
	}

	if sceneType != 0 {
		whereBuilder.WriteString(" AND scene_type = ?")
		args = append(args, sceneType)
	}

	if freeType != 0 {
		whereBuilder.WriteString(" AND free_type = ?")
		args = append(args, freeType)
	}

	whereClause := whereBuilder.String()

	// 使用索引提示优化查询
	query := fmt.Sprintf("SELECT %s FROM %s FORCE INDEX(idx_start_time_play_type) %s ORDER BY id DESC LIMIT ?, ?",
		playRecordDetailFieldNames, m.table, whereClause)

	// 优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s FORCE INDEX(idx_start_time_play_type) %s",
		m.table, whereClause)

	var resp []*PlayRecordDetail
	var count int64

	// 执行查询
	queryArgs := append(args, offset, pageSize)

	// 添加查询日志，便于调试
	startTime := time.Now()
	err = m.conn.QueryRows(&resp, query, queryArgs...)
	queryTime := time.Since(startTime)

	if err != nil {
		logx.Errorf("查询客户详细记录失败: %v, 耗时: %v, SQL: %s", err, queryTime, query)
		return nil, 0, fmt.Errorf("查询客户详细记录失败: %w", err)
	}

	if queryTime > 200*time.Millisecond {
		logx.Slowf("客户详细记录查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户详细记录总数失败: %w", err)
	}

	return resp, count, nil
}

// FindGroupBySong 根据歌曲编码查询 并按歌曲编码分组  前置 条件  日期
/* ltsTime 和 playType 为 固定 传参   */
/* 歌曲名  需要 在列表返回时 去 song 表中查询  song  和 playRecord 不在一个数据库中 */
func (m *defaultPlayRecordModel) FindGroupBySong(ltsTime string, playType int, songCode []int64, page, pageSize int64) ([]*SumPlaySongRecord, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 验证必填参数
	if ltsTime == "" {
		return nil, 0, ErrEmptyLtsTime
	}
	if playType == 0 {
		return nil, 0, ErrEmptyPlayType
	}

	// 将日期字符串转换为时间戳范围
	startTs, endTs, err := middleware.ConvertDateToUTCTimestampRangeStartTime(ltsTime)
	if err != nil {
		return nil, 0, err
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE start_time BETWEEN ? AND ? AND play_type = ?")

	args := []interface{}{startTs, endTs, playType}

	// 处理歌曲编码条件
	if len(songCode) > 0 {
		whereBuilder.WriteString(" AND song_code IN (")
		placeholders := make([]string, len(songCode))
		for i := range songCode {
			placeholders[i] = "?"
			args = append(args, songCode[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	whereClause := whereBuilder.String()

	// 使用索引提示优化查询
	query := fmt.Sprintf("SELECT %s FROM %s FORCE INDEX(idx_start_time_play_type) %s GROUP BY song_code LIMIT ?, ?",
		sumPlayRecordSongFieldNames, m.table, whereClause)

	// 优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT song_code) FROM %s FORCE INDEX(idx_start_time_play_type) %s",
		m.table, whereClause)

	var resp []*SumPlaySongRecord
	var count int64

	// 执行查询并记录性能
	startTime := time.Now()

	// 执行查询
	queryArgs := append(args, offset, pageSize)
	err = m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询歌曲分组数据失败: %w", err)
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询歌曲分组总数失败: %w", err)
	}

	// 记录查询耗时
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("歌曲分组查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	return resp, count, nil
}

func (m *defaultPlayRecordModel) FindDetailGroupBySong(ltsTime string, songCode []int64, vid []int64, req *types.SumPlayRecordDetailByVidReq) ([]*PlayRecordDetail, int64, error) {
	// 参数验证
	if ltsTime == "" {
		return nil, 0, ErrEmptyLtsTime
	}
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	offset := (req.Page - 1) * req.PageSize

	// 将日期字符串转换为时间戳范围
	startTs, endTs, err := middleware.ConvertDateToUTCTimestampRangeStartTime(ltsTime)
	if err != nil {
		return nil, 0, err
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE start_time BETWEEN ? AND ?")

	args := []interface{}{startTs, endTs}

	// 处理歌曲编码条件
	if len(songCode) > 0 {
		whereBuilder.WriteString(" AND song_code IN (")
		placeholders := make([]string, len(songCode))
		for i := range songCode {
			placeholders[i] = "?"
			args = append(args, songCode[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 添加其他条件
	if req.SceneType != 0 {
		whereBuilder.WriteString(" AND scene_type = ?")
		args = append(args, req.SceneType)
	}

	if req.PlayType != 0 {
		whereBuilder.WriteString(" AND play_type = ?")
		args = append(args, req.PlayType)
	}

	if req.FreeType != 0 {
		whereBuilder.WriteString(" AND free_type = ?")
		args = append(args, req.FreeType)
	}

	if req.UseType != 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, req.UseType)
	}

	if req.CpId != 0 {
		whereBuilder.WriteString(" AND cp_id = ?")
		args = append(args, req.CpId)
	}

	whereClause := whereBuilder.String()

	// 使用索引提示优化查询
	query := fmt.Sprintf("SELECT %s FROM %s FORCE INDEX(idx_start_time_play_type) %s ORDER BY id DESC LIMIT ?, ?",
		playRecordDetailFieldNames, m.table, whereClause)

	// 优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s FORCE INDEX(idx_start_time_play_type) %s",
		m.table, whereClause)

	var resp []*PlayRecordDetail
	var count int64

	// 执行查询并记录性能
	startTime := time.Now()

	// 执行查询
	queryArgs := append(args, offset, req.PageSize)
	err = m.conn.QueryRows(&resp, query, queryArgs...)

	queryTime := time.Since(startTime)
	if err != nil {
		logx.Errorf("查询歌曲详细记录失败: %v, 耗时: %v, SQL: %s", err, queryTime, query)
		return nil, 0, fmt.Errorf("查询歌曲详细记录失败: %w", err)
	}

	if queryTime > 200*time.Millisecond {
		logx.Slowf("歌曲详细记录查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询歌曲详细记录总数失败: %w", err)
	}

	return resp, count, nil
}

/*---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------*/

// V2  版本的查询方法
func (m *defaultPlayRecordModel) FindByPlayTypeGroupByLtsV2(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error) {
	// 参数验证与默认值设置
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE play_type = ? AND lts BETWEEN ? AND ?")

	args := []interface{}{playType, ltsTimeBegin, ltsTimeEnd}

	// 添加vid条件
	if vid != 0 {
		whereBuilder.WriteString(" AND vid = ?")
		args = append(args, vid)
	}

	whereClause := whereBuilder.String()

	// 优化查询 - 使用预计算的日期分组表或物化视图
	// 如果没有物化视图，可以使用以下优化查询
	query := fmt.Sprintf(
		"SELECT DATE_FORMAT(FROM_UNIXTIME(lts / 1000), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, "+
			"SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type "+
			"FROM %s USE INDEX(idx_lts_play_type) %s "+
			"GROUP BY lts_time "+
			"ORDER BY lts_time "+
			"LIMIT ?, ?",
		m.table, whereClause)

	// 优化COUNT查询 - 使用近似计数或预计算
	// 对于分页展示，可以考虑使用近似计数或固定大数值
	countQuery := fmt.Sprintf(
		"SELECT COUNT(1) FROM ("+
			"SELECT 1 FROM %s USE INDEX(idx_lts_play_type) %s "+
			"GROUP BY DATE_FORMAT(FROM_UNIXTIME(lts / 1000), '%%Y-%%m-%%d') "+
			"LIMIT 1000"+
			") AS temp",
		m.table, whereClause)

	// 执行查询
	startTime := time.Now()

	// 计算月份差异（用于决定是否使用估算）
	monthsDiff := (ltsTimeEnd - ltsTimeBegin) / (30 * 24 * 60 * 60 * 1000)

	// 数据查询
	queryArgs := append([]interface{}{}, args...)
	queryArgs = append(queryArgs, offset, pageSize)

	var resp []*SumPlayRecordLts
	var count int64

	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期分组数据失败: %w", err)
	}

	// 计数查询 - 对于大范围使用估算
	if monthsDiff > 3 {
		// 估算值：每个月最多31天
		count = int64(monthsDiff * 31)
	} else {
		err = m.conn.QueryRow(&count, countQuery, args...)
		if err != nil {
			return nil, 0, fmt.Errorf("查询日期分组总数失败: %w", err)
		}
	}

	// 记录查询耗时
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("日期分组查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	return resp, count, nil
}

func (m *defaultPlayRecordModel) FindGroupBySongV2(ltsTime string, playType int, songCode []int64, page, pageSize int64) ([]*SumPlaySongRecord, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 验证必填参数
	if ltsTime == "" {
		return nil, 0, ErrEmptyLtsTime
	}
	if playType == 0 {
		return nil, 0, ErrEmptyPlayType
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE DATE_FORMAT(FROM_UNIXTIME(lts / 1000  - 28800), '%Y-%m-%d') = ? AND play_type = ?")

	args := []interface{}{ltsTime, playType}

	// 处理歌曲编码条件
	if len(songCode) > 0 {
		whereBuilder.WriteString(" AND song_code IN (")
		placeholders := make([]string, len(songCode))
		for i := range songCode {
			placeholders[i] = "?"
			args = append(args, songCode[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	whereClause := whereBuilder.String()

	// 使用索引提示优化查询
	query := fmt.Sprintf("SELECT %s FROM %s %s GROUP BY song_code LIMIT ?, ?",
		sumPlayRecordSongFieldNames, m.table, whereClause)

	// 使用子查询优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT song_code) FROM %s %s",
		m.table, whereClause)

	var resp []*SumPlaySongRecord
	var count int64

	// 执行查询
	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultPlayRecordModel) FindDetailGroupBySongV2(ltsTime string, songCode []int64, vid []int64, req *types.SumPlayRecordDetailByVidReq) ([]*PlayRecordDetail, int64, error) {
	offset := (req.Page - 1) * req.PageSize

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE DATE_FORMAT(FROM_UNIXTIME(lts / 1000  - 28800 ), '%Y-%m-%d') = ?")

	args := []interface{}{ltsTime}

	// 处理歌曲编码条件
	if len(songCode) > 0 {
		whereBuilder.WriteString(" AND song_code IN (")
		placeholders := make([]string, len(songCode))
		for i := range songCode {
			placeholders[i] = "?"
			args = append(args, songCode[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 添加其他条件
	if req.SceneType != 0 {
		whereBuilder.WriteString(" AND scene_type = ?")
		args = append(args, req.SceneType)
	}

	if req.PlayType != 0 {
		whereBuilder.WriteString(" AND play_type = ?")
		args = append(args, req.PlayType)
	}

	if req.FreeType != 0 {
		whereBuilder.WriteString(" AND free_type = ?")
		args = append(args, req.FreeType)
	}

	if req.UseType != 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, req.UseType)
	}

	if req.CpId != 0 {
		whereBuilder.WriteString(" AND cp_id = ?")
		args = append(args, req.CpId)
	}

	whereClause := whereBuilder.String()

	// 优化查询 - 添加索引提示和限制返回字段
	query := fmt.Sprintf("SELECT %s FROM %s %s ORDER BY id DESC LIMIT ?, ?",
		playRecordDetailFieldNames, m.table, whereClause)

	// 优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s",
		m.table, whereClause)

	var resp []*PlayRecordDetail
	var count int64

	// 执行查询
	queryArgs := append(args, offset, req.PageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

// FindGroupByCustomer 根据客户ID分组查询播放记录
// 优化版本：使用参数化查询、字符串构建器和更高效的SQL
func (m *defaultPlayRecordModel) FindGroupByCustomerV2(ltsTime string, playType int, vid []int64, useType, page, pageSize int64) ([]*SumPlayCustomerRecord, int64, error) {
	// 参数验证
	if ltsTime == "" {
		return nil, 0, ErrEmptyLtsTime
	}
	if playType == 0 {
		return nil, 0, ErrEmptyPlayType
	}
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE DATE_FORMAT(FROM_UNIXTIME(lts / 1000  - 28800 ), '%Y-%m-%d') = ? AND play_type = ?")

	args := []interface{}{ltsTime, playType}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理useType条件
	if useType > 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, useType)
	}

	whereClause := whereBuilder.String()

	// 优化查询 - 使用索引提示和更高效的聚合函数
	query := fmt.Sprintf("SELECT vid, ANY_VALUE(use_type) as use_type, ANY_VALUE(play_type) as play_type, "+
		"SUM(play_num) as play_num_total, SUM(play_duration) as play_duration_total "+
		"FROM %s %s GROUP BY vid LIMIT ?, ?",
		m.table, whereClause)

	// 优化COUNT查询 - 使用子查询避免全表扫描
	countQuery := fmt.Sprintf("SELECT COUNT(DISTINCT vid) FROM %s %s",
		m.table, whereClause)

	var resp []*SumPlayCustomerRecord
	var count int64

	// 执行查询
	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户分组数据失败: %w", err)
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户分组总数失败: %w", err)
	}

	return resp, count, nil
}

// FindeDetailGroupByCustomer 根据客户ID查询详细播放记录
// 优化版本：使用参数化查询、字符串构建器和更高效的SQL
func (m *defaultPlayRecordModel) FindDetailGroupByCustomerV2(ltsTime string, vid []int64, playType, useType, sceneType, freeType, page, pageSize int64) ([]*PlayRecordDetail, int64, error) {
	// 参数验证
	if ltsTime == "" {
		return nil, 0, ErrEmptyLtsTime
	}
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE DATE_FORMAT(FROM_UNIXTIME(lts / 1000  - 28800), '%Y-%m-%d') = ?")

	args := []interface{}{ltsTime}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理其他条件
	if playType > 0 {
		whereBuilder.WriteString(" AND play_type = ?")
		args = append(args, playType)
	}

	if useType > 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, useType)
	}

	if sceneType > 0 {
		whereBuilder.WriteString(" AND scene_type = ?")
		args = append(args, sceneType)
	}

	if freeType > 0 {
		whereBuilder.WriteString(" AND free_type = ?")
		args = append(args, freeType)
	}

	whereClause := whereBuilder.String()

	// 优化查询 - 使用索引提示和限制返回字段
	query := fmt.Sprintf("SELECT %s FROM %s %s ORDER BY lts DESC LIMIT ?, ?",
		playRecordDetailFieldNames, m.table, whereClause)

	// 优化COUNT查询
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s",
		m.table, whereClause)

	var resp []*PlayRecordDetail
	var count int64

	// 执行查询
	queryArgs := append(args, offset, pageSize)

	// 添加查询日志，便于调试
	startTime := time.Now()
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	queryTime := time.Since(startTime)

	if err != nil {
		logx.Errorf("查询客户详细记录失败: %v, 耗时: %v, SQL: %s", err, queryTime, query)
		return nil, 0, fmt.Errorf("查询客户详细记录失败: %w", err)
	}

	if queryTime > 200*time.Millisecond {
		logx.Slowf("客户详细记录查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询客户详细记录总数失败: %w", err)
	}

	return resp, count, nil
}

/*---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------*/

// V3
func (m *defaultPlayRecordModel) FindByPlayTypeGroupByLtsV3(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error) {
	// 参数验证与默认值设置
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 计算月份差异（用于决定查询策略）
	monthsDiff := (ltsTimeEnd - ltsTimeBegin) / (30 * 24 * 60 * 60 * 1000)

	// 对于大范围查询，使用按月分段查询策略
	if monthsDiff > 2 {
		return m.findByPlayTypeGroupByLtsSegmentedV3(ltsTimeBegin, ltsTimeEnd, playType, vid, page, pageSize)
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE play_type = ? AND lts BETWEEN ? AND ?")

	args := []interface{}{playType, ltsTimeBegin, ltsTimeEnd}

	// 添加vid条件
	if vid != 0 {
		whereBuilder.WriteString(" AND vid = ?")
		args = append(args, vid)
	}

	whereClause := whereBuilder.String()

	// 使用与原始函数相同的查询方式
	query := fmt.Sprintf(
		"SELECT DATE_FORMAT(FROM_UNIXTIME(lts/1000 -28800), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, "+
			"SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type "+
			"FROM %s USE INDEX(idx_lts_play_type) %s "+
			"GROUP BY lts_time "+
			"ORDER BY lts_time "+
			"LIMIT ?, ?",
		m.table, whereClause)

	// 使用与原始函数相同的COUNT查询，确保返回准确的计数
	countQuery := fmt.Sprintf(
		"SELECT COUNT(*) FROM ("+
			"SELECT 1 FROM %s USE INDEX(idx_lts_play_type) %s "+
			"GROUP BY DATE_FORMAT(FROM_UNIXTIME(lts/1000 -28800), '%%Y-%%m-%%d')"+
			") AS t",
		m.table, whereClause)

	// 执行查询
	startTime := time.Now()

	// 数据查询
	queryArgs := append([]interface{}{}, args...)
	queryArgs = append(queryArgs, offset, pageSize)

	var resp []*SumPlayRecordLts
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期分组数据失败: %w", err)
	}

	// 执行COUNT查询获取准确的总数
	var count int64
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期分组总数失败: %w", err)
	}

	// 记录查询耗时
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("日期分组查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	return resp, count, nil
}

// V4
func (m *defaultPlayRecordModel) FindByPlayTypeGroupByLtsV4(ltsTimeBegin, ltsTimeEnd int64, playType int, vid []int64, useType int64, songcodes []int64, page, pageSize int64) ([]*SumPlayRecordLts, int64, error) {
	// 参数验证与默认值设置
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 计算月份差异（用于决定查询策略）
	monthsDiff := (ltsTimeEnd - ltsTimeBegin) / (30 * 24 * 60 * 60 * 1000)

	// 对于大范围查询，使用按月分段查询策略
	if monthsDiff > 2 {
		return m.findByPlayTypeGroupByLtsSegmentedV4(ltsTimeBegin, ltsTimeEnd, playType, vid, useType, songcodes, page, pageSize)
	}

	// 使用字符串构建器提高性能
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE play_type = ? AND start_time BETWEEN ? AND ?")

	args := []interface{}{playType, ltsTimeBegin, ltsTimeEnd}

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			args = append(args, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理歌曲编码条件
	if len(songcodes) > 0 {
		whereBuilder.WriteString(" AND song_code IN (")
		placeholders := make([]string, len(songcodes))
		for i := range songcodes {
			placeholders[i] = "?"
			args = append(args, songcodes[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理useType条件
	if useType > 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		args = append(args, useType)
	}

	whereClause := whereBuilder.String()

	// 使用与原始函数相同的查询方式
	query := fmt.Sprintf(
		"SELECT DATE_FORMAT(FROM_UNIXTIME(start_time -28800), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, "+
			"SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type "+
			"FROM %s USE INDEX(idx_start_time_play_type) %s "+
			"GROUP BY lts_time "+
			"ORDER BY lts_time "+
			"LIMIT ?, ?",
		m.table, whereClause)

	// 使用与原始函数相同的COUNT查询，确保返回准确的计数
	countQuery := fmt.Sprintf(
		"SELECT COUNT(*) FROM ("+
			"SELECT 1 FROM %s USE INDEX(idx_start_time_play_type) %s "+
			"GROUP BY DATE_FORMAT(FROM_UNIXTIME(start_time -28800), '%%Y-%%m-%%d')"+
			") AS t",
		m.table, whereClause)

	// 执行查询
	startTime := time.Now()

	// 数据查询
	queryArgs := append([]interface{}{}, args...)
	queryArgs = append(queryArgs, offset, pageSize)

	var resp []*SumPlayRecordLts
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期分组数据失败: %w", err)
	}

	// 执行COUNT查询获取准确的总数
	var count int64
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期分组总数失败: %w", err)
	}

	// 记录查询耗时
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("日期分组查询慢: %dms, 条件: %v", queryTime.Milliseconds(), whereClause)
	}

	return resp, count, nil
}

// findByPlayTypeGroupByLtsSegmented 分段查询日期分组数据
// 将大范围查询拆分为多个小范围查询，然后合并结果
func (m *defaultPlayRecordModel) findByPlayTypeGroupByLtsSegmentedV3(ltsTimeBegin, ltsTimeEnd int64, playType int, vid, page, pageSize int64) ([]*SumPlayRecordLts, int64, error) {
	// 计算总天数
	daysDiff := (ltsTimeEnd - ltsTimeBegin) / (24 * 60 * 60 * 1000)

	// 估算总记录数
	totalCount := daysDiff

	// 计算需要查询的页码范围
	startOffset := (page - 1) * pageSize
	endOffset := startOffset + pageSize

	if endOffset > totalCount {
		endOffset = totalCount
	}

	// 如果起始偏移已超过总数，返回空结果
	if startOffset >= totalCount {
		return []*SumPlayRecordLts{}, totalCount, nil
	}

	// 创建结果集
	result := make([]*SumPlayRecordLts, 0, pageSize)

	// 使用高效的SQL直接获取分页后的日期
	dateQuery := fmt.Sprintf(
		"SELECT DISTINCT DATE_FORMAT(FROM_UNIXTIME(lts/1000 -28800), '%%Y-%%m-%%d') AS date_str "+
			"FROM %s "+
			"WHERE play_type = ? AND lts BETWEEN ? AND ? "+
			"ORDER BY date_str "+
			"LIMIT ?, ?",
		m.table)

	var dates []string
	dateArgs := []interface{}{playType, ltsTimeBegin, ltsTimeEnd, startOffset, pageSize}

	err := m.conn.QueryRows(&dates, dateQuery, dateArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期列表失败: %w", err)
	}

	// 如果没有日期，返回空结果
	if len(dates) == 0 {
		return []*SumPlayRecordLts{}, totalCount, nil
	}

	// 构建IN查询
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE play_type = ? AND (")

	var queryArgs []interface{}
	queryArgs = append(queryArgs, playType)

	for i, date := range dates {
		// 解析日期
		t, err := time.ParseInLocation("2006-01-02", date, time.UTC)
		if err != nil {
			continue
		}

		// 计算当天的开始和结束时间戳
		dayStart := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.UTC).UnixMilli()
		dayEnd := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999, time.UTC).UnixMilli()

		if i > 0 {
			whereBuilder.WriteString(" OR ")
		}
		whereBuilder.WriteString("(lts BETWEEN ? AND ?)")
		queryArgs = append(queryArgs, dayStart, dayEnd)
	}
	whereBuilder.WriteString(")")

	// 添加vid条件
	if vid != 0 {
		whereBuilder.WriteString(" AND vid = ?")
		queryArgs = append(queryArgs, vid)
	}

	// 构建最终查询
	query := fmt.Sprintf(
		"SELECT DATE_FORMAT(FROM_UNIXTIME(lts/1000 -28800), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, "+
			"SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type "+
			"FROM %s "+
			"%s "+
			"GROUP BY lts_time "+
			"ORDER BY lts_time",
		m.table, whereBuilder.String())

	// 执行查询
	startTime := time.Now()
	err = m.conn.QueryRows(&result, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("分段查询日期分组数据失败: %w", err)
	}

	// 记录查询耗时
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("分段日期分组查询慢: %dms, 日期数: %d", queryTime.Milliseconds(), len(dates))
	}

	return result, totalCount, nil
}

func (m *defaultPlayRecordModel) findByPlayTypeGroupByLtsSegmentedV4(ltsTimeBegin, ltsTimeEnd int64, playType int, vid []int64, useType int64, songcodes []int64, page, pageSize int64) ([]*SumPlayRecordLts, int64, error) {
	// 计算总天数
	daysDiff := (ltsTimeEnd - ltsTimeBegin) / (24 * 60 * 60 * 1000)

	// 估算总记录数
	totalCount := daysDiff

	// 计算需要查询的页码范围
	startOffset := (page - 1) * pageSize
	endOffset := startOffset + pageSize

	if endOffset > totalCount {
		endOffset = totalCount
	}

	// 如果起始偏移已超过总数，返回空结果
	if startOffset >= totalCount {
		return []*SumPlayRecordLts{}, totalCount, nil
	}

	// 创建结果集
	result := make([]*SumPlayRecordLts, 0, pageSize)

	// 使用高效的SQL直接获取分页后的日期
	dateQuery := fmt.Sprintf(
		"SELECT DISTINCT DATE_FORMAT(FROM_UNIXTIME(start_time - 28800), '%%Y-%%m-%%d') AS date_str "+
			"FROM %s USE INDEX(idx_start_time_play_type) "+
			"WHERE play_type = ? AND start_time BETWEEN ? AND ? "+
			"ORDER BY date_str "+
			"LIMIT ?, ?",
		m.table)

	var dates []string
	dateArgs := []interface{}{playType, ltsTimeBegin, ltsTimeEnd, startOffset, pageSize}

	err := m.conn.QueryRows(&dates, dateQuery, dateArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期列表失败: %w", err)
	}

	// 如果没有日期，返回空结果
	if len(dates) == 0 {
		return []*SumPlayRecordLts{}, totalCount, nil
	}

	// 构建IN查询
	var whereBuilder strings.Builder
	whereBuilder.WriteString("WHERE play_type = ? AND (")

	var queryArgs []interface{}
	queryArgs = append(queryArgs, playType)

	for i, date := range dates {
		// 解析日期
		t, err := time.ParseInLocation("2006-01-02", date, time.UTC)
		if err != nil {
			continue
		}

		// 计算当天的开始和结束时间戳
		dayStart := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.UTC).UnixMilli()
		dayEnd := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999, time.UTC).UnixMilli()

		if i > 0 {
			whereBuilder.WriteString(" OR ")
		}
		whereBuilder.WriteString("(start_time BETWEEN ? AND ?)")
		queryArgs = append(queryArgs, dayStart, dayEnd)
	}
	whereBuilder.WriteString(")")

	// 处理vid条件
	if len(vid) > 0 {
		whereBuilder.WriteString(" AND vid IN (")
		placeholders := make([]string, len(vid))
		for i := range vid {
			placeholders[i] = "?"
			queryArgs = append(queryArgs, vid[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理歌曲编码条件
	if len(songcodes) > 0 {
		whereBuilder.WriteString(" AND song_code IN (")
		placeholders := make([]string, len(songcodes))
		for i := range songcodes {
			placeholders[i] = "?"
			queryArgs = append(queryArgs, songcodes[i])
		}
		whereBuilder.WriteString(strings.Join(placeholders, ","))
		whereBuilder.WriteString(")")
	}

	// 处理useType条件
	if useType > 0 {
		whereBuilder.WriteString(" AND use_type = ?")
		queryArgs = append(queryArgs, useType)
	}

	// 构建最终查询
	query := fmt.Sprintf(
		"SELECT DATE_FORMAT(FROM_UNIXTIME(start_time - 28800), '%%Y-%%m-%%d') AS lts_time, "+
			"SUM(play_num) AS play_num_total, "+
			"SUM(play_duration) AS play_duration_total, "+
			"MAX(play_type) AS play_type "+
			"FROM %s USE INDEX(idx_start_time_play_type) "+
			"%s "+
			"GROUP BY lts_time "+
			"ORDER BY lts_time",
		m.table, whereBuilder.String())

	// 执行查询
	startTime := time.Now()
	err = m.conn.QueryRows(&result, query, queryArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("分段查询日期分组数据失败: %w", err)
	}

	// 记录查询耗时
	queryTime := time.Since(startTime)
	if queryTime > 200*time.Millisecond {
		logx.Slowf("分段日期分组查询慢: %dms, 日期数: %d", queryTime.Milliseconds(), len(dates))
	}

	return result, totalCount, nil
}
