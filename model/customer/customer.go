package customer

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	customerFieldNames       = strings.Join(customerRows, ",")
	customerRows             = []string{"id", "name", "vid", "cid", "ip", "appid", "remark", "status", "end_time", "update_time", "create_time"}
	ErrCustomerNotFound      = errors.New("客户不存在")
	ErrCustomerStatusInvalid = errors.New("客户已被禁用")
)

type (
	CustomerModel interface {
		FindOne(id int64) (*Customer, error)
		FindOneByVid(vid int64) (*Customer, error)
		FindAll(page, pageSize int64) ([]*Customer, error)
		Count() (int64, error)
		Insert(data *Customer) (sql.Result, error)
		Update(data *Customer) error
		Delete(id int64) error
		FindByName(name string) ([]*Customer, error)
		Find(name string, vid, cid, beginEndTime, endEndTime, status int64, ip, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*Customer, int64, error)
		FindVid(name string) ([]int64, error)
		FindCid(name string) ([]int64, error)
		UpdateStatus(id, status, endTime int64) error
		FindOneByNameNotLike(name string) (*Customer, error)
		FindOneByCid(cid int64) (*Customer, error)
		FindByVids(vids []int64) ([]*Customer, error)
	}

	defaultCustomerModel struct {
		conn  sqlx.SqlConn
		table string
	}

	Customer struct {
		Id         int64     `db:"id"`
		Name       string    `db:"name"`
		Vid        int64     `db:"vid"`
		Cid        int64     `db:"cid"`
		Ip         string    `db:"ip"`
		Appid      string    `db:"appid"`
		Remark     string    `db:"remark"`
		Status     int64     `db:"status"`
		EndTime    int64     `db:"end_time"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
	}
)

func NewCustomerModel(conn sqlx.SqlConn) CustomerModel {
	return &defaultCustomerModel{
		conn:  conn,
		table: "customer",
	}
}

func (m *defaultCustomerModel) FindOne(id int64) (*Customer, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", customerFieldNames, m.table)
	var resp Customer
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrCustomerNotFound
	default:
		return nil, err
	}
}

func (m *defaultCustomerModel) FindOneByVid(vid int64) (*Customer, error) {
	query := fmt.Sprintf("select %s from %s where vid = ? limit 1", customerFieldNames, m.table)
	var resp Customer
	err := m.conn.QueryRow(&resp, query, vid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultCustomerModel) FindAll(page, pageSize int64) ([]*Customer, error) {
	var limit string
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		limit = fmt.Sprintf(" limit %d, %d", offset, pageSize)
	} else {
		limit = ""
	}

	query := fmt.Sprintf("select %s from %s order by id %s", customerFieldNames, m.table, limit)
	var resp []*Customer
	err := m.conn.QueryRows(&resp, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultCustomerModel) Count() (int64, error) {
	query := fmt.Sprintf("select count(*) from %s", m.table)
	var count int64
	err := m.conn.QueryRow(&count, query)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultCustomerModel) Insert(data *Customer) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(customerRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.Name, data.Vid, data.Cid, data.Ip, data.Appid, data.Remark,
		data.Status, data.EndTime, now, now)
}

func (m *defaultCustomerModel) Update(data *Customer) error {
	query := fmt.Sprintf("update %s set name = ?, vid = ?, cid = ?, ip = ?, appid = ?, remark = ?, status = ?, end_time = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.Name, data.Vid, data.Cid, data.Ip, data.Appid, data.Remark,
		data.Status, data.EndTime, data.Id)
	return err
}

func (m *defaultCustomerModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultCustomerModel) FindByName(name string) ([]*Customer, error) {
	query := fmt.Sprintf("select %s from %s where name like ? ", customerFieldNames, m.table)
	var resp []*Customer
	err := m.conn.QueryRows(&resp, query, "%"+name+"%")
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultCustomerModel) Find(name string, vid, cid, beginEndTime, endEndTime, status int64, ip, orderByCreateTime, orderByUpdateTime string, page, pageSize int64) ([]*Customer, int64, error) {
	offset := (page - 1) * pageSize

	conditions := []string{}
	args := []interface{}{}

	if name != "" {
		conditions = append(conditions, "name like ?")
		args = append(args, "%"+name+"%")
	}
	if ip != "" {
		conditions = append(conditions, "ip like ?")
		args = append(args, "%"+ip+"%")
	}
	if vid > 0 {
		conditions = append(conditions, "vid = ?")
		args = append(args, vid)
	}
	if cid > 0 {
		conditions = append(conditions, "cid = ?")
		args = append(args, cid)
	}
	if beginEndTime > 0 && endEndTime > 0 {
		conditions = append(conditions, "end_time >= ? and end_time <= ?")
		args = append(args, beginEndTime, endEndTime)
	}
	if status >= 0 && status != 99 {
		conditions = append(conditions, "status = ?")
		args = append(args, status)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = " create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = " create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = " update_time " + orderByUpdateTime
	}

	query := fmt.Sprintf("SELECT %s FROM %s%s ORDER BY %s LIMIT ?, ?",
		customerFieldNames, m.table, whereClause, orderfield)
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)

	var resp []*Customer
	var count int64

	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultCustomerModel) FindVid(name string) ([]int64, error) {
	query := fmt.Sprintf("select vid from %s where name like ? ", m.table)
	var resp []int64
	err := m.conn.QueryRows(&resp, query, "%"+name+"%")
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultCustomerModel) FindCid(name string) ([]int64, error) {
	query := fmt.Sprintf("select cid from %s where name like ? ", m.table)
	var resp []int64
	err := m.conn.QueryRows(&resp, query, "%"+name+"%")
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultCustomerModel) UpdateStatus(id, status, endTime int64) error {
	query := fmt.Sprintf("update %s set status = ?, end_time = ? where id = ?", m.table)
	//query := fmt.Sprintf("update %s set status = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, status, endTime, id)
	return err
}

func (m *defaultCustomerModel) FindOneByNameNotLike(name string) (*Customer, error) {
	query := fmt.Sprintf("select %s from %s where name = ? ", customerFieldNames, m.table)
	var resp Customer
	err := m.conn.QueryRow(&resp, query, name)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultCustomerModel) FindOneByCid(cid int64) (*Customer, error) {
	query := fmt.Sprintf("select %s from %s where cid = ? ", customerFieldNames, m.table)
	var resp Customer
	err := m.conn.QueryRow(&resp, query, cid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

// FindByVids 根据Vid列表查询客户
func (m *defaultCustomerModel) FindByVids(vids []int64) ([]*Customer, error) {
	if len(vids) == 0 {
		return []*Customer{}, nil
	}

	// 构建IN查询条件
	placeholders := make([]string, len(vids))
	args := make([]interface{}, len(vids))
	for i, vid := range vids {
		placeholders[i] = "?"
		args[i] = vid
	}

	query := fmt.Sprintf("SELECT %s FROM %s WHERE vid IN (%s)", customerFieldNames, m.table, strings.Join(placeholders, ","))
	var resp []*Customer
	err := m.conn.QueryRows(&resp, query, args...)
	return resp, err
}
