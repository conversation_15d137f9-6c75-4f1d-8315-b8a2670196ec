package log

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	operationLogFieldNames  = strings.Join(operationLogRows, ",")
	operationLogRows        = []string{"id", "user_uuid", "username", "module", "operation", "entity_id", "entity_name", "old_value", "new_value", "ip", "user_agent", "create_time"}
	ErrOperationLogNotFound = errors.New("操作日志不存在")
)

type (
	OperationLogModel interface {
		FindOne(id int64) (*OperationLog, error)
		FindByUserUuid(userUuid string, page, pageSize int64) ([]*OperationLog, int64, error)
		FindByModule(module string, page, pageSize int64) ([]*OperationLog, int64, error)
		FindByEntityId(module string, entityId int64, page, pageSize int64) ([]*OperationLog, int64, error)
		Insert(data *OperationLog) (sql.Result, error)
		GetConn() sqlx.SqlConn // 添加获取连接的方法
	}

	defaultOperationLogModel struct {
		conn  sqlx.SqlConn
		table string
	}

	OperationLog struct {
		Id         int64     `db:"id"`
		UserUuid   string    `db:"user_uuid"`
		Username   string    `db:"username"`
		Module     string    `db:"module"`
		Operation  string    `db:"operation"`
		EntityId   int64     `db:"entity_id"`
		EntityName string    `db:"entity_name"`
		OldValue   string    `db:"old_value"`
		NewValue   string    `db:"new_value"`
		Ip         string    `db:"ip"`
		UserAgent  string    `db:"user_agent"`
		CreateTime time.Time `db:"create_time"`
	}
)

func NewOperationLogModel(conn sqlx.SqlConn) OperationLogModel {
	return &defaultOperationLogModel{
		conn:  conn,
		table: "operation_log",
	}
}

func (m *defaultOperationLogModel) FindOne(id int64) (*OperationLog, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", operationLogFieldNames, m.table)
	var resp OperationLog
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrOperationLogNotFound
	default:
		return nil, err
	}
}

func (m *defaultOperationLogModel) FindByUserUuid(userUuid string, page, pageSize int64) ([]*OperationLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where user_uuid = ? order by id desc limit ?, ?", operationLogFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where user_uuid = ?", m.table)

	var resp []*OperationLog
	var count int64
	err := m.conn.QueryRows(&resp, query, userUuid, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, userUuid)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultOperationLogModel) FindByModule(module string, page, pageSize int64) ([]*OperationLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where module = ? order by id desc limit ?, ?", operationLogFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where module = ?", m.table)

	var resp []*OperationLog
	var count int64
	err := m.conn.QueryRows(&resp, query, module, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, module)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultOperationLogModel) FindByEntityId(module string, entityId int64, page, pageSize int64) ([]*OperationLog, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	query := fmt.Sprintf("select %s from %s where module = ? and entity_id = ? order by id desc limit ?, ?", operationLogFieldNames, m.table)
	countQuery := fmt.Sprintf("select count(*) from %s where module = ? and entity_id = ?", m.table)

	var resp []*OperationLog
	var count int64
	err := m.conn.QueryRows(&resp, query, module, entityId, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	err = m.conn.QueryRow(&count, countQuery, module, entityId)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultOperationLogModel) Insert(data *OperationLog) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(operationLogRows[1:], ","))

	return m.conn.Exec(query, data.UserUuid, data.Username, data.Module, data.Operation,
		data.EntityId, data.EntityName, data.OldValue, data.NewValue, data.Ip, data.UserAgent,
		time.Now())
}

// GetConn 获取数据库连接
func (m *defaultOperationLogModel) GetConn() sqlx.SqlConn {
	return m.conn
}
