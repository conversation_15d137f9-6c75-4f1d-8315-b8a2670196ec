package hot

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	songHotTypeFieldNames  = strings.Join(songHotTypeRows, ",")
	songHotTypeRows        = []string{"id", "hot_type", "name", "status", "update_time", "create_time"}
	ErrSongHotTypeNotFound = errors.New("榜单类型不存在")
	SongHotTypeHad         = errors.New("已存在相同类型榜单")
)

const (
	Closed int64 = iota
	Opern
)

type (
	SongHotTypeModel interface {
		FindOne(id int64) (*HotTypeInfo, error)
		FindOneByType(hotType int64) (*HotTypeInfo, error)
		FindOneByName(name string) (*HotTypeInfo, error)
		FindAll() ([]*SongHotType, error)
		Find(hotType int64, name, orderByCreateTime, orderByUpdateTime string, status, page, pageSize int64) ([]*SongHotType, int64, error)
		CountByNameWithHotType(name string, hotType int64) (int64, error)
		Insert(data *SongHotType) (sql.Result, error)
		Update(data *SongHotType) error
		Delete(id int64) error
		CloseOrOpen(id int64, status int64) error
	}

	defaultSongHotTypeModel struct {
		conn  sqlx.SqlConn
		table string
	}

	HotTypeInfo struct {
		Id         int64     `db:"id"`
		HotType    int64     `db:"hot_type"`
		Name       string    `db:"name"`
		Status     int64     `db:"status"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
	}

	SongHotType struct {
		Id         int64     `db:"id"`
		HotType    int64     `db:"hot_type"`
		Name       string    `db:"name"`
		Status     int64     `db:"status"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
		Num        int64     `db:"num"`
	}
)

func NewSongHotTypeModel(conn sqlx.SqlConn) SongHotTypeModel {
	return &defaultSongHotTypeModel{
		conn:  conn,
		table: "song_hot_type",
	}
}

func (m *defaultSongHotTypeModel) FindOne(id int64) (*HotTypeInfo, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", songHotTypeFieldNames, m.table)
	var resp HotTypeInfo
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultSongHotTypeModel) FindOneByType(hotType int64) (*HotTypeInfo, error) {
	query := fmt.Sprintf("select %s from %s where hot_type = ? limit 1", songHotTypeFieldNames, m.table)
	var resp HotTypeInfo
	err := m.conn.QueryRow(&resp, query, hotType)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultSongHotTypeModel) FindOneByName(name string) (*HotTypeInfo, error) {
	query := fmt.Sprintf("select %s from %s where name = ? limit 1", songHotTypeFieldNames, m.table)
	var resp HotTypeInfo
	err := m.conn.QueryRow(&resp, query, name)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultSongHotTypeModel) FindAll() ([]*SongHotType, error) {
	query := fmt.Sprintf("select %s from %s where status = 1 order by hot_type asc", songHotTypeFieldNames, m.table)
	var resp []*SongHotType
	err := m.conn.QueryRows(&resp, query)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultSongHotTypeModel) Find(hotType int64, name, orderByCreateTime, orderByUpdateTime string, status, page, pageSize int64) ([]*SongHotType, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	conditions := []string{}
	args := []interface{}{}

	if hotType > 0 {
		conditions = append(conditions, "hot_type = ? ")
		args = append(args, hotType)
	}
	if name != "" {
		conditions = append(conditions, "name like ?")
		args = append(args, "%"+name+"%")
	}
	if status >= 0 && status != 99 {
		conditions = append(conditions, "status = ?")
		args = append(args, status)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = "create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = "create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = "update_time " + orderByUpdateTime
	}

	query := fmt.Sprintf(`select * ,  (SELECT COUNT(id) 
     FROM song_hot sh 
     WHERE sh.hot_type = sht.hot_type ) AS num 
		from %s sht %s
		order by sht.%s
		limit ?, ?`, m.table, whereClause, orderfield)

	var resp []*SongHotType
	var count int64
	queryArgs := append(args, offset, pageSize)
	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s%s", m.table, whereClause)
	// Get total count
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultSongHotTypeModel) Insert(data *SongHotType) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)",
		m.table, strings.Join(songHotTypeRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.HotType, data.Name, data.Status, now, now)
}

func (m *defaultSongHotTypeModel) CountByNameWithHotType(name string, hotType int64) (int64, error) {
	query := fmt.Sprintf(`select count(id)
		from %s 
		where name = ?  and hot_type = ? `, m.table)
	var count int64
	err := m.conn.QueryRow(&count, query, name, hotType)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultSongHotTypeModel) Update(data *SongHotType) error {
	query := fmt.Sprintf("update %s set name = ?, status = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.Name, data.Status, data.Id)
	return err
}

func (m *defaultSongHotTypeModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultSongHotTypeModel) CloseOrOpen(id int64, status int64) error {
	query := fmt.Sprintf("update %s set status = ? ,update_time = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, status, time.Now(), id)
	return err
}
