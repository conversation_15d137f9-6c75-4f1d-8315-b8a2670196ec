package hot

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var (
	songHotFieldNames  = strings.Join(songHotRows, ",")
	songHotRows        = []string{"id", "hot_type", "song_code", "vid", "num", "update_time", "create_time"}
	ErrSongHotNotFound = errors.New("热歌记录不存在")
	SongHotHad         = errors.New("当前榜单已存在相同歌曲")
	ErrAddSongHot      = errors.New("所选歌曲添加榜单失败")
	SongHotNumIsExist  = errors.New("填入的序号已存在，请重新填入")
)

type (
	SongHotModel interface {
		FindOne(id int64) (*SongHot, error)
		FindOneByNumWithType(hotType, num int64) (*SongHot, error)
		FindOneBySongCodeWithType(songCode, num int64) (*SongHot, error)
		FindHotSongsByType(hotType, vid int64, page, pageSize int64) ([]*SongHotWithSong, error)
		Find(hotType int64, songName, orderByCreateTime, orderByUpdateTime string, songCode, page, pageSize int64) ([]*SongHotWithSong, int64, error)
		CountByType(hotType, vid int64) (int64, error)
		CountByName(name string) (int64, error)
		CountBySongCodeWithHotType(songCode, hotType int64) (int64, error)
		Insert(data *SongHot) (sql.Result, error)
		Update(data *SongHot) error
		UpdateNum(data *SongHot) error
		Delete(id int64) error
		DeleteBySongCode(songCode int64) error
		FindAllHotSongsByType(hotType int64) ([]*SongHot, error)
		FindNum(hotType int64) ([]*SongHotWithSong, int64, error)
	}

	defaultSongHotModel struct {
		conn  sqlx.SqlConn
		table string
	}

	SongHot struct {
		Id         int64     `db:"id"`
		HotType    int64     `db:"hot_type"`
		SongCode   int64     `db:"song_code"`
		Vid        int64     `db:"vid"`
		Num        int64     `db:"num"`
		UpdateTime time.Time `db:"update_time"`
		CreateTime time.Time `db:"create_time"`
	}

	SongHotWithSong struct {
		SongHot
		SongName   string `db:"song_name"`
		SongSinger string `db:"song_singer"`
	}
)

func NewSongHotModel(conn sqlx.SqlConn) SongHotModel {
	return &defaultSongHotModel{
		conn:  conn,
		table: "song_hot",
	}
}

func (m *defaultSongHotModel) FindOne(id int64) (*SongHot, error) {
	query := fmt.Sprintf("select %s from %s where id = ? limit 1", songHotFieldNames, m.table)
	var resp SongHot
	err := m.conn.QueryRow(&resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrSongHotNotFound
	default:
		return nil, err
	}
}

func (m *defaultSongHotModel) FindOneByNumWithType(hotType, num int64) (*SongHot, error) {
	query := fmt.Sprintf("select %s from %s where hot_type = ? and num = ? limit 1", songHotFieldNames, m.table)
	var resp SongHot
	err := m.conn.QueryRow(&resp, query, hotType, num)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}

func (m *defaultSongHotModel) FindHotSongsByType(hotType, vid int64, page, pageSize int64) ([]*SongHotWithSong, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	orderDirection := "desc"
	if hotType != 0 {
		orderDirection = "asc" // 热度值榜单按热度降序，其他榜单按num升序
	}

	query := fmt.Sprintf(`select h.id, h.hot_type, h.song_code, h.vid, h.num, h.update_time, h.create_time,
		s.name as song_name, s.singer as song_singer
		from %s h
		inner join song s on h.song_code = s.song_code
		where h.hot_type = ? and h.vid = ? and s.status = 1
		order by h.update_time %s
		limit ?, ?`, m.table, orderDirection)

	var resp []*SongHotWithSong
	err := m.conn.QueryRows(&resp, query, hotType, vid, offset, pageSize)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultSongHotModel) Find(hotType int64, songName, orderByCreateTime, orderByUpdateTime string, songCode, page, pageSize int64) ([]*SongHotWithSong, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	conditions := []string{}
	args := []interface{}{}

	if hotType > 0 {
		conditions = append(conditions, "h.hot_type = ? ")
		args = append(args, hotType)
	}

	if songName != "" {
		conditions = append(conditions, "s.name like ?")
		args = append(args, "%"+songName+"%")
	}
	if songCode != 0 {
		conditions = append(conditions, "h.song_code = ?")
		args = append(args, songCode)
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	var orderfield string

	if orderByCreateTime == "" && orderByUpdateTime == "" {
		orderfield = "create_time DESC"
	}

	if orderByCreateTime != "" {
		orderfield = "create_time " + orderByCreateTime
	}

	if orderByUpdateTime != "" {
		orderfield = "update_time " + orderByUpdateTime
	}

	query := fmt.Sprintf(`select h.id, h.hot_type, h.song_code, h.vid, h.num, h.update_time, h.create_time,
		s.name as song_name, s.singer as song_singer
		from %s h
		inner join song s on h.song_code = s.song_code
	    %s 
		order by h.%s
		limit ?, ?`, m.table, whereClause, orderfield)

	var resp []*SongHotWithSong
	var count int64

	queryArgs := append(args, offset, pageSize)

	err := m.conn.QueryRows(&resp, query, queryArgs...)
	if err != nil {
		return nil, 0, err
	}

	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s as h inner join song s on h.song_code = s.song_code  %s", m.table, whereClause)
	// Get total count
	err = m.conn.QueryRow(&count, countQuery, args...)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultSongHotModel) CountByType(hotType, vid int64) (int64, error) {
	query := fmt.Sprintf(`select count(h.id)
		from %s h
		inner join song s on h.song_code = s.song_code
		where h.hot_type = ? and h.vid = ? and s.status = 1`, m.table)
	var count int64
	err := m.conn.QueryRow(&count, query, hotType, vid)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultSongHotModel) CountByName(name string) (int64, error) {
	query := fmt.Sprintf(`select count(h.id)
		from %s h
		inner join song s on h.song_code = s.song_code
		where h.name = ? `, m.table)
	var count int64
	err := m.conn.QueryRow(&count, query, name)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultSongHotModel) CountBySongCodeWithHotType(songCode, hotType int64) (int64, error) {
	query := fmt.Sprintf(`select count(id)
		from %s 
		where song_code = ?  and hot_type = ? `, m.table)
	var count int64
	err := m.conn.QueryRow(&count, query, songCode, hotType)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (m *defaultSongHotModel) Insert(data *SongHot) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)",
		m.table, strings.Join(songHotRows[1:], ","))
	now := time.Now()
	return m.conn.Exec(query, data.HotType, data.SongCode, data.Vid, data.Num, now, now)
}

func (m *defaultSongHotModel) UpdateNum(data *SongHot) error {
	query := fmt.Sprintf("update %s set num = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.Num, data.Id)
	return err
}

func (m *defaultSongHotModel) Update(data *SongHot) error {
	query := fmt.Sprintf("update %s set song_code = ?,vid = ? , num = ? where id = ?", m.table)
	_, err := m.conn.Exec(query, data.SongCode, data.Vid, data.Num, data.Id)
	return err
}

func (m *defaultSongHotModel) Delete(id int64) error {
	query := fmt.Sprintf("delete from %s where id = ?", m.table)
	_, err := m.conn.Exec(query, id)
	return err
}

func (m *defaultSongHotModel) DeleteBySongCode(songCode int64) error {
	query := fmt.Sprintf("delete from %s where song_code = ?", m.table)
	_, err := m.conn.Exec(query, songCode)
	return err
}

func (m *defaultSongHotModel) FindAllHotSongsByType(hotType int64) ([]*SongHot, error) {

	query := fmt.Sprintf(`select * from %s where h.hot_type = ?`, m.table)

	var resp []*SongHot
	err := m.conn.QueryRows(&resp, query, hotType)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *defaultSongHotModel) FindNum(hotType int64) ([]*SongHotWithSong, int64, error) {
	query := fmt.Sprintf(`select h.id, h.hot_type, h.song_code, h.vid, h.num, h.update_time, h.create_time,
		s.name as song_name, s.singer as song_singer
		from %s h
		inner join song s on h.song_code = s.song_code
		where h.hot_type = ? and s.status = 1 order by num desc`, m.table)

	var resp []*SongHotWithSong
	var count int64

	err := m.conn.QueryRows(&resp, query, hotType)
	if err != nil {
		return nil, 0, err
	}

	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s as h inner join song s on h.song_code = s.song_code where h.hot_type = ?", m.table)
	// Get total count
	err = m.conn.QueryRow(&count, countQuery, hotType)
	if err != nil {
		return nil, 0, err
	}

	return resp, count, nil
}

func (m *defaultSongHotModel) FindOneBySongCodeWithType(songCode, hotType int64) (*SongHot, error) {
	query := fmt.Sprintf("select %s from %s where song_code = ? and hot_type = ? limit 1", songHotFieldNames, m.table)
	var resp SongHot
	err := m.conn.QueryRow(&resp, query, songCode, hotType)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, nil
	default:
		return nil, err
	}
}
