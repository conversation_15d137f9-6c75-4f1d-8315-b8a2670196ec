package playrecord

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"music/model/playrecord"

	"github.com/tealeg/xlsx"
	"github.com/zeromicro/go-zero/core/logx"
)

// ImportPlayRecordsFromExcel 从Excel或CSV文件导入播放记录数据
func ImportPlayRecordsFromExcelV1(recordModel playrecord.PlayRecordModel, filePath, failedOutputPath string) (*ImportResult, error) {
	result := &ImportResult{
		FailedRecords: make(map[int]string),
	}

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(filePath))

	var records []*playrecord.PlayRecord
	var headers []string
	var err error

	switch ext {
	case ".csv":
		records, headers, err = importFromCSVV1(filePath)
		logx.Infof("CSV获取成功，记录数: %d", len(records))
		logx.Infof("CSV获取成功，数据: %+v", records)
	//case ".xlsx", ".xls":
	//	records, err = importFromExcel(filePath)
	default:
		return result, fmt.Errorf("不支持的文件格式: %s, 请使用CSV文件(.csv)", ext)
	}

	if err != nil {
		return result, fmt.Errorf("导入文件失败: %w", err)
	}

	result.TotalCount = len(records)

	if len(records) == 0 {
		return result, fmt.Errorf("没有找到有效的播放记录数据")
	}

	// 批量导入到数据库
	//successCount, failedRecords, err := recordModel.BatchImport(records)
	successCount, failedRecords, err := recordModel.BatchImportGoroutineWithTx(records)
	if err != nil {
		return result, fmt.Errorf("导入播放记录失败: %w", err)
	}

	result.SuccessCount = successCount
	result.FailedCount = len(failedRecords)

	// 如果有失败记录且指定了输出路径，记录失败详情
	if len(failedRecords) > 0 && failedOutputPath != "" {
		if err := writeFailedRecordsToExcel(records, failedRecords, headers, failedOutputPath); err != nil {
			result.ErrorMessage = fmt.Sprintf("写入失败记录时出错: %s", err.Error())
		}
	}

	return result, nil
}

// importFromCSV 从CSV文件导入数据
func importFromCSVV1(filePath string) ([]*playrecord.PlayRecord, []string, error) {
	// 打开CSV文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, nil, fmt.Errorf("无法打开CSV文件: %w", err)
	}
	defer file.Close()

	/*  尝试 新的读取 csv */
	// 创建带缓冲的读取器
	reader := bufio.NewReader(file)

	// 检测文件编码
	data, _ := reader.Peek(1024)
	isUTF8 := true
	for _, b := range data {
		if b == 0xEF || b == 0xBB || b == 0xBF {
			continue // UTF-8 BOM
		}
		if b > 0x7F {
			isUTF8 = false
			break
		}
	}

	// 创建适当的reader
	var csvReader *csv.Reader
	if isUTF8 {
		file.Seek(0, 0) // 重置文件指针到开始位置
		csvReader = csv.NewReader(reader)
	} else {
		file.Seek(0, 0) // 重置文件指针到开始位置
		gbkReader := transform.NewReader(reader, simplifiedchinese.GBK.NewDecoder())
		csvReader = csv.NewReader(gbkReader)
	}

	// 读取第一行来检测分隔符
	firstLine, err := csvReader.Read()
	if err != nil && err != io.EOF {
		return nil, nil, fmt.Errorf("读取第一行失败: %w", err)
	}

	// 检测分隔符
	separator := detectSeparatorV2(strings.Join(firstLine, ""))

	// 重新打开文件以确保从头开始读取
	file.Close()
	file, err = os.Open(filePath)
	if err != nil {
		return nil, nil, fmt.Errorf("重新打开文件失败: %w", err)
	}
	defer file.Close()

	// 重新创建reader
	reader = bufio.NewReader(file)
	if isUTF8 {
		csvReader = csv.NewReader(reader)
	} else {
		gbkReader := transform.NewReader(reader, simplifiedchinese.GBK.NewDecoder())
		csvReader = csv.NewReader(gbkReader)
	}

	// 设置CSV reader的配置
	csvReader.Comma = separator
	csvReader.LazyQuotes = true
	csvReader.TrimLeadingSpace = true

	// 读取表头
	headers, err := csvReader.Read()
	if err != nil {
		return nil, nil, fmt.Errorf("读取CSV表头失败: %w", err)
	}

	logx.Infof("CSV表头: %v", headers)
	logx.Infof("使用的分隔符: %c", separator)

	/*// 创建CSV reader
	reader := csv.NewReader(file)
	reader.Comma = '\t' // 设置分隔符为制表符

	// 读取表头
	headers, err := reader.Read()
	if err != nil {
		return nil, nil, fmt.Errorf("读取CSV表头失败: %w", err)
	}
	*/
	logx.Infof("CSV表头: %v", headers)

	// 创建列名到索引的映射
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[strings.TrimSpace(header)] = i
	}

	// 检查必要的列是否存在
	/*	requiredColumns := []string{"lts", "vid", "songCode"}
		for _, col := range requiredColumns {
			if _, exists := headerMap[col]; !exists {
				return nil, nil, fmt.Errorf("CSV文件缺少必要的列: %s", col)
			}
		}*/

	var records []*playrecord.PlayRecord
	lineNum := 1 // 从1开始，因为0是表头

	// 逐行读取数据
	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logx.Errorf("读取第%d行时出错: %v", lineNum, err)
			continue
		}

		// 解析行数据为PlayRecord对象
		record, err := parseCSVRowToPlayRecordV1(row, headerMap)
		if err != nil {
			logx.Errorf("解析第%d行数据失败: %v", lineNum, err)
			continue
		}

		logx.Infof("解析第%d行数据成功: %+v", lineNum, record)

		records = append(records, record)
		lineNum++
	}

	return records, headers, nil
}

// parseCSVRowToPlayRecord 解析CSV行数据为PlayRecord对象
func parseCSVRowToPlayRecordOld(row []string, headerMap map[string]int) (*playrecord.PlayRecord, error) {
	record := &playrecord.PlayRecord{}

	// 解析时间戳 (lts)
	if idx, ok := headerMap["lts"]; ok && idx < len(row) {
		lts, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析时间戳失败: %v", err)
		}
		record.Lts = lts
	}

	// 解析客户ID (vid)
	if idx, ok := headerMap["vid"]; ok && idx < len(row) {
		vid, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析客户ID失败: %v", err)
		}
		record.Vid = vid
	}

	// 设置唯一ID (uniqueId)
	if idx, ok := headerMap["uniqueId"]; ok && idx < len(row) {
		record.UniqueId = strings.TrimSpace(row[idx])
	}

	// 解析版权方ID (cpId)
	if idx, ok := headerMap["cpId"]; ok && idx < len(row) {
		cpId, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err == nil {
			record.CpId = cpId
		}
	}

	// 解析歌曲编码 (songCode)
	if idx, ok := headerMap["songCode"]; ok && idx < len(row) {
		songCode, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析歌曲编码失败: %v", err)
		}
		record.SongCode = songCode
	}

	// 解析歌曲时长 (songDuration)
	if idx, ok := headerMap["songDuration"]; ok && idx < len(row) {
		duration, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.SongDuration = duration
		}
	}

	// 解析播放时长 (playDuration)
	if idx, ok := headerMap["playDuration"]; ok && idx < len(row) {
		duration, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.PlayDuration = duration
		}
	}

	// 解析开始时间 (startTime)
	if idx, ok := headerMap["startTime"]; ok && idx < len(row) {
		startTime, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err == nil {
			record.StartTime = startTime
		}
	}

	// 解析结束时间 (endTime)
	if idx, ok := headerMap["endTime"]; ok && idx < len(row) {
		endTime, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err == nil {
			record.EndTime = endTime
		}
	}

	// 解析播放位置 (playbackPos)
	if idx, ok := headerMap["playbackPos"]; ok && idx < len(row) {
		pos, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.PlaybackPos = pos
		}
	}

	// 解析播放次数 (playNum)
	if idx, ok := headerMap["playNum"]; ok && idx < len(row) {
		num, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil && num > 0 {
			record.PlayNum = num
		} else {
			record.PlayNum = 1 // 默认为1次
		}
	} else {
		record.PlayNum = 1 // 默认为1次
	}

	// 解析场景类型 (sceneType)
	if idx, ok := headerMap["sceneType"]; ok && idx < len(row) {
		sceneType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.SceneType = sceneType
		}
	}

	// 解析播放类型 (playType)
	if idx, ok := headerMap["playType"]; ok && idx < len(row) {
		playType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.PlayType = playType
		}
	}

	// 解析免费类型 (freeType)
	if idx, ok := headerMap["freeType"]; ok && idx < len(row) {
		freeType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.FreeType = freeType
		}
	}

	// 解析记录类型 (recordType)
	if idx, ok := headerMap["recordType"]; ok && idx < len(row) {
		recordType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.RecordType = recordType
		}
	}

	// 解析使用类型 (useType)
	if idx, ok := headerMap["useType"]; ok && idx < len(row) {
		useType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.UseType = useType
		}
	}

	// 设置创建时间
	record.CreateTime = time.Now()

	// 设置推送状态为默认值0
	record.PushStatus = 0

	return record, nil
}

func parseCSVRowToPlayRecordV1(row []string, headerMap map[string]int) (*playrecord.PlayRecord, error) {
	record := &playrecord.PlayRecord{}

	// 检查行数据是否为空
	if len(row) == 0 {
		return nil, fmt.Errorf("空行数据")
	}

	logx.Infof("解析行数据: %v", row)

	// 解析时间戳 (lts)
	if len(row) > 0 {
		if value := strings.TrimSpace(row[0]); value != "" {
			if lts, err := strconv.ParseInt(value, 10, 64); err == nil {
				record.Lts = lts
			}
		}
	}

	// 解析客户ID (vid)
	if len(row) > 1 {
		if value := strings.TrimSpace(row[1]); value != "" {
			if vid, err := strconv.ParseInt(value, 10, 64); err == nil {
				record.Vid = vid
			}
		}
	}

	// 设置唯一ID (uniqueId)
	if len(row) > 2 {
		record.UniqueId = strings.TrimSpace(row[2])
	}

	// 解析版权方ID (cpId)
	if len(row) > 3 {
		if value := strings.TrimSpace(row[3]); value != "" {
			if cpId, err := strconv.ParseInt(value, 10, 64); err == nil {
				record.CpId = cpId
			}
		}
	}

	// 解析歌曲编号 (songCode)
	if len(row) > 4 {
		if value := strings.TrimSpace(row[4]); value != "" {
			if songCode, err := strconv.ParseInt(value, 10, 64); err == nil {
				record.SongCode = songCode
			}
		}
	}

	// 解析歌曲时长 (songDuration)
	if len(row) > 5 {
		if value := strings.TrimSpace(row[5]); value != "" {
			if duration, err := strconv.Atoi(value); err == nil {
				record.SongDuration = duration
			}
		}
	}

	// 解析播放时长 (playDuration)
	if len(row) > 6 {
		if value := strings.TrimSpace(row[6]); value != "" {
			if duration, err := strconv.Atoi(value); err == nil {
				record.PlayDuration = duration
			}
		}
	}

	// 解析开始时间 (startTime)
	if len(row) > 7 {
		if value := strings.TrimSpace(row[7]); value != "" {
			if startTime, err := strconv.ParseInt(value, 10, 64); err == nil {
				record.StartTime = startTime
			}
		}
	}

	// 解析结束时间 (endTime)
	if len(row) > 8 {
		if value := strings.TrimSpace(row[8]); value != "" {
			if endTime, err := strconv.ParseInt(value, 10, 64); err == nil {
				record.EndTime = endTime
			}
		}
	}

	// 解析播放位置 (playbackPos)
	if len(row) > 9 {
		if value := strings.TrimSpace(row[9]); value != "" {
			if pos, err := strconv.Atoi(value); err == nil {
				record.PlaybackPos = pos
			}
		}
	}

	// 解析播放次数 (playNum)
	if len(row) > 10 {
		if value := strings.TrimSpace(row[10]); value != "" {
			if num, err := strconv.Atoi(value); err == nil && num > 0 {
				record.PlayNum = num
			}
		}
	}

	// 解析场景类型 (sceneType)
	if len(row) > 11 {
		if value := strings.TrimSpace(row[11]); value != "" {
			if sceneType, err := strconv.Atoi(value); err == nil {
				record.SceneType = sceneType
			}
		}
	}

	// 解析播放类型 (playType)
	if len(row) > 12 {
		if value := strings.TrimSpace(row[12]); value != "" {
			if playType, err := strconv.Atoi(value); err == nil {
				record.PlayType = playType
			}
		}
	}

	// 解析免费类型 (freeType)
	if len(row) > 13 {
		if value := strings.TrimSpace(row[13]); value != "" {
			if freeType, err := strconv.Atoi(value); err == nil {
				record.FreeType = freeType
			}
		}
	}

	// 解析记录类型 (recordType)
	if len(row) > 14 {
		if value := strings.TrimSpace(row[14]); value != "" {
			if recordType, err := strconv.Atoi(value); err == nil {
				record.RecordType = recordType
			}
		}
	}

	// 解析使用类型 (useType)
	if len(row) > 15 {
		if value := strings.TrimSpace(row[15]); value != "" {
			if useType, err := strconv.Atoi(value); err == nil {
				record.UseType = useType
			}
		}
	}

	// 设置创建时间和推送状态
	record.CreateTime = time.Now()
	record.PushStatus = 0

	return record, nil
}

// isEmptyRow 检查是否为空行
func isEmptyRow(row *xlsx.Row) bool {
	if len(row.Cells) == 0 {
		return true
	}

	for _, cell := range row.Cells {
		if strings.TrimSpace(cell.String()) != "" {
			return false
		}
	}
	return true
}

// parseExcelRowToPlayRecord 解析Excel行为PlayRecord对象
func parseExcelRowToPlayRecord(row *xlsx.Row, headerMap map[string]int) (*playrecord.PlayRecord, error) {
	record := &playrecord.PlayRecord{}

	// 提取并验证必要字段：播放时间
	if idx, ok := headerMap["播放时间"]; ok && idx < len(row.Cells) {
		timeStr := strings.TrimSpace(row.Cells[idx].String())
		if timeStr == "" {
			return nil, fmt.Errorf("播放时间不能为空")
		}

		// 尝试解析不同格式的时间
		timestamp, err := parseTimeToTimestamp(timeStr)
		if err != nil {
			return nil, fmt.Errorf("播放时间格式错误: %s", err.Error())
		}
		record.Lts = timestamp
	} else {
		return nil, fmt.Errorf("播放时间字段缺失")
	}

	// 提取并验证必要字段：歌曲编码
	if idx, ok := headerMap["歌曲编码"]; ok && idx < len(row.Cells) {
		songCodeStr := strings.TrimSpace(row.Cells[idx].String())
		if songCodeStr == "" {
			return nil, fmt.Errorf("歌曲编码不能为空")
		}

		songCode, err := strconv.ParseInt(songCodeStr, 10, 64)
		if err != nil || songCode <= 0 {
			return nil, fmt.Errorf("歌曲编码必须为正整数: %s", songCodeStr)
		}
		record.SongCode = songCode
	} else {
		return nil, fmt.Errorf("歌曲编码字段缺失")
	}

	// 提取并验证必要字段：客户ID
	if idx, ok := headerMap["客户ID"]; ok && idx < len(row.Cells) {
		vidStr := strings.TrimSpace(row.Cells[idx].String())
		if vidStr == "" {
			return nil, fmt.Errorf("客户ID不能为空")
		}

		vid, err := strconv.ParseInt(vidStr, 10, 64)
		if err != nil || vid <= 0 {
			return nil, fmt.Errorf("客户ID必须为正整数: %s", vidStr)
		}
		record.Vid = vid
	} else {
		return nil, fmt.Errorf("客户ID字段缺失")
	}

	// 提取可选字段：版权方ID
	if idx, ok := headerMap["版权方ID"]; ok && idx < len(row.Cells) {
		cpIdStr := strings.TrimSpace(row.Cells[idx].String())
		if cpIdStr != "" {
			cpId, err := strconv.ParseInt(cpIdStr, 10, 64)
			if err == nil {
				record.CpId = cpId
			}
		}
	}

	// 提取可选字段：歌曲时长
	if idx, ok := headerMap["歌曲时长"]; ok && idx < len(row.Cells) {
		durationStr := strings.TrimSpace(row.Cells[idx].String())
		if durationStr != "" {
			duration, err := strconv.Atoi(durationStr)
			if err == nil {
				record.SongDuration = duration
			}
		}
	}

	// 提取可选字段：播放时长
	if idx, ok := headerMap["播放时长"]; ok && idx < len(row.Cells) {
		playDurationStr := strings.TrimSpace(row.Cells[idx].String())
		if playDurationStr != "" {
			playDuration, err := strconv.Atoi(playDurationStr)
			if err == nil {
				record.PlayDuration = playDuration
			}
		}
	}

	// 提取可选字段：播放位置
	if idx, ok := headerMap["播放位置"]; ok && idx < len(row.Cells) {
		posStr := strings.TrimSpace(row.Cells[idx].String())
		if posStr != "" {
			pos, err := strconv.Atoi(posStr)
			if err == nil {
				record.PlaybackPos = pos
			}
		}
	}

	// 提取可选字段：播放次数
	if idx, ok := headerMap["播放次数"]; ok && idx < len(row.Cells) {
		numStr := strings.TrimSpace(row.Cells[idx].String())
		if numStr != "" {
			num, err := strconv.Atoi(numStr)
			if err == nil && num > 0 {
				record.PlayNum = num
			} else {
				record.PlayNum = 1 // 默认为1次
			}
		} else {
			record.PlayNum = 1 // 默认为1次
		}
	} else {
		record.PlayNum = 1 // 默认为1次
	}

	// 提取可选字段：场景类型
	if idx, ok := headerMap["场景类型"]; ok && idx < len(row.Cells) {
		sceneTypeStr := strings.TrimSpace(row.Cells[idx].String())
		if sceneTypeStr != "" {
			sceneType, err := strconv.Atoi(sceneTypeStr)
			if err == nil {
				record.SceneType = sceneType
			}
		}
	}

	// 提取可选字段：播放类型
	if idx, ok := headerMap["播放类型"]; ok && idx < len(row.Cells) {
		playTypeStr := strings.TrimSpace(row.Cells[idx].String())
		if playTypeStr != "" {
			playType, err := strconv.Atoi(playTypeStr)
			if err == nil {
				record.PlayType = playType
			}
		}
	}

	// 提取可选字段：免费类型
	if idx, ok := headerMap["免费类型"]; ok && idx < len(row.Cells) {
		freeTypeStr := strings.TrimSpace(row.Cells[idx].String())
		if freeTypeStr != "" {
			freeType, err := strconv.Atoi(freeTypeStr)
			if err == nil {
				record.FreeType = freeType
			}
		}
	}

	// 提取可选字段：记录类型
	if idx, ok := headerMap["记录类型"]; ok && idx < len(row.Cells) {
		recordTypeStr := strings.TrimSpace(row.Cells[idx].String())
		if recordTypeStr != "" {
			recordType, err := strconv.Atoi(recordTypeStr)
			if err == nil {
				record.RecordType = recordType
			}
		}
	}

	// 提取可选字段：使用类型
	if idx, ok := headerMap["使用类型"]; ok && idx < len(row.Cells) {
		useTypeStr := strings.TrimSpace(row.Cells[idx].String())
		if useTypeStr != "" {
			useType, err := strconv.Atoi(useTypeStr)
			if err == nil {
				record.UseType = useType
			}
		}
	}

	// 提取可选字段：用户ID
	if idx, ok := headerMap["用户ID"]; ok && idx < len(row.Cells) {
		record.Uid = strings.TrimSpace(row.Cells[idx].String())
	}

	// 提取可选字段：渠道ID
	if idx, ok := headerMap["渠道ID"]; ok && idx < len(row.Cells) {
		record.ChannelId = strings.TrimSpace(row.Cells[idx].String())
	}

	// 提取可选字段：请求IP
	if idx, ok := headerMap["请求IP"]; ok && idx < len(row.Cells) {
		record.RequestIp = strings.TrimSpace(row.Cells[idx].String())
	}

	// 生成唯一ID
	randNum := time.Now().UnixNano() % 10000
	record.UniqueId = fmt.Sprintf("%d_%d_%d", record.SongCode, record.Lts, randNum)

	// 设置创建时间和推送状态
	record.CreateTime = time.Now()
	record.PushStatus = 0 // 默认未推送

	return record, nil
}

// parseTimeToTimestamp 将多种时间格式转换为时间戳
func parseTimeToTimestamp(timeStr string) (int64, error) {
	// 尝试不同的时间格式
	formats := []string{
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
		"2006-01-02",
		"2006/01/02",
		"01/02/2006",
		"01-02-2006",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t.Unix(), nil
		}
	}

	// 尝试直接解析为时间戳
	if timestamp, err := strconv.ParseInt(timeStr, 10, 64); err == nil {
		return timestamp, nil
	}

	return 0, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// writeFailedRecordsToExcel 将失败记录写入Excel文件
func writeFailedRecordsToExcel(records []*playrecord.PlayRecord, failedRecords map[int]string, headerRow []string, outputPath string) error {
	// 创建新Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("失败记录")
	if err != nil {
		return err
	}

	// 复制原始表头并添加"失败原因"列
	newHeaderRow := sheet.AddRow()
	for _, cell := range headerRow {
		newHeaderRow.AddCell().SetString(cell)
	}
	newHeaderRow.AddCell().SetString("失败原因")

	// 写入失败记录
	for idx, reason := range failedRecords {
		if idx >= 0 && idx < len(records) {
			record := records[idx]

			// 创建新行
			row := sheet.AddRow()

			// 添加"播放时间"列
			timeCell := row.AddCell()
			timeCell.SetString(time.Unix(record.Lts, 0).Format("2006-01-02 15:04:05"))

			// 添加"歌曲编码"列
			row.AddCell().SetInt64(record.SongCode)

			// 添加"客户ID"列
			row.AddCell().SetInt64(record.Vid)

			// 添加"版权方ID"列
			row.AddCell().SetInt64(record.CpId)

			// 添加"歌曲时长"列
			row.AddCell().SetInt(record.SongDuration)

			// 添加"播放时长"列
			row.AddCell().SetInt(record.PlayDuration)

			// 添加"播放位置"列
			row.AddCell().SetInt(record.PlaybackPos)

			// 添加"播放次数"列
			row.AddCell().SetInt(record.PlayNum)

			// 添加"场景类型"列
			row.AddCell().SetInt(record.SceneType)

			// 添加"播放类型"列
			row.AddCell().SetInt(record.PlayType)

			// 添加"免费类型"列
			row.AddCell().SetInt(record.FreeType)

			// 添加"记录类型"列
			row.AddCell().SetInt(record.RecordType)

			// 添加"使用类型"列
			row.AddCell().SetInt(record.UseType)

			// 添加"用户ID"列
			row.AddCell().SetString(record.Uid)

			// 添加"渠道ID"列
			row.AddCell().SetString(record.ChannelId)

			// 添加"请求IP"列
			row.AddCell().SetString(record.RequestIp)

			// 添加"失败原因"列
			row.AddCell().SetString(reason)
		}
	}

	// 保存文件
	return file.Save(outputPath)
}

// CreatePlayRecordTemplate 创建播放记录导入模板
func CreatePlayRecordTemplate(outputPath string) error {
	// 创建Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("播放记录导入模板")
	if err != nil {
		return err
	}

	// 添加表头
	headers := []string{
		"播放时间", "歌曲编码", "客户ID", "版权方ID",
		"歌曲时长", "播放时长", "播放位置", "播放次数",
		"场景类型", "播放类型", "免费类型", "记录类型",
		"使用类型", "用户ID", "渠道ID", "请求IP",
	}

	headerRow := sheet.AddRow()
	for _, header := range headers {
		cell := headerRow.AddCell()
		cell.SetString(header)
	}

	// 添加表头样式
	// 如果需要设置样式，可以在这里添加

	// 添加示例行
	exampleRow := sheet.AddRow()
	exampleRow.AddCell().SetString("2023-01-01 12:00:00") // 播放时间
	exampleRow.AddCell().SetInt(10001)                    // 歌曲编码
	exampleRow.AddCell().SetInt(1001)                     // 客户ID
	exampleRow.AddCell().SetInt(101)                      // 版权方ID
	exampleRow.AddCell().SetInt(180)                      // 歌曲时长(秒)
	exampleRow.AddCell().SetInt(120)                      // 播放时长(秒)
	exampleRow.AddCell().SetInt(0)                        // 播放位置
	exampleRow.AddCell().SetInt(1)                        // 播放次数
	exampleRow.AddCell().SetInt(0)                        // 场景类型
	exampleRow.AddCell().SetInt(1)                        // 播放类型
	exampleRow.AddCell().SetInt(0)                        // 免费类型
	exampleRow.AddCell().SetInt(0)                        // 记录类型
	exampleRow.AddCell().SetInt(0)                        // 使用类型
	exampleRow.AddCell().SetString("user123")             // 用户ID
	exampleRow.AddCell().SetString("channel001")          // 渠道ID
	exampleRow.AddCell().SetString("***********")         // 请求IP

	// 添加说明行
	sheet.AddRow() // 空行
	noteRow := sheet.AddRow()
	noteCell := noteRow.AddCell()
	noteCell.SetString("填表说明：")

	explanations := []string{
		"1. 播放时间：必填，支持格式如 2023-01-01 12:00:00 或 2023-01-01",
		"2. 歌曲编码：必填，必须是正整数",
		"3. 客户ID：必填，必须是正整数",
		"4. 播放次数：选填，默认为1",
		"5. 场景类型：选填，0-普通播放，1-背景音乐，2-直播",
		"6. 播放类型：选填，1-在线播放，2-离线播放",
		"7. 免费类型：选填，0-付费，1-免费",
		"8. 使用类型：选填，0-普通用户，1-VIP",
	}

	for _, exp := range explanations {
		expRow := sheet.AddRow()
		expRow.AddCell().SetString(exp)
	}

	// 保存文件
	return file.Save(outputPath)
}
