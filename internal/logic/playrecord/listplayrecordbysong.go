package playrecord

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

type ListPlayRecordGroupBySongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListPlayRecordGroupBySongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListPlayRecordGroupBySongLogic {
	return &ListPlayRecordGroupBySongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListPlayRecordGroupBySongLogic) ListPlayRecordGroupBySong(req *types.SumPlayRecordBySongReq) (*types.SumPlayRecordResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	if req.Page == 0 {
		req.Page = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	// 查询指定歌曲的播放记录
	songCode := make([]int64, 0)

	if req.SongName != "" || req.SongCode > 0 {
		// 根据 歌曲名称 以及 歌曲编号  查询 歌曲
		songs, err := l.svcCtx.SongModel.FindByNameOrSongCode(req.SongName, req.SongCode)
		if err != nil {
			return nil, err
		}

		if songs == nil {
			return nil, errors.New(types.ErrNoSongInfo)
		}

		for _, song := range songs {
			songCode = append(songCode, song.SongCode)
		}
	}

	// 查询指定歌曲的播放记录
	list, count, err := l.svcCtx.PlayRecordModel.FindGroupBySong(req.LtsTime, req.PlayType, songCode, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.New(types.ErrQuerySongPlayRecord)
	}

	if list == nil {
		return new(types.SumPlayRecordResp), nil
	}

	// 优化2: 提取需要查询的唯一ID集合
	songCodes := make(map[int64]struct{})
	cpIds := make(map[int64]struct{})

	for _, v := range list {
		songCodes[v.SongCode] = struct{}{}
		cpIds[v.CpId] = struct{}{}
	}

	// 优化3: 只查询需要的歌曲信息
	songMap := make(map[int64]string)
	songDurationMap := make(map[int64]int64)
	if len(songCodes) > 0 {
		// 这里假设SongModel有FindByCodes方法，如果没有，需要添加
		songs, err := l.svcCtx.SongModel.FindByCodes(keysFromMap(songCodes))
		if err != nil {
			return nil, errors.New(types.ErrInsertSongName)
		}
		for _, s := range songs {
			songMap[s.SongCode] = s.Name
			songDurationMap[s.SongCode] = s.Duration
		}
	}

	// 优化4: 只查询需要的版权方信息
	cpMap := make(map[int64]string)
	if len(cpIds) > 0 {
		// 这里假设CpModel有FindByIds方法，如果没有，需要添加
		cps, err := l.svcCtx.CpModel.FindByIds(keysFromMap(cpIds))
		if err != nil {
			return nil, errors.New(types.ErrInsertCpName)
		}
		for _, c := range cps {
			cpMap[c.Id] = c.Name
		}
	}

	sumPlayRecords := make([]types.SumPlayRecord, 0)

	for _, v := range list {

		sumPlayRecords = append(sumPlayRecords, types.SumPlayRecord{
			LtsTime:           req.LtsTime,
			PlayType:          v.PlayType,
			SongCode:          v.SongCode,
			SongName:          songMap[v.SongCode],
			SongDuration:      songDurationMap[v.SongCode],
			CpId:              v.CpId,
			CpName:            cpMap[v.CpId],
			PlayDurationTotal: v.PlayDurationTotal,
			PlayNumTotal:      v.PlayNumTotal,
		})
	}

	return &types.SumPlayRecordResp{
		Total: count,
		List:  sumPlayRecords,
	}, nil
}
