package playrecord

import (
	"bytes"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"io/ioutil"
	"music/internal/types"
	"music/model/customer"
	"music/model/playrecord"
	"music/model/song"
	"time"
)

// MakeAssociationPlayRecord 组装音集协的播放记录
func MakeAssociationPlayRecord(songModel song.SongModel, customerModel customer.CustomerModel, playRecords []*playrecord.PlayRecord) ([]types.AssociationPlayRecord, error) {
	associationPlayRecords := make([]types.AssociationPlayRecord, 0, len(playRecords))

	// 获取 song 信息
	// 1. 提取所有需要查询的歌曲编码
	songCodes := make(map[int64]struct{})
	songCodesList := make([]int64, 0)
	vids := make(map[int64]struct{})
	vidList := make([]int64, 0)
	for _, record := range playRecords {
		songCodes[record.SongCode] = struct{}{}
		songCodesList = append(songCodesList, record.SongCode)

		vids[record.Vid] = struct{}{}
		vidList = append(vidList, record.Vid)
	}

	// 2. 调用模型批量获取歌曲信息
	songs, err := songModel.FindByCodes(songCodesList)
	if err != nil {
		return nil, fmt.Errorf("获取歌曲信息失败: %w", err)
	}

	// 3. 构建歌曲信息映射，便于快速查找
	songMap := make(map[int64]*song.Song)
	for _, s := range songs {
		songMap[s.SongCode] = s
	}

	// 获取 customer 信息
	// 1. 批量查询customer信息
	customers, err := customerModel.FindByVids(vidList)

	// 2. 构建客户信息映射，便于快速查找
	customerMap := make(map[int64]*customer.Customer)
	for _, s := range customers {
		customerMap[s.Vid] = s
	}

	for _, playRecord := range playRecords {
		// 从映射中获取歌曲信息
		songInfo := songMap[playRecord.SongCode]

		// 如果找不到歌曲信息，使用播放记录中的基本信息
		var songName, songSinger string
		songDuration := playRecord.SongDuration

		// 如果找到歌曲信息，优先使用歌曲表中的详细信息
		if songInfo != nil {
			songName = songInfo.Name
			songSinger = songInfo.Singer
		}

		var companyName string
		if customerMap[playRecord.Vid] != nil {
			companyName = customerMap[playRecord.Vid].Name
		}

		utcStartTime := time.Unix(playRecord.StartTime, 0).UTC().Format("2006/01/02 15:04:05")
		// 转成 东八区 时间
		bjStartTime := time.Unix(playRecord.StartTime, 0).In(time.FixedZone("CST", 8*3600)).Format("2006/01/02 15:04:05")

		associationPlayRecord := types.AssociationPlayRecord{
			RequestUuid:      playRecord.UniqueId,
			SongCode:         playRecord.SongCode,
			SongLibrary:      0, // 不传
			SongName:         songName,
			SongWordAuthor:   "", // 不传
			SongTuneAuthor:   "", // 不传
			SongSinger:       songSinger,
			PlatId:           1, // 默认值
			PlatName:         "默认平台",
			ClientId:         0, // 不传
			ClientName:       "默认客户端",
			RoomType:         0,  // 用 sceneType 字段替代
			RoomId:           "", // 不传，有隐私风险
			RoomName:         "", // 不传，有隐私风险
			RoomMasterId:     "", // 不传，有隐私风险
			RoomMasterName:   "", // 不传，有隐私风险
			AnchorOrPlatform: 2,  // 默认值，平台调用
			MonthlyGear:      1,  // 默认值，按次调用
			// startTime转成 UTC+8
			PlayBeginTime:             bjStartTime,
			PlayDuration:              int64(playRecord.PlayDuration),
			PlayMode:                  int64(playRecord.PlayType), // 默认值，原唱模式
			IsClip:                    0,                          // 不传，对计量计费没用处
			IsDrm:                     0,                          // 不传，对计量计费没用处
			OriginalPlayDuration:      int64(songDuration),
			AccompanimentPlayDuration: 0, // 不传，无论是原唱还是伴唱，歌曲时长都用 originalPlayDuration 字段
			IsCallSuccess:             1, // 默认值，调用成功
			Status:                    1, // 默认值，有效状态
			// startTime转成 UTC+8
			CallTime:             bjStartTime,
			IsFirstThirtySeconds: 0,                         // 默认值，不传，这个没有用处
			EffectiveUseNum:      int64(playRecord.PlayNum), // 本次调用的有效次数，例如播放多次都根据计量规则算好了是多次
			MusicUserType:        0,                         // 不传，用 sceneType 字段替代
			Cid:                  playRecord.CpId,
			Vid:                  playRecord.Vid,
			CompanyName:          companyName, // 客户名称-声网客户名称全称
			// 使用 UTC 时间格式
			PlayBeginTimeUTC: utcStartTime,
			SceneType:        int64(playRecord.SceneType),
		}

		// 如果找到歌曲信息，添加额外的歌曲属性
		if songInfo != nil {
			// 添加歌曲类型信息
			associationPlayRecord.SongLibrary = songInfo.Type

			// 添加版权方信息
			if songInfo.VendorId > 0 {
				associationPlayRecord.Cid = songInfo.VendorId
			}

			// 添加DRM信息
			if songInfo.DrmPath != "" {
				associationPlayRecord.IsDrm = 1
			}
		}

		associationPlayRecords = append(associationPlayRecords, associationPlayRecord)
	}
	return associationPlayRecords, nil

}

// 组装音集协的播放记录头部信息
func makeAssociationPlayRecordHeader() ([]interface{}, error) {
	header := []interface{}{
		"requestUuid",
		"songCode",
		"songLibrary",
		"songName",
		"songWordAuthor",
		"songTuneAuthor",
		"songSinger",
		"platId",
		"platName",
		"clientId",
		"clientName",
		"roomType",
		"roomId",
		"roomName",
		"roomMasterId",
		"roomMasterName",
		"anchorOrPlatform",
		"monthlyGear",
		"playBeginTime",
		"playDuration",
		"playMode",
		"isClip",
		"isDrm",
		"originalPlayDuration",
		"accompanimentPlayDuration",
		"isCallSuccess",
		"status",
		"callTime",
		"isFirstThirtySeconds",
		"effectiveUseNum",
		"musicUserType",
		"cid",
		"vid",
		"companyName",
		"playBeginTime-UTC",
		"sceneType",
	}
	return header, nil
}

// 生成文件
func GenerateAssociationPlayRecordFileName() string {
	currentTime := time.Now()
	// 格式化为 "YYYYMMDD_HHMMSS" 格式
	return fmt.Sprintf("play_record_%s.csv", currentTime.Format("20060102_150405"))
}

// 组装文件
func MakeAssociationPlayRecordFileContent(associationPlayRecords []types.AssociationPlayRecord) ([][]interface{}, error) {
	var fileContent [][]interface{}

	// 添加头部信息
	header, err := makeAssociationPlayRecordHeader()
	if err != nil {
		return nil, err
	}
	fileContent = append(fileContent, header)

	// 添加播放记录数据
	for _, record := range associationPlayRecords {
		row := []interface{}{
			record.RequestUuid,
			record.SongCode,
			record.SongLibrary,
			record.SongName,
			record.SongWordAuthor,
			record.SongTuneAuthor,
			record.SongSinger,
			record.PlatId,
			record.PlatName,
			record.ClientId,
			record.ClientName,
			record.RoomType,
			record.RoomId,
			record.RoomName,
			record.RoomMasterId,
			record.RoomMasterName,
			record.AnchorOrPlatform,
			record.MonthlyGear,
			record.PlayBeginTime,
			record.PlayDuration,
			record.PlayMode,
			record.IsClip,
			record.IsDrm,
			record.OriginalPlayDuration,
			record.AccompanimentPlayDuration,
			record.IsCallSuccess,
			record.Status,
			record.CallTime,
			record.IsFirstThirtySeconds,
			record.EffectiveUseNum,
			record.MusicUserType,
			record.Cid,
			record.Vid,
			record.CompanyName,
			record.PlayBeginTimeUTC,
			record.SceneType,
		}
		fileContent = append(fileContent, row)
	}
	return fileContent, nil
}

// 生成本地文件
func GenerateAssociationPlayRecordLocalFile(fileName string, fileContent [][]interface{}) error {
	err := WriteXlsxFile(fileName, fileContent)
	if err != nil {
		return fmt.Errorf("生成本地文件失败: %w", err)
	}
	return nil
}

func WriteXlsxFile(fileName string, fileContent [][]interface{}) error {
	file := excelize.NewFile()
	sheet := "敖拜"

	var currentRowIdx int = 1

	for _, row := range fileContent {
		if len(row) == 0 {
			continue // 跳过空行
		}

		cell := fmt.Sprintf("A%d", currentRowIdx)
		file.SetSheetRow(sheet, cell, &row)

		currentRowIdx++
	}

	// 将文件写入内存
	var buffer bytes.Buffer
	if err := file.Write(&buffer); err != nil {
		return fmt.Errorf("写入内存失败: %w", err)
	}

	// 将内存中的内容写入文件
	if err := ioutil.WriteFile(fileName, buffer.Bytes(), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil

}
