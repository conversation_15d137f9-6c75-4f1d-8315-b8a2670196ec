package playrecord

import (
	"context"
	"fmt"
	"github.com/tealeg/xlsx"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/customer"
	"music/model/playrecord"
	"music/model/song"
	"path/filepath"
	"time"
)

type MakeAssociationPlayRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMakeAssociationPlayRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MakeAssociationPlayRecordLogic {
	return &MakeAssociationPlayRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// MakeAssociationPlayRecord 组装音集协的播放记录
func (l *MakeAssociationPlayRecordLogic) MakeAssociationPlayRecord(songModel song.SongModel, customerModel customer.CustomerModel, playRecords []*playrecord.PlayRecord) ([]types.AssociationPlayRecord, error) {
	associationPlayRecords := make([]types.AssociationPlayRecord, 0, len(playRecords))

	songCodes := make(map[int64]struct{})
	songCodesList := make([]int64, 0)
	vids := make(map[int64]struct{})
	vidList := make([]int64, 0)
	// 获取 song 信息
	// 1. 提取所有需要查询的歌曲编码
	for _, record := range playRecords {
		songCodes[record.SongCode] = struct{}{}
		songCodesList = append(songCodesList, record.SongCode)

		vids[record.Vid] = struct{}{}
		vidList = append(vidList, record.Vid)
	}

	// 2. 调用模型批量获取歌曲信息
	songs, err := songModel.FindByCodes(songCodesList)
	if err != nil {
		return nil, fmt.Errorf("获取歌曲信息失败: %w", err)
	}

	// 3. 构建歌曲信息映射，便于快速查找
	songMap := make(map[int64]*song.Song)
	for _, s := range songs {
		songMap[s.SongCode] = s
	}

	// 获取 customer 信息
	// 1. 批量查询customer信息
	customers, err := customerModel.FindByVids(vidList)

	// 2. 构建客户信息映射，便于快速查找
	customerMap := make(map[int64]*customer.Customer)
	for _, s := range customers {
		customerMap[s.Vid] = s
	}

	for _, playRecord := range playRecords {
		// 从映射中获取歌曲信息
		songInfo := songMap[playRecord.SongCode]

		// 如果找不到歌曲信息，使用播放记录中的基本信息
		var songName, songSinger string
		songDuration := playRecord.SongDuration

		// 如果找到歌曲信息，优先使用歌曲表中的详细信息
		if songInfo != nil {
			songName = songInfo.Name
			songSinger = songInfo.Singer
		}

		var companyName string
		if customerMap[playRecord.Vid] != nil {
			companyName = customerMap[playRecord.Vid].Name
		}

		utcStartTime := time.Unix(playRecord.StartTime, 0).UTC().Format("2006/01/02 15:04:05")
		// 转成 东八区 时间
		bjStartTime := time.Unix(playRecord.StartTime, 0).In(time.FixedZone("CST", 8*3600)).Format("2006/01/02 15:04:05")

		associationPlayRecord := types.AssociationPlayRecord{
			RequestUuid:      playRecord.UniqueId,
			SongCode:         playRecord.SongCode,
			SongLibrary:      0, // 不传
			SongName:         songName,
			SongWordAuthor:   "", // 不传
			SongTuneAuthor:   "", // 不传
			SongSinger:       songSinger,
			PlatId:           1, // 默认值
			PlatName:         "默认平台",
			ClientId:         0, // 不传
			ClientName:       "默认客户端",
			RoomType:         0,  // 用 sceneType 字段替代
			RoomId:           "", // 不传，有隐私风险
			RoomName:         "", // 不传，有隐私风险
			RoomMasterId:     "", // 不传，有隐私风险
			RoomMasterName:   "", // 不传，有隐私风险
			AnchorOrPlatform: 2,  // 默认值，平台调用
			MonthlyGear:      1,  // 默认值，按次调用
			// startTime转成 UTC+8
			PlayBeginTime:             bjStartTime,
			PlayDuration:              int64(playRecord.PlayDuration),
			PlayMode:                  int64(playRecord.PlayType), // 默认值，原唱模式
			IsClip:                    0,                          // 不传，对计量计费没用处
			IsDrm:                     0,                          // 不传，对计量计费没用处
			OriginalPlayDuration:      int64(songDuration),
			AccompanimentPlayDuration: 0, // 不传，无论是原唱还是伴唱，歌曲时长都用 originalPlayDuration 字段
			IsCallSuccess:             1, // 默认值，调用成功
			Status:                    1, // 默认值，有效状态
			// startTime转成 UTC+8
			CallTime:             bjStartTime,
			IsFirstThirtySeconds: 0,                         // 默认值，不传，这个没有用处
			EffectiveUseNum:      int64(playRecord.PlayNum), // 本次调用的有效次数，例如播放多次都根据计量规则算好了是多次
			MusicUserType:        0,                         // 不传，用 sceneType 字段替代
			Cid:                  playRecord.CpId,
			Vid:                  playRecord.Vid,
			CompanyName:          companyName, // 客户名称-声网客户名称全称
			// 使用 UTC 时间格式
			PlayBeginTimeUTC: utcStartTime,
			SceneType:        int64(playRecord.SceneType),
		}

		// 如果找到歌曲信息，添加额外的歌曲属性
		if songInfo != nil {
			// 添加歌曲类型信息
			associationPlayRecord.SongLibrary = songInfo.Type

			// 添加版权方信息
			if songInfo.VendorId > 0 {
				associationPlayRecord.Cid = songInfo.VendorId
			}

			// 添加DRM信息
			if songInfo.DrmPath != "" {
				associationPlayRecord.IsDrm = 1
			}
		}

		associationPlayRecords = append(associationPlayRecords, associationPlayRecord)
	}

	// 生成文件 .xlsx
	// 1. 获取 header
	header, err := makeAssociationPlayRecordHeader()
	if err != nil {
		return nil, fmt.Errorf("获取头部信息失败: %w", err)
	}

	// 2. 生成文件路径
	outputPath := filepath.Join(l.svcCtx.Config.File.ExportPath, fmt.Sprintf("association_play_record_%s.xlsx", time.Now().Format("2006-01-02_15:04")))

	// 2. 生成文件
	if err := writeAssociationPlayRecordToExcel(associationPlayRecords, header, outputPath); err != nil {
		return nil, fmt.Errorf("生成文件失败: %w", err)
	}

	return associationPlayRecords, nil

}

// 组装音集协的播放记录头部信息
func makeAssociationPlayRecordHeader() ([]interface{}, error) {
	header := []interface{}{
		"requestUuid",
		"songCode",
		"songLibrary",
		"songName",
		"songWordAuthor",
		"songTuneAuthor",
		"songSinger",
		"platId",
		"platName",
		"clientId",
		"clientName",
		"roomType",
		"roomId",
		"roomName",
		"roomMasterId",
		"roomMasterName",
		"anchorOrPlatform",
		"monthlyGear",
		"playBeginTime",
		"playDuration",
		"playMode",
		"isClip",
		"isDrm",
		"originalPlayDuration",
		"accompanimentPlayDuration",
		"isCallSuccess",
		"status",
		"callTime",
		"isFirstThirtySeconds",
		"effectiveUseNum",
		"musicUserType",
		"cid",
		"vid",
		"companyName",
		"playBeginTime-UTC",
		"sceneType",
	}
	return header, nil
}

// 写入文件
func writeAssociationPlayRecordToExcel(associationPlayRecords []types.AssociationPlayRecord, header []interface{}, outputPath string) error {
	// 创建新Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("敖拜")
	if err != nil {
		return err
	}

	// 添加表头
	headerRow := sheet.AddRow()
	for _, cell := range header {
		headerRow.AddCell().SetString(fmt.Sprintf("%v", cell))
	}

	// 添加数据
	for _, record := range associationPlayRecords {
		row := sheet.AddRow()
		row.AddCell().SetString(record.RequestUuid)
		row.AddCell().SetInt64(record.SongCode)
		row.AddCell().SetInt64(record.SongLibrary)
		row.AddCell().SetString(record.SongName)
		row.AddCell().SetString(record.SongWordAuthor)
		row.AddCell().SetString(record.SongTuneAuthor)
		row.AddCell().SetString(record.SongSinger)
		row.AddCell().SetInt64(record.PlatId)
		row.AddCell().SetString(record.PlatName)
		row.AddCell().SetInt64(record.ClientId)
		row.AddCell().SetString(record.ClientName)
		row.AddCell().SetInt64(record.RoomType)
		row.AddCell().SetString(record.RoomId)
		row.AddCell().SetString(record.RoomName)
		row.AddCell().SetString(record.RoomMasterId)
		row.AddCell().SetString(record.RoomMasterName)
		row.AddCell().SetInt64(record.AnchorOrPlatform)
		row.AddCell().SetInt64(record.MonthlyGear)
		row.AddCell().SetString(record.PlayBeginTime)
		row.AddCell().SetInt64(record.PlayDuration)
		row.AddCell().SetInt64(record.PlayMode)
		row.AddCell().SetInt64(record.IsClip)
		row.AddCell().SetInt64(record.IsDrm)
		row.AddCell().SetInt64(record.OriginalPlayDuration)
		row.AddCell().SetInt64(record.AccompanimentPlayDuration)
		row.AddCell().SetInt64(record.IsCallSuccess)
		row.AddCell().SetInt64(record.Status)
		row.AddCell().SetString(record.CallTime)
		row.AddCell().SetInt64(record.IsFirstThirtySeconds)
		row.AddCell().SetInt64(record.EffectiveUseNum)
		row.AddCell().SetInt64(record.MusicUserType)
		row.AddCell().SetInt64(record.Cid)
		row.AddCell().SetInt64(record.Vid)
		row.AddCell().SetString(record.CompanyName)
		row.AddCell().SetString(record.PlayBeginTimeUTC)
		row.AddCell().SetInt64(record.SceneType)
	}

	// 保存文件
	return file.Save(outputPath)
}
