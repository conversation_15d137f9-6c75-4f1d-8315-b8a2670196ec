package playrecord

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

type ListPlayRecordGroupByVidLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListPlayRecordGroupByVidLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListPlayRecordGroupByVidLogic {
	return &ListPlayRecordGroupByVidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListPlayRecordGroupByVidLogic) ListPlayRecordGroupByVid(req *types.SumPlayRecordByVidReq) (*types.SumPlayRecordResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	if req.Page == 0 {
		req.Page = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	// 查询指定歌曲的播放记录
	vids := make([]int64, 0)

	if req.CustomerName != "" {
		// 根据 客户名称 以及 客户编号  查询 客户
		customers, err := l.svcCtx.CustomerModel.FindByName(req.CustomerName)
		if err != nil {
			return nil, err
		}

		if customers == nil {
			return nil, errors.New(types.ErrNoCustomerInfo)
		}

		for _, customer := range customers {
			vids = append(vids, customer.Vid)
		}
	}
	if req.Vid != 0 {
		vids = append(vids, req.Vid)
	}

	// 查询指定歌曲的播放记录
	list, count, err := l.svcCtx.PlayRecordModel.FindGroupByCustomer(req.LtsTime, req.PlayType, vids, req.UseType, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.New(types.ErrQueryCustomerPlayRecord)
	}

	if list == nil {
		return new(types.SumPlayRecordResp), nil
	}

	// 优化2: 提取需要查询的唯一ID集合
	customerVids := make(map[int64]struct{})

	for _, v := range list {
		customerVids[v.Vid] = struct{}{}
	}

	// 优化3: 只查询需要的客户信息
	customerMap := make(map[int64]string)
	if len(customerVids) > 0 {
		// 这里假设CustomerModel有FindByVids方法，如果没有，需要添加
		customers, err := l.svcCtx.CustomerModel.FindByVids(keysFromMap(customerVids))
		if err != nil {
			return nil, errors.New(types.ErrInsertCustomerName)
		}
		for _, c := range customers {
			customerMap[c.Vid] = c.Name
		}
	}

	sumPlayRecords := make([]types.SumPlayRecord, 0)

	for _, v := range list {

		sumPlayRecords = append(sumPlayRecords, types.SumPlayRecord{
			LtsTime:           req.LtsTime,
			PlayType:          v.PlayType,
			CustomerName:      customerMap[v.Vid],
			Vid:               v.Vid,
			UseType:           v.UseType,
			PlayDurationTotal: v.PlayDurationTotal,
			PlayNumTotal:      v.PlayNumTotal,
		})
	}

	return &types.SumPlayRecordResp{
		Total: count,
		List:  sumPlayRecords,
	}, nil
}
