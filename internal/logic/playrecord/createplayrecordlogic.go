package playrecord

import (
	"context"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/playrecord"
)

type CreatePlayRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreatePlayRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePlayRecordLogic {
	return &CreatePlayRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreatePlayRecordLogic) CreatePlayRecord(req *types.CreatePlayRecordReq) (*types.CreatePlayRecordResp, error) {
	// 验证必填参数
	if req.Lts == 0 {
		return &types.CreatePlayRecordResp{
			Success: false,
			Message: "时间戳不能为空",
		}, nil
	}

	if req.UniqueId == "" {
		return &types.CreatePlayRecordResp{
			Success: false,
			Message: "唯一ID不能为空",
		}, nil
	}

	if req.SongCode == 0 {
		return &types.CreatePlayRecordResp{
			Success: false,
			Message: "歌曲编号不能为空",
		}, nil
	}

	// 检查唯一ID是否已存在
	_, err := l.svcCtx.PlayRecordModel.FindOneByUniqueId(req.UniqueId)
	if err == nil {
		return &types.CreatePlayRecordResp{
			Success: false,
			Message: fmt.Sprintf("唯一ID %s 已存在", req.UniqueId),
		}, nil
	} else if err != playrecord.ErrPlayRecordNotFound {
		return nil, err
	}

	// 创建播放记录
	record := &playrecord.PlayRecord{
		Lts:          req.Lts,
		Vid:          req.Vid,
		UniqueId:     req.UniqueId,
		RequestIp:    req.RequestIp,
		CpId:         req.CpId,
		SongCode:     req.SongCode,
		SongDuration: req.SongDuration,
		PlayDuration: req.PlayDuration,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		PlaybackPos:  req.PlaybackPos,
		PlayNum:      req.PlayNum,
		SceneType:    req.SceneType,
		PlayType:     req.PlayType,
		FreeType:     req.FreeType,
		RecordType:   req.RecordType,
		UseType:      req.UseType,
		Uid:          req.Uid,
		ChannelId:    req.ChannelId,
		PushStatus:   0, // 默认未推送
		PushTime:     0,
	}

	result, err := l.svcCtx.PlayRecordModel.Insert(record)
	if err != nil {
		return nil, err
	}

	insertId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &types.CreatePlayRecordResp{
		Id:      insertId,
		Success: true,
		Message: "添加成功",
	}, nil
}
