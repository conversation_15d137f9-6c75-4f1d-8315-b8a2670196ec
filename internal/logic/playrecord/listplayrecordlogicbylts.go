package playrecord

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

type ListPlayRecordGroupByLtsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListPlayRecordGroupByLtsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListPlayRecordGroupByLtsLogic {
	return &ListPlayRecordGroupByLtsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListPlayRecordGroupByLtsLogic) ListPlayRecordGroupByLts(req *types.SumPlayRecordReq) (*types.SumPlayRecordResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	if req.Page == 0 {
		req.Page = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	var startTimeUnix int64
	var endTimeUnix int64
	var err error

	startTime := middleware.DefaultMonthToUnixStartTime(middleware.MonthLimit)
	if req.StartDate == "" && req.EndDate == "" && req.Month == "" {
		startTimeUnix = startTime
		endTimeUnix = middleware.NowToUnixStartTime()
	}

	if req.StartDate != "" && req.EndDate != "" {
		startTimeUnix, err = middleware.BeginDateToUnixStartTime(req.StartDate, middleware.MonthLimit)
		if err != nil {
			return nil, err
		}
		endTimeUnix, err = middleware.EndDateToUnixStartTime(req.EndDate)
		if err != nil {
			return nil, err
		}
	}

	if req.Month != "" {
		startTimeUnix, endTimeUnix, err = middleware.MonthConvertToBeginAndEndToUnixStartTime(req.Month, middleware.MonthLimit)
		if err != nil {
			return nil, err
		}
	}

	if req.PlayType == 0 {
		return nil, errors.New(types.ErrNotPlayType)
	}

	// 查询指定歌曲的播放记录
	songCode := make([]int64, 0)

	if req.SongName != "" || req.SongCode > 0 {
		// 根据 歌曲名称 以及 歌曲编号  查询 歌曲
		songs, err := l.svcCtx.SongModel.FindByNameOrSongCode(req.SongName, req.SongCode)
		if err != nil {
			return nil, err
		}

		if songs == nil {
			return nil, errors.New(types.ErrNoSongInfo)
		}

		for _, song := range songs {
			songCode = append(songCode, song.SongCode)
		}
	}

	// 查询指定歌曲的播放记录
	vids := make([]int64, 0)

	if req.CustomerName != "" {
		// 根据 客户名称 以及 客户编号  查询 客户
		customers, err := l.svcCtx.CustomerModel.FindByName(req.CustomerName)
		if err != nil {
			return nil, err
		}

		if customers == nil {
			return nil, errors.New(types.ErrNoCustomerInfo)
		}

		for _, customer := range customers {
			vids = append(vids, customer.Vid)
		}
	}

	if req.Vid != 0 {
		vids = append(vids, req.Vid)
	}

	// 查询数据
	list, count, err := l.svcCtx.PlayRecordModel.FindByPlayTypeGroupByLtsV4(startTimeUnix, endTimeUnix, req.PlayType, vids, req.UseType, songCode, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.New(types.ErrQuerySumPlayRecord)
	}

	if list == nil {
		return new(types.SumPlayRecordResp), nil
	}

	record, err := l.svcCtx.PlayRecordModel.GetLatestPlayRecord(req.PlayType)
	if err != nil {
		return nil, errors.New(types.ErrQuerySumPlayRecord)
	}
	var lastUpdateTime string

	if record != nil {
		//lastUpdateTime = middleware.UnixMilliToTime(record.PushTime)
		lastUpdateTime = middleware.UnixMilliToTime(record.Lts)
	}

	sumPlayRecords := make([]types.SumPlayRecord, 0)

	for _, v := range list {
		// 获取歌曲信息
		/*song, songErr := l.svcCtx.SongModel.FindOneBySongCode(v.SongCode)
		if songErr != nil {
			return nil, errors.New(types.ErrInsertSongName)
		}
		v.SongName = song.Name

		// 获取版权方信息
		cp, cpErr := l.svcCtx.CpModel.FindOne(v.CpId)
		if cpErr != nil {
			return nil, errors.New(types.ErrInsertCpName)
		}
		v.CpName = cp.Name

		// 获取客户信息
		customer, customerErr := l.svcCtx.CustomerModel.FindOne(v.Vid)
		if customerErr != nil {
			return nil, errors.New(types.ErrInsertCustomerName)
		}
		v.CustomerName = customer.Name*/

		sumPlayRecords = append(sumPlayRecords, types.SumPlayRecord{
			LtsTime:  v.LtsTime,
			PlayType: v.PlayType,
			//SongCode:          v.SongCode,
			//SongName:          v.SongName,
			//SongDuration:      v.SongDuration,
			//CpId:              v.CpId,
			//CpName:            v.CpName,
			//Vid:               v.Vid,
			//CustomerName:      v.CustomerName,
			//UseType:           v.UseType,
			PlayDurationTotal: v.PlayDurationTotal,
			PlayNumTotal:      v.PlayNumTotal,
		})
	}

	return &types.SumPlayRecordResp{
		Total:          count,
		List:           sumPlayRecords,
		LastUpdateTime: lastUpdateTime,
	}, nil
}
