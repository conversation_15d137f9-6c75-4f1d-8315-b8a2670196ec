package playrecord

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/model/song"

	"music/internal/svc"
	"music/internal/types"
)

type ListPlayRecordSongsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListPlayRecordSongsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListPlayRecordSongsLogic {
	return &ListPlayRecordSongsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListPlayRecordSongsLogic) ListPlayRecordSongs(req *types.PlayRecordSongListReq) (*types.ListSongForHotResp, error) {
	var songs []types.SongInfoForHot
	var total int64
	var err error
	// 执行查询
	songCodeList, count, err := l.svcCtx.PlayRecordModel.FindSongCodeByVid(req.LtsTime, req.PlayType, req.Vid, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	total = int64(count)

	if len(songCodeList) >= 0 {
		songList, err := l.svcCtx.SongModel.FindByCodes(songCodeList)
		if err != nil {
			return nil, err
		}

		songs = l.convertSongList(songList)
	} else {
		return nil, errors.New(types.ErrNoSongInfo)
	}

	return &types.ListSongForHotResp{
		Total: total,
		List:  songs,
	}, nil
}

func (l *ListPlayRecordSongsLogic) convertSongList(songList []*song.Song) []types.SongInfoForHot {
	var songs []types.SongInfoForHot
	for _, s := range songList {
		songs = append(songs, types.SongInfoForHot{
			Id:           s.Id,
			ImportId:     s.ImportId,
			Type:         s.Type,
			VendorId:     s.VendorId,
			SongCode:     s.SongCode,
			Name:         s.Name,
			Singer:       s.Singer,
			VendorSongId: s.VendorSongId,
			CloseTime:    s.CloseTime,
			Status:       s.Status,
		})
	}
	return songs
}
