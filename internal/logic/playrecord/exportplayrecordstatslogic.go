package playrecord

import (
	"archive/zip"
	"context"
	"errors"
	"fmt"
	"io"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
	"os"
	"path/filepath"
	"sort"
	"time"

	"github.com/tealeg/xlsx"
	"github.com/zeromicro/go-zero/core/logx"
)

type ExportPlayRecordStatsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportPlayRecordStatsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportPlayRecordStatsLogic {
	return &ExportPlayRecordStatsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportPlayRecordStatsLogic) ExportPlayRecordStats(req *types.ExportPlayRecordStatsReq) (*types.ExportPlayRecordStatsResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	if req.PlayType != 1 && req.PlayType != 2 && req.PlayType != 3 {
		return nil, errors.New("请选择正确的播放类型导出")
	}

	// 确保导出目录存在
	exportDir := filepath.FromSlash(l.svcCtx.Config.File.ExportPath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return nil, errors.New(types.ErrCreateDir)
	}

	// 设置临时目录
	tempDirName := fmt.Sprintf("playRecordStats_%s", time.Now().Format("20060102150405"))
	tempDir := filepath.Join(exportDir, tempDirName)
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, errors.New(types.ErrCreateDir)
	}
	defer os.RemoveAll(tempDir)

	// 根据播放类型导出不同的数据
	var filePaths []string
	var err error
	var customerNameMap = make(map[int64]types.ExportCustomerInfo)
	var vid []int64

	if req.CustomerName != "" {
		customers, err := l.svcCtx.CustomerModel.FindByName(req.CustomerName)
		if err != nil {
			return nil, errors.New("获取客户信息失败")
		}

		for _, customer := range customers {
			customerNameMap[customer.Vid] = types.ExportCustomerInfo{
				Cid:  customer.Cid,
				Name: customer.Name,
			}
			vid = append(vid, customer.Vid)
		}
	} else if req.Vid == 0 {
		customers, err := l.svcCtx.CustomerModel.FindAll(0, 0)
		if err != nil {
			return nil, errors.New("获取客户信息失败")
		}

		for _, customer := range customers {
			customerNameMap[customer.Vid] = types.ExportCustomerInfo{
				Cid:  customer.Cid,
				Name: customer.Name,
			}
			vid = append(vid, customer.Vid)
		}
	}

	if req.Vid != 0 {
		// 获取单个客户信息
		customer, err := l.svcCtx.CustomerModel.FindOneByVid(req.Vid)
		if err != nil {
			return nil, errors.New("获取客户信息失败")
		}

		customerNameMap[customer.Vid] = types.ExportCustomerInfo{
			Cid:  customer.Cid,
			Name: customer.Name,
		}
		vid = append(vid, customer.Vid)

	}

	var startTimeUTC, endTimeUTC time.Time
	var startTimeBJ, endTimeBJ time.Time
	//var startTimeUnix, endTimeUnix int64

	// 根据导出类型处理时间范围
	switch req.ExportType {
	case 1: // 按月份导出
		if req.Month == "" {
			return nil, errors.New("月份不能为空")
		}
		startTimeUTC, endTimeUTC, err = l.validateMonth(req.Month, types.UTC)
		startTimeBJ, endTimeBJ, err = l.validateMonth(req.Month, types.BJ)
	case 2: // 按日期范围导出
		if req.BeginDate == "" || req.EndDate == "" {
			return nil, errors.New("开始日期和结束日期不能为空")
		}
		startTimeUTC, endTimeUTC, err = l.validateDates([]string{req.BeginDate, req.EndDate}, types.UTC)
		startTimeBJ, endTimeBJ, err = l.validateDates([]string{req.BeginDate, req.EndDate}, types.BJ)
	case 3: // 按指定日期导出
		if len(req.Dates) == 0 {
			return nil, errors.New("日期列表不能为空")
		}
		startTimeUTC, endTimeUTC, err = l.validateDates(req.Dates, types.UTC)
		startTimeBJ, endTimeBJ, err = l.validateDates(req.Dates, types.BJ)
	case 4: // 导出所有
		// 获取 三个月前的 一号
		currentTime := time.Now()
		threeMonthsAgo := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, currentTime.Location()).AddDate(0, middleware.MonthLimit, 0)

		startTimeUTC, endTimeUTC, err = l.validateDates([]string{threeMonthsAgo.Format(time.DateOnly), currentTime.Format(time.DateOnly)}, types.UTC)
		startTimeBJ, endTimeBJ, err = l.validateDates([]string{threeMonthsAgo.Format(time.DateOnly), currentTime.Format(time.DateOnly)}, types.BJ)
	default:
		return nil, errors.New("不支持的导出类型")
	}

	if err != nil {
		return nil, err
	}

	var prefix string
	var isClip string

	if req.PlayType == 1 { // DRM
		//prefix = "DRM_"
	}

	if req.PlayType == 2 { // 明文
		//prefix = "明文_"
	}

	if req.PlayType == 3 { // 副歌
		//prefix = "副歌_"
		isClip = "_isClip"
	}

	//logx.Infof("开始导出播放记录统计数据: %s 至 %s, 时区: %s, 客户数量: %d", startTimeUTC.Format("2006-01-02"), endTimeUTC.Format("2006-01-02"), types.UTC, len(customerNameMap))
	//logx.Infof("vid 数量: %d", len(vid))
	//logx.Infof("vid : %+v", vid)

	statsUTC, err := l.getPlayStats(startTimeUTC.Unix(), endTimeUTC.Unix(), req.Dates, req.PlayType, vid, req.UseType, types.UTC, customerNameMap)
	if err != nil {
		return nil, fmt.Errorf("获取播放统计失败: %w", err)
	}
	filePathUTC := filepath.Join(tempDir, fmt.Sprintf("%s%s_UTC%s.xlsx", prefix, fmt.Sprintf("%s至%s", startTimeUTC.Format("2006-01-02"), endTimeUTC.Format("2006-01-02")), isClip))
	if err := l.exportStatsToExcel(statsUTC, filePathUTC, isClip); err != nil {
		return nil, err
	}
	filePaths = append(filePaths, filePathUTC)

	statsBJ, err := l.getPlayStats(startTimeBJ.Unix(), endTimeBJ.Unix(), req.Dates, req.PlayType, vid, req.UseType, types.BJ, customerNameMap)
	if err != nil {
		return nil, fmt.Errorf("获取播放统计失败: %w", err)
	}
	filePathBJ := filepath.Join(tempDir, fmt.Sprintf("%s%s_BJ%s.xlsx", prefix, fmt.Sprintf("%s至%s", startTimeBJ.Format("2006-01-02"), endTimeBJ.Format("2006-01-02")), isClip))
	if err := l.exportStatsToExcel(statsBJ, filePathBJ, isClip); err != nil {
		return nil, err
	}
	filePaths = append(filePaths, filePathBJ)

	// 创建ZIP文件
	zipFileName := fmt.Sprintf("%s.zip", time.Now().Format("20060102150405"))
	zipFilePath := filepath.Join(exportDir, zipFileName)
	if err := l.createZipArchive(zipFilePath, filePaths); err != nil {
		return nil, fmt.Errorf("创建ZIP文件失败: %w", err)
	}

	// 获取文件信息
	fileInfo, err := os.Stat(zipFilePath)
	if err != nil {
		return nil, fmt.Errorf("获取ZIP文件信息失败: %w", err)
	}

	return &types.ExportPlayRecordStatsResp{
		Success:     true,
		Message:     "导出成功",
		FileName:    zipFileName,
		FileSize:    fileInfo.Size(),
		DownloadUrl: zipFileName,
		RecordCount: 0,
	}, nil
}

// validateMonthRange 验证月份范围并转换为UTC时间
func (l *ExportPlayRecordStatsLogic) validateMonthRange(startMonth, endMonth string) (time.Time, time.Time, error) {
	// 校验 startMonth 早于  当前 月往前 三个月
	currentTime := time.Now()
	threeMonthsAgo := currentTime.AddDate(0, middleware.MonthLimit, 0)
	if startMonth < threeMonthsAgo.Format("2006-01") {
		return time.Time{}, time.Time{}, errors.New(types.ErrNotExportThreeMonthAgo)
	}

	// 验证开始月份
	startTime, err := time.Parse("2006-01", startMonth)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("开始月份格式错误，应为YYYY-MM格式")
	}

	// 验证结束月份
	endTime, err := time.Parse("2006-01", endMonth)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("结束月份格式错误，应为YYYY-MM格式")
	}

	// 检查日期范围
	if startTime.After(endTime) {
		return time.Time{}, time.Time{}, errors.New("开始月份不能晚于结束月份")
	}

	// 将结束月份调整为当月的最后一天
	startTime = time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, time.Local)
	endTime = time.Date(endTime.Year(), endTime.Month()+1, 0, 23, 59, 59, 0, time.Local)

	return startTime, endTime, nil
}

// validateMonthRange 验证日期范围并转换为UTC时间
func (l *ExportPlayRecordStatsLogic) validateDateRange(startDate, endDate string) (time.Time, time.Time, error) {
	// 校验 startMonth 早于  当前 月往前 三个月
	currentTime := time.Now()
	threeMonthsAgo := currentTime.AddDate(0, middleware.MonthLimit, 0)
	if startDate < threeMonthsAgo.Format(time.DateOnly) {
		return time.Time{}, time.Time{}, errors.New(types.ErrNotExportThreeMonthAgo)
	}

	// 验证开始月份
	startTime, err := time.Parse(time.DateOnly, startDate)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("开始日期格式错误，应为YYYY-MM-DD格式")
	}

	// 验证结束月份
	endTime, err := time.Parse(time.DateOnly, endDate)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("结束日期格式错误，应为YYYY-MM-DD格式")
	}

	// 检查日期范围
	if startTime.After(endTime) {
		return time.Time{}, time.Time{}, errors.New("开始日期不能晚于结束日期")
	}

	// 调整 开始日期 和 结束日期
	startTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local)
	endTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 23, 59, 59, 0, time.Local)

	return startTime, endTime, nil
}

// getPlayStats 获取播放统计数据
func (l *ExportPlayRecordStatsLogic) getPlayStats(startTime, endTime int64, dates []string, playType int, vid []int64, useType int64, timeZoneType types.TimeZoneType, customerNameMap map[int64]types.ExportCustomerInfo) (map[string]map[int64]*types.DailyPlayStat, error) {
	// 查询该日期范围内的所有播放记录
	records, err := l.svcCtx.PlayRecordModel.FindByPlayTypeGroupByStartTimeDatesForExportV3(startTime, endTime, dates, playType, vid, useType, timeZoneType)
	if err != nil {
		return nil, err
	}

	// 统计结果: 日期 -> VID -> 统计数据
	stats := make(map[string]map[int64]*types.DailyPlayStat)

	if dates == nil || len(dates) == 0 {
		// 初始化所有日期的统计数据
		//currentTime := time.Unix(startTime)
		//endDateTime := time.Unix(endTime)
		currentTime := time.Unix(startTime, 0)
		endDateTime := time.Unix(endTime, 0)
		for currentTime.Before(endDateTime) || currentTime.Equal(endDateTime) {
			dateStr := currentTime.Format("2006-01-02")
			stats[dateStr] = make(map[int64]*types.DailyPlayStat)

			for _, playrecord := range records {
				stats[dateStr][playrecord.Vid] = &types.DailyPlayStat{
					Date:         dateStr,
					CustomerName: customerNameMap[playrecord.Vid].Name,
					Vid:          playrecord.Vid,
					Cid:          customerNameMap[playrecord.Vid].Cid,
					PlayCount:    0,
				}
			}

			currentTime = currentTime.AddDate(0, 0, 1)
		}
	} else {
		for _, date := range dates {
			stats[date] = make(map[int64]*types.DailyPlayStat)
			// 只给查询出来初始化
			for _, playrecord := range records {
				stats[date][playrecord.Vid] = &types.DailyPlayStat{
					Date:         date,
					CustomerName: customerNameMap[playrecord.Vid].Name,
					Vid:          playrecord.Vid,
					Cid:          customerNameMap[playrecord.Vid].Cid,
					PlayCount:    0,
				}
			}
		}
	}

	// 统计播放记录
	for _, record := range records {
		if dateStats, ok := stats[record.LtsTime]; ok {
			if stat, ok := dateStats[record.Vid]; ok {
				stat.PlayCount = record.PlayNumTotal
			}
		}
	}

	return stats, nil
}

// exportStatsToExcel 将统计数据导出为Excel
func (l *ExportPlayRecordStatsLogic) exportStatsToExcel(stats map[string]map[int64]*types.DailyPlayStat, filePath string, isClip string) error {
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("data")
	if err != nil {
		return err
	}

	// 1. 收集所有唯一的日期和客户
	dates := make([]string, 0)
	customerBasicInfo := make(map[int64]types.ExportCustomerInfo) // vid -> customerName
	for date, dateStats := range stats {
		dates = append(dates, date)
		for vid, stat := range dateStats {
			info := customerBasicInfo[vid]
			info.Name = stat.CustomerName
			info.Cid = stat.Cid
			customerBasicInfo[vid] = info
		}
	}

	sort.Strings(dates)

	// 2. 创建表头
	headers := []string{"客户端", "vid", "cid"}
	headers = append(headers, dates...)
	headers = append(headers, "合计")

	headerRow := sheet.AddRow()
	for _, header := range headers {
		cell := headerRow.AddCell()
		cell.SetString(header)
	}

	// 3. 填充数据
	// 用于计算每日合计
	dailyTotals := make(map[string]int64)
	// 用于计算客户总计
	customerTotals := make(map[int64]int64)

	// 按客户名称排序
	vids := make([]int64, 0, len(customerBasicInfo))
	for vid := range customerBasicInfo {
		vids = append(vids, vid)
	}
	sort.Slice(vids, func(i, j int) bool {
		return customerBasicInfo[vids[i]].Name < customerBasicInfo[vids[j]].Name
	})

	// 填充每个客户的数据行
	for _, vid := range vids {
		row := sheet.AddRow()

		// 客户名称
		row.AddCell().SetString(customerBasicInfo[vid].Name)
		// 客户 vid
		row.AddCell().SetInt64(vid)
		// 客户 cid
		row.AddCell().SetInt64(customerBasicInfo[vid].Cid)

		// 每日数据
		rowTotal := int64(0)
		for _, date := range dates {
			var playCount int64 = 0
			if dateStats, ok := stats[date]; ok {
				if stat, ok := dateStats[vid]; ok {
					playCount = stat.PlayCount
				}
			}
			row.AddCell().SetInt64(playCount)
			rowTotal += playCount
			dailyTotals[date] += playCount
		}

		// 行合计
		customerTotals[vid] = rowTotal
		row.AddCell().SetInt64(rowTotal)
	}

	if isClip != "" {
		// 4. 添加总次数行
		totalRow := sheet.AddRow()
		totalRow.AddCell().SetString("总次数")
		totalRow.AddCell().SetString("")
		totalRow.AddCell().SetString("")
		// 填充总次数
		grandTotal := int64(0)
		for _, date := range dates {
			total := dailyTotals[date]
			totalRow.AddCell().SetInt64(total)
			grandTotal += total
		}

		// 添加总次数
		totalRow.AddCell().SetInt64(grandTotal)
	}

	// 4. 添加合计行
	totalRow := sheet.AddRow()
	totalRow.AddCell().SetString("合计")
	totalRow.AddCell().SetString("")
	totalRow.AddCell().SetString("")

	// 填充每日合计
	grandTotal := int64(0)
	for _, date := range dates {
		total := dailyTotals[date]
		totalRow.AddCell().SetInt64(total)
		grandTotal += total
	}

	// 添加总计
	totalRow.AddCell().SetInt64(grandTotal)

	return file.Save(filePath)
}

// createZipArchive 创建ZIP压缩文件
func (l *ExportPlayRecordStatsLogic) createZipArchive(zipPath string, filePaths []string) error {
	// 创建zip文件
	zipFile, err := os.Create(zipPath)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	// 创建zip写入器
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 添加文件到zip
	for _, filePath := range filePaths {
		err = l.addFileToZip(zipWriter, filePath)
		if err != nil {
			return err
		}
	}

	return nil
}

// addFileToZip 将文件添加到ZIP中
func (l *ExportPlayRecordStatsLogic) addFileToZip(zipWriter *zip.Writer, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 获取文件信息
	info, err := file.Stat()
	if err != nil {
		return err
	}

	// 创建zip文件头
	header, err := zip.FileInfoHeader(info)
	if err != nil {
		return err
	}

	// 使用文件名而不是完整路径
	header.Name = filepath.Base(filePath)

	// 设置压缩方法
	header.Method = zip.Deflate

	// 创建文件写入器
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}

	// 复制文件内容到zip
	_, err = io.Copy(writer, file)
	return err
}

// 验证单个月份
func (l *ExportPlayRecordStatsLogic) validateMonth(month string, timeZone types.TimeZoneType) (time.Time, time.Time, error) {

	// 校验
	currentTime := time.Now()
	threeMonthsAgo := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, currentTime.Location()).AddDate(0, middleware.MonthLimit, 0)
	if month < threeMonthsAgo.Format("2006-01") {
		return time.Time{}, time.Time{}, errors.New(types.ErrNotExportThreeMonthAgo)
	}

	var timeLocation *time.Location
	if timeZone == types.UTC {
		timeLocation = time.UTC
	} else {
		timeLocation = time.Local
	}

	startTime, err := time.ParseInLocation("2006-01", month, timeLocation)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("月份格式错误，应为YYYY-MM格式")
	}

	endTime := startTime.AddDate(0, 1, -1)
	endTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 23, 59, 59, 999999999, timeLocation)

	return startTime, endTime, nil
}

// 验证日期列表
func (l *ExportPlayRecordStatsLogic) validateDates(dates []string, timeZone types.TimeZoneType) (time.Time, time.Time, error) {
	if len(dates) == 0 {
		return time.Time{}, time.Time{}, errors.New("日期列表不能为空")
	}

	// 设置时区
	var timeLocation *time.Location
	if timeZone == types.UTC {
		timeLocation = time.UTC
	} else {
		// 使用北京时间 (UTC+8)
		timeLocation, _ = time.LoadLocation("Asia/Shanghai")
		if timeLocation == nil { // 如果加载失败，使用本地时区
			timeLocation = time.Local
		}
	}

	currentTime := time.Now().In(timeLocation)

	// 计算三个月前的1号（使用指定时区）
	threeMonthsAgo := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, timeLocation).AddDate(0, middleware.MonthLimit, 0)
	threeMonthsAgoStr := threeMonthsAgo.Format("2006-01-02")

	// 复制并排序日期数组，避免修改原始数据
	sortedDates := make([]string, len(dates))
	copy(sortedDates, dates)
	sort.Strings(sortedDates)

	// 验证最早日期是否超过三个月限制
	if sortedDates[0] < threeMonthsAgoStr {
		return time.Time{}, time.Time{}, errors.New(types.ErrNotExportThreeMonthAgo)
	}

	// 解析最早日期，明确使用指定时区
	startTime, err := time.ParseInLocation("2006-01-02", sortedDates[0], timeLocation)
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("起始日期格式错误: %s", sortedDates[0])
	}

	// 解析最晚日期，明确使用指定时区
	endTime, err := time.ParseInLocation("2006-01-02", sortedDates[len(sortedDates)-1], timeLocation)
	if err != nil {
		return time.Time{}, time.Time{}, fmt.Errorf("结束日期格式错误: %s", sortedDates[len(sortedDates)-1])
	}

	// 调整时间范围（起始日期设为当天00:00:00，结束日期设为当天23:59:59.999999999）
	// 确保使用正确的时区
	startTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, timeLocation)
	endTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 23, 59, 59, 999999999, timeLocation)

	// 调试信息
	logx.Infof("验证日期 - 时区: %s, 起始时间: %s (%d), 结束时间: %s (%d)",
		timeLocation.String(),
		startTime.Format(time.RFC3339),
		startTime.Unix(),
		endTime.Format(time.RFC3339),
		endTime.Unix())

	return startTime, endTime, nil
}
