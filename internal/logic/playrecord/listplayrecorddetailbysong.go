package playrecord

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

type ListPlayRecordDetailGroupBySongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListPlayRecordDetailGroupBySongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListPlayRecordDetailGroupBySongLogic {
	return &ListPlayRecordDetailGroupBySongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListPlayRecordDetailGroupBySongLogic) ListPlayRecordDetailGroupBySong(req *types.SumPlayRecordDetailBySongReq) (*types.SumPlayRecordDetailResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	if req.Page == 0 {
		req.Page = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	if req.LtsTime == "" {
		return nil, errors.New(types.ErrLtsTime)
	}

	if req.PlayType == 0 {
		return nil, errors.New(types.ErrPlayType)
	}

	// 查询指定歌曲的播放记录
	vids := make([]int64, 0)

	if req.CustomerName != "" {
		// 根据 客户名称 以及 客户编号  查询 客户
		customers, err := l.svcCtx.CustomerModel.FindByName(req.CustomerName)
		if err != nil {
			return nil, err
		}

		if customers == nil {
			return nil, errors.New(types.ErrNoCustomerInfo)
		}

		for _, customer := range customers {
			vids = append(vids, customer.Vid)
		}
	} else if req.Vid != 0 {
		vids = append(vids, req.Vid)
	}

	// 查询指定歌曲的播放记录
	list, count, err := l.svcCtx.PlayRecordModel.FindDetailGroupByCustomer(req.LtsTime, vids, req.PlayType, req.UseType, req.SceneType, req.FreeType, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.New(types.ErrQuerySongPlayRecord)
	}

	if list == nil {
		return new(types.SumPlayRecordDetailResp), nil
	}

	// 获取歌曲信息
	allSong, songErr := l.svcCtx.SongModel.FindAll(0, 0)
	if songErr != nil {
		return nil, errors.New(types.ErrInsertSongName)
	}
	songMap := make(map[int64]string)
	for _, s := range allSong {
		songMap[s.SongCode] = s.Name
	}

	// 获取版权方信息
	cp, cpErr := l.svcCtx.CpModel.FindAll(0, 0)
	if cpErr != nil {
		return nil, errors.New(types.ErrInsertCpName)
	}

	cpMap := make(map[int64]string)
	for _, c := range cp {
		cpMap[c.Id] = c.Name
	}

	allCustomer, customerErr := l.svcCtx.CustomerModel.FindAll(0, 0)
	if customerErr != nil {
		return nil, errors.New(types.ErrInsertCustomerName)
	}

	allCustomerMap := make(map[int64]string)

	for _, c := range allCustomer {
		allCustomerMap[c.Vid] = c.Name
	}

	sumPlayRecordDetails := make([]types.SumPlayRecordDetail, 0)

	for _, v := range list {

		sumPlayRecordDetails = append(sumPlayRecordDetails, types.SumPlayRecordDetail{
			LtsTime:      v.LtsTime,
			PlayType:     v.PlayType,
			SongCode:     v.SongCode,
			SongName:     songMap[v.SongCode],
			SongDuration: v.SongDuration,
			CpId:         v.CpId,
			CpName:       cpMap[v.CpId],
			Vid:          v.Vid,
			CustomerName: allCustomerMap[v.Vid],
			UseType:      v.UseType,
			PlayDuration: v.PlayDuration,
			StartTime:    middleware.UnixToUTCTime(v.StartTime),
			EndTime:      middleware.UnixToUTCTime(v.EndTime),
			PlayBackPos:  v.PlayBackPos,
			PlayNum:      v.PlayNum,
			SceneType:    v.SceneType,
			RecordType:   v.RecordType,
			FreeType:     v.FreeType,
		})
	}

	return &types.SumPlayRecordDetailResp{
		Total: count,
		List:  sumPlayRecordDetails,
	}, nil
}

// 优化
func (l *ListPlayRecordDetailGroupBySongLogic) ListPlayRecordDetailGroupBySongV2(req *types.SumPlayRecordDetailBySongReq) (*types.SumPlayRecordDetailResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	if req.Page == 0 {
		req.Page = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	if req.LtsTime == "" {
		return nil, errors.New(types.ErrLtsTime)
	}

	if req.PlayType == 0 {
		return nil, errors.New(types.ErrPlayType)
	}

	// 查询指定歌曲的播放记录
	vids := make([]int64, 0)

	if req.CustomerName != "" {
		// 根据 客户名称 以及 客户编号  查询 客户
		customers, err := l.svcCtx.CustomerModel.FindByName(req.CustomerName)
		if err != nil {
			return nil, err
		}

		if customers == nil {
			return nil, errors.New(types.ErrNoCustomerInfo)
		}

		for _, customer := range customers {
			vids = append(vids, customer.Vid)
		}
	} else if req.Vid != 0 {
		vids = append(vids, req.Vid)
	}

	// 查询指定歌曲的播放记录
	list, count, err := l.svcCtx.PlayRecordModel.FindDetailGroupByCustomer(req.LtsTime, vids, req.PlayType, req.UseType, req.SceneType, req.FreeType, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.New(types.ErrQuerySongPlayRecord)
	}

	if list == nil {
		return new(types.SumPlayRecordDetailResp), nil
	}

	// 优化2: 提取需要查询的唯一ID集合
	songCodes := make(map[int64]struct{})
	cpIds := make(map[int64]struct{})
	customerVids := make(map[int64]struct{})

	for _, v := range list {
		songCodes[v.SongCode] = struct{}{}
		cpIds[v.CpId] = struct{}{}
		customerVids[v.Vid] = struct{}{}
	}

	// 优化3: 只查询需要的歌曲信息
	songMap := make(map[int64]string)
	if len(songCodes) > 0 {
		// 这里假设SongModel有FindByCodes方法，如果没有，需要添加
		songs, err := l.svcCtx.SongModel.FindByCodes(keysFromMap(songCodes))
		if err != nil {
			return nil, errors.New(types.ErrInsertSongName)
		}
		for _, s := range songs {
			songMap[s.SongCode] = s.Name
		}
	}

	// 优化4: 只查询需要的版权方信息
	cpMap := make(map[int64]string)
	if len(cpIds) > 0 {
		// 这里假设CpModel有FindByIds方法，如果没有，需要添加
		cps, err := l.svcCtx.CpModel.FindByIds(keysFromMap(cpIds))
		if err != nil {
			return nil, errors.New(types.ErrInsertCpName)
		}
		for _, c := range cps {
			cpMap[c.Id] = c.Name
		}
	}

	// 优化5: 只查询需要的客户信息
	customerMap := make(map[int64]string)
	if len(customerVids) > 0 {
		// 这里假设CustomerModel有FindByVids方法，如果没有，需要添加
		customers, err := l.svcCtx.CustomerModel.FindByVids(keysFromMap(customerVids))
		if err != nil {
			return nil, errors.New(types.ErrInsertCustomerName)
		}
		for _, c := range customers {
			customerMap[c.Vid] = c.Name
		}
	}

	// 构建结果
	sumPlayRecordDetails := make([]types.SumPlayRecordDetail, 0, len(list))
	for _, v := range list {
		sumPlayRecordDetails = append(sumPlayRecordDetails, types.SumPlayRecordDetail{
			LtsTime:      v.LtsTime,
			PlayType:     v.PlayType,
			SongCode:     v.SongCode,
			SongName:     songMap[v.SongCode],
			SongDuration: v.SongDuration,
			CpId:         v.CpId,
			CpName:       cpMap[v.CpId],
			Vid:          v.Vid,
			CustomerName: customerMap[v.Vid],
			UseType:      v.UseType,
			PlayDuration: v.PlayDuration,
			StartTime:    middleware.UnixToUTCTime(v.StartTime),
			EndTime:      middleware.UnixToUTCTime(v.EndTime),
			PlayBackPos:  v.PlayBackPos,
			PlayNum:      v.PlayNum,
			SceneType:    v.SceneType,
			RecordType:   v.RecordType,
			FreeType:     v.FreeType,
		})
	}

	return &types.SumPlayRecordDetailResp{
		Total: count,
		List:  sumPlayRecordDetails,
	}, nil
}

// 辅助函数：从map中提取keys
func keysFromMap(m map[int64]struct{}) []int64 {
	keys := make([]int64, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
