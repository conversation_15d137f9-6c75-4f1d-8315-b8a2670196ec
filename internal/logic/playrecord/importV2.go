package playrecord

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"music/model/playrecord"

	"github.com/zeromicro/go-zero/core/logx"
)

// ImportResult 导入结果结构
type ImportResult struct {
	TotalCount    int            // 总记录数
	SuccessCount  int            // 成功导入数
	FailedCount   int            // 失败数
	FailedRecords map[int]string // 失败详情，行号:原因
	ErrorMessage  string         // 整体错误信息
}

// ImportPlayRecordsFromFileV2 从文件导入播放记录数据
func ImportPlayRecordsFromFileV2(recordModel playrecord.PlayRecordModel, filePath, failedOutputPath string) (*ImportResult, []*playrecord.PlayRecord, error) {
	result := &ImportResult{
		FailedRecords: make(map[int]string),
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return result, nil, fmt.Errorf("文件不存在: %s", filePath)
	}

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(filePath))

	var records []*playrecord.PlayRecord
	var headers []string
	var err error

	// 根据文件类型选择导入方法
	switch ext {
	case ".csv":
		records, headers, err = importFromCSVV2(filePath)
		logx.Infof("CSV导入完成，记录数: %d", len(records))
	default:
		return result, nil, fmt.Errorf("不支持的文件格式: %s, 请使用CSV文件(.csv)", ext)
	}

	if err != nil {
		return result, nil, fmt.Errorf("导入文件失败: %w", err)
	}

	result.TotalCount = len(records)

	if len(records) == 0 {
		return result, nil, fmt.Errorf("没有找到有效的播放记录数据")
	}

	// 批量导入到数据库
	successCount, failedRecords, err := recordModel.BatchImportGoroutineWithTx(records)
	if err != nil {
		return result, nil, fmt.Errorf("导入播放记录失败: %w", err)
	}

	result.SuccessCount = successCount
	result.FailedCount = len(failedRecords)

	// 如果有失败记录且指定了输出路径，记录失败详情
	if len(failedRecords) > 0 && failedOutputPath != "" {
		if err := writeFailedRecordsToCSVV2(records, failedRecords, headers, failedOutputPath); err != nil {
			result.ErrorMessage = fmt.Sprintf("写入失败记录时出错: %s", err.Error())
		}
	}

	return result, records, nil
}

// importFromCSV 从CSV文件导入数据
func importFromCSVV2(filePath string) ([]*playrecord.PlayRecord, []string, error) {
	// 打开CSV文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, nil, fmt.Errorf("无法打开CSV文件: %w", err)
	}
	defer file.Close()

	// 创建带缓冲的读取器
	reader := bufio.NewReader(file)

	// 检测文件编码
	data, _ := reader.Peek(1024)
	isUTF8 := true
	for _, b := range data {
		if b == 0xEF || b == 0xBB || b == 0xBF {
			continue // UTF-8 BOM
		}
		if b > 0x7F {
			isUTF8 = false
			break
		}
	}

	// 创建适当的reader
	var csvReader *csv.Reader
	if isUTF8 {
		file.Seek(0, 0) // 重置文件指针到开始位置
		csvReader = csv.NewReader(reader)
	} else {
		file.Seek(0, 0) // 重置文件指针到开始位置
		gbkReader := transform.NewReader(reader, simplifiedchinese.GBK.NewDecoder())
		csvReader = csv.NewReader(gbkReader)
	}

	// 读取第一行来检测分隔符
	firstLine, err := csvReader.Read()
	if err != nil && err != io.EOF {
		return nil, nil, fmt.Errorf("读取第一行失败: %w", err)
	}

	// 检测分隔符
	separator := detectSeparatorV2(strings.Join(firstLine, ""))

	// 重新打开文件以确保从头开始读取
	file.Close()
	file, err = os.Open(filePath)
	if err != nil {
		return nil, nil, fmt.Errorf("重新打开文件失败: %w", err)
	}
	defer file.Close()

	// 重新创建reader
	reader = bufio.NewReader(file)
	if isUTF8 {
		csvReader = csv.NewReader(reader)
	} else {
		gbkReader := transform.NewReader(reader, simplifiedchinese.GBK.NewDecoder())
		csvReader = csv.NewReader(gbkReader)
	}

	// 设置CSV reader的配置
	csvReader.Comma = separator
	csvReader.LazyQuotes = true
	csvReader.TrimLeadingSpace = true

	// 读取表头
	headers, err := csvReader.Read()
	if err != nil {
		return nil, nil, fmt.Errorf("读取CSV表头失败: %w", err)
	}

	logx.Infof("CSV表头: %v", headers)
	logx.Infof("使用的分隔符: %c", separator)

	// 创建列名到索引的映射
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[strings.TrimSpace(header)] = i
	}

	var records []*playrecord.PlayRecord
	lineNum := 1 // 从1开始，因为0是表头

	// 逐行读取数据
	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logx.Errorf("读取第%d行时出错: %v", lineNum, err)
			lineNum++
			continue
		}

		// 解析行数据为PlayRecord对象
		record, err := parseCSVRowToPlayRecordV2(row, headerMap)
		if err != nil {
			logx.Errorf("解析第%d行数据失败: %v", lineNum, err)
			lineNum++
			continue
		}

		records = append(records, record)
		lineNum++
	}

	return records, headers, nil
}

// parseCSVRowToPlayRecord 解析CSV行数据为PlayRecord对象
func parseCSVRowToPlayRecordV2(row []string, headerMap map[string]int) (*playrecord.PlayRecord, error) {
	record := &playrecord.PlayRecord{}

	// 检查行数据是否为空
	if len(row) == 0 {
		return nil, fmt.Errorf("空行数据")
	}

	// 解析时间戳 (lts)
	if idx, ok := headerMap["lts"]; ok && idx < len(row) {
		lts, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析时间戳失败: %v", err)
		}
		record.Lts = lts
	}

	// 解析客户ID (vid)
	if idx, ok := headerMap["vid"]; ok && idx < len(row) {
		vid, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析客户ID失败: %v", err)
		}
		record.Vid = vid
	}

	// 设置唯一ID (uniqueId)
	if idx, ok := headerMap["uniqueId"]; ok && idx < len(row) {
		record.UniqueId = strings.TrimSpace(row[idx])
	}

	// 解析版权方ID (cpId)
	if idx, ok := headerMap["cpId"]; ok && idx < len(row) {
		cpId, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err == nil {
			record.CpId = cpId
		}
	}

	// 解析歌曲编码 (songCode)
	if idx, ok := headerMap["songCode"]; ok && idx < len(row) {
		songCode, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析歌曲编码失败: %v", err)
		}
		record.SongCode = songCode
	}

	// 解析歌曲时长 (songDuration)
	if idx, ok := headerMap["songDuration"]; ok && idx < len(row) {
		duration, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.SongDuration = duration
		}
	}

	// 解析播放时长 (playDuration)
	if idx, ok := headerMap["playDuration"]; ok && idx < len(row) {
		duration, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.PlayDuration = duration
		}
	}

	// 解析开始时间 (startTime)
	if idx, ok := headerMap["startTime"]; ok && idx < len(row) {
		startTime, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err == nil {
			record.StartTime = startTime
		}
	}

	// 解析结束时间 (endTime)
	if idx, ok := headerMap["endTime"]; ok && idx < len(row) {
		endTime, err := strconv.ParseInt(strings.TrimSpace(row[idx]), 10, 64)
		if err == nil {
			record.EndTime = endTime
		}
	}

	// 解析播放位置 (playbackPos)
	if idx, ok := headerMap["playbackPos"]; ok && idx < len(row) {
		pos, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.PlaybackPos = pos
		}
	}

	// 解析播放次数 (playNum)
	if idx, ok := headerMap["playNum"]; ok && idx < len(row) {
		num, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil && num > 0 {
			record.PlayNum = num
		} else {
			record.PlayNum = 1 // 默认为1次
		}
	} else {
		record.PlayNum = 1 // 默认为1次
	}

	// 解析场景类型 (sceneType)
	if idx, ok := headerMap["sceneType"]; ok && idx < len(row) {
		sceneType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.SceneType = sceneType
		}
	}

	// 解析播放类型 (playType)
	if idx, ok := headerMap["playType"]; ok && idx < len(row) {
		playType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.PlayType = playType
		}
	}

	// 解析免费类型 (freeType)
	if idx, ok := headerMap["freeType"]; ok && idx < len(row) {
		freeType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.FreeType = freeType
		}
	}

	// 解析记录类型 (recordType)
	if idx, ok := headerMap["recordType"]; ok && idx < len(row) {
		recordType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.RecordType = recordType
		}
	}

	// 解析使用类型 (useType)
	if idx, ok := headerMap["useType"]; ok && idx < len(row) {
		useType, err := strconv.Atoi(strings.TrimSpace(row[idx]))
		if err == nil {
			record.UseType = useType
		}
	}

	// 生成唯一ID (如果没有)
	if record.UniqueId == "" {
		randNum := time.Now().UnixNano() % 10000
		record.UniqueId = fmt.Sprintf("%d_%d_%d", record.SongCode, record.Lts, randNum)
	}

	// 设置创建时间和推送状态
	record.CreateTime = time.Now()
	record.PushStatus = 0 // 默认未推送

	return record, nil
}

// detectSeparator 检测CSV文件的分隔符
func detectSeparatorV2(line string) rune {
	// 常见的分隔符
	separators := []rune{',', '\t', ';', '|'}
	counts := make(map[rune]int)

	// 计算每个分隔符在行中的出现次数
	for _, sep := range separators {
		counts[sep] = strings.Count(line, string(sep))
	}

	// 找出出现次数最多的分隔符
	maxCount := 0
	var bestSep rune = ',' // 默认使用逗号

	for sep, count := range counts {
		if count > maxCount {
			maxCount = count
			bestSep = sep
		}
	}

	return bestSep
}

// writeFailedRecordsToCSV 将失败记录写入CSV文件
func writeFailedRecordsToCSVV2(records []*playrecord.PlayRecord, failedRecords map[int]string, headers []string, outputPath string) error {
	// 创建输出目录（如果不存在）
	dir := filepath.Dir(outputPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 创建输出文件
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %w", err)
	}
	defer file.Close()

	// 创建CSV writer
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头，添加"失败原因"列
	newHeaders := append(headers, "失败原因")
	if err := writer.Write(newHeaders); err != nil {
		return fmt.Errorf("写入表头失败: %w", err)
	}

	// 写入失败记录
	for idx, reason := range failedRecords {
		if idx >= 0 && idx < len(records) {
			record := records[idx]

			// 准备行数据
			row := make([]string, len(headers)+1)

			// 填充行数据
			for i, header := range headers {
				switch header {
				case "lts":
					row[i] = fmt.Sprintf("%d", record.Lts)
				case "vid":
					row[i] = fmt.Sprintf("%d", record.Vid)
				case "uniqueId":
					row[i] = record.UniqueId
				case "cpId":
					row[i] = fmt.Sprintf("%d", record.CpId)
				case "songCode":
					row[i] = fmt.Sprintf("%d", record.SongCode)
				case "songDuration":
					row[i] = fmt.Sprintf("%d", record.SongDuration)
				case "playDuration":
					row[i] = fmt.Sprintf("%d", record.PlayDuration)
				case "startTime":
					row[i] = strconv.FormatInt(record.StartTime, 10)
				case "endTime":
					row[i] = strconv.FormatInt(record.EndTime, 10)
				case "playbackPos":
					row[i] = fmt.Sprintf("%d", record.PlaybackPos)
				case "playNum":
					row[i] = fmt.Sprintf("%d", record.PlayNum)
				case "sceneType":
					row[i] = fmt.Sprintf("%d", record.SceneType)
				case "playType":
					row[i] = fmt.Sprintf("%d", record.PlayType)
				case "freeType":
					row[i] = fmt.Sprintf("%d", record.FreeType)
				case "recordType":
					row[i] = fmt.Sprintf("%d", record.RecordType)
				case "useType":
					row[i] = fmt.Sprintf("%d", record.UseType)
				}
			}

			// 添加失败原因
			row[len(headers)] = reason

			// 写入行
			if err := writer.Write(row); err != nil {
				logx.Errorf("写入失败记录行失败: %v", err)
				continue
			}
		}
	}

	return nil
}
