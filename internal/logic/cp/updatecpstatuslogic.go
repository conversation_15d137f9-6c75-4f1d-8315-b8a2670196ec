package cp

import (
	"context"
	"errors"
	"music/internal/middleware"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UpdateCpStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateCpStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateCpStatusLogic {
	return &UpdateCpStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateCpStatusLogic) UpdateCpStatusLogic(req *types.UpdateCpStatusReq) (*types.AddOrUpdateCpResp, error) {
	var endTime int64 = 0
	var err error
	if req.Status == types.CpClose {
		// 关闭版权方，设置结束时间
		endTime, err = middleware.TimeToUnix(time.Now().Format("2006-01-02"))
		if err != nil {
			return nil, err
		}
	} else {
		cp, err := l.svcCtx.CpModel.FindOne(req.Id)
		if err != nil {
			return nil, errors.New(types.ErrNoCpInfo)
		}
		endTime = cp.EndTime
	}

	// 查询版权方详情
	err = l.svcCtx.CpModel.UpdateStatus(req.Id, req.Status, endTime)
	if err != nil {
		return nil, errors.New(types.ErrUpdateCpStatus)
	}

	return &types.AddOrUpdateCpResp{
		Success: true,
		Message: "更新版权方状态成功",
	}, nil
}
