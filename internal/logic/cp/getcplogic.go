package cp

import (
	"context"
	"music/internal/middleware"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type GetCpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCpLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCpLogic {
	return &GetCpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCpLogic) GetCp(req *types.GetCpReq) (*types.GetCpResp, error) {
	// 查询版权方详情
	cp, err := l.svcCtx.CpModel.FindOne(req.Id)
	if err != nil {
		return nil, err
	}

	return &types.GetCpResp{
		Cp: types.CpInfo{
			Id:      cp.Id,
			Name:    cp.Name,
			Remark:  cp.Remark,
			Status:  cp.Status,
			EndTime: middleware.UnixToDate(cp.EndTime),
		},
	}, nil
}
