package cp

import (
	"context"
	"errors"
	"music/internal/middleware"
	"music/model/cp"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UpdateCpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateCpLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateCpLogic {
	return &UpdateCpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateCpLogic) UpdateCpLogic(req *types.UpdateCpInfo) (*types.AddOrUpdateCpResp, error) {
	var endTime int64 = 0
	var err error

	// 检查版权方是否存在
	info, err := l.svcCtx.CpModel.FindOneByName(req.Name)
	if err != nil {
		return nil, errors.New(types.ErrCpNameExists)
	}
	if info != nil {
		if info.Id != req.Id {
			return nil, errors.New(types.ErrCpNameExists)
		}
	}

	// 暂时不限制
	/*if req.Status == types.CpClose && req.EndTime == "" {
		return nil, errors.New(types.ErrNeedSetEndTime)
	}

	if req.Status == types.CpOpen && req.EndTime != "" {
		return nil, errors.New(types.ErrSetEndTime)
	}*/

	if req.EndTime != "" {
		endTime, err = middleware.TimeToUnix(req.EndTime)
		if err != nil {
			return nil, err
		}
	}

	cp := &cp.Cp{
		Id:         req.Id,
		Name:       req.Name,
		Status:     req.Status,
		EndTime:    endTime,
		UpdateTime: time.Now(),
	}

	// 更新版权方信息
	err = l.svcCtx.CpModel.Update(cp)
	if err != nil {
		return nil, errors.New(types.ErrUpdateCp)
	}

	return &types.AddOrUpdateCpResp{
		Success: true,
		Message: "更新版权方成功",
	}, nil
}
