package cp

import (
	"context"
	"errors"
	"music/internal/middleware"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListCpsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListCpsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListCpsLogic {
	return &ListCpsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListCpsLogic) ListCps(req *types.ListCpReq) (*types.ListCpResp, error) {

	beginEndTime, err := middleware.DateToUnix(req.BeginEndTime)
	if err != nil {
		return nil, err
	}

	endEndTime, err := middleware.TimeToUnix(req.EndEndTime)
	if err != nil {
		return nil, err
	}

	// 查询版权方列表
	cps, count, err := l.svcCtx.CpModel.Find(req.Name, beginEndTime, endEndTime, req.Status, req.OrderByCreateTime, req.OrderByUpdateTime, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.New(types.ErrQueryCpInfo)
	}

	var result []types.CpInfo
	for _, cp := range cps {
		result = append(result, types.CpInfo{
			Id:         cp.Id,
			Name:       cp.Name,
			Remark:     cp.Remark,
			Status:     cp.Status,
			EndTime:    middleware.UnixToTime(cp.EndTime),
			CreateTime: cp.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: cp.UpdateTime.Format("2006-01-02 15:04:05"),
		})
	}

	return &types.ListCpResp{
		Total: count,
		List:  result,
	}, nil
}
