package cp

import (
	"context"
	"errors"
	"music/internal/middleware"
	"music/model/cp"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type AddCpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddCpLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddCpLogic {
	return &AddCpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddCpLogic) AddCpLogic(req *types.AddCpInfo) (*types.AddOrUpdateCpResp, error) {
	var endTime int64 = 0
	var err error

	// 检查版权方是否存在
	info, err := l.svcCtx.CpModel.FindOneByName(req.Name)
	if err != nil {
		return nil, errors.New(types.ErrQueryCpInfo)
	}
	if info != nil {
		return nil, errors.New(types.ErrCpNameExists)
	}

	// 暂时不限制
	/*if req.Status == types.CpClose && req.EndTime == "" {
		return nil, errors.New(types.ErrNeedSetEndTime)
	}

	if req.Status == types.CpOpen && req.EndTime != "" {
		return nil, errors.New(types.ErrSetEndTime)
	}
	*/
	if req.EndTime != "" {
		endTime, err = middleware.TimeToUnix(req.EndTime)
		if err != nil {
			return nil, err
		}
	}

	/*
		// 关闭版权方，设置结束时间
				endTime, err = middleware.DateToUnix(time.Now().Format("2006-01-02"))
				if err != nil {
					return nil, errors.New(types.ErrFormatTime)
				}
	*/

	cp := &cp.Cp{
		Name:       req.Name,
		Status:     req.Status,
		EndTime:    endTime,
		Remark:     req.Remark,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}

	// 查询版权方详情
	result, err := l.svcCtx.CpModel.Insert(cp)
	if err != nil {
		return nil, errors.New(types.ErrCreateCp)
	}

	cpId, err := result.LastInsertId()
	if err != nil {
		return nil, errors.New(types.ErrCreateCp)
	}

	return &types.AddOrUpdateCpResp{
		Id:      cpId,
		Success: true,
		Message: "创建版权方成功",
	}, nil
}
