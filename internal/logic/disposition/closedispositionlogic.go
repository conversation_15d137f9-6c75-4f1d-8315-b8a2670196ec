package disposition

import (
	"context"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"music/model/disposition"

	"music/internal/svc"
	"music/internal/types"
)

type CloseOrOpenDispositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCloseOrOpenDispositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloseOrOpenDispositionLogic {
	return &CloseOrOpenDispositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CloseOrOpenDispositionLogic) CloseOrOpenDisposition(req *types.CloseOrOpenDispositionCommonReq) (*types.DispositionCommonResp, error) {
	// 查询指定vid的配置列表
	dispositionInfo, err := l.svcCtx.DispositionModel.FindOne(req.Id)
	if err != nil {
		return nil, errors.New(types.ErrQueryDisposition)
	}

	if dispositionInfo == nil {
		return nil, errors.New(types.ErrDispositionNotFound)
	}

	rollback := false
	detailIds := []int64{}
	if req.Status == types.DispositionOrDispositionDetailClose {
		dispositionDetails := make([]*disposition.DispositionDetail, 0)
		dispositionDetails, err = l.svcCtx.DispositionDetailModel.FindByOrderCode(dispositionInfo.OrderCode)
		if err != nil {
			return nil, errors.New(types.ErrQueryDispositionDetail)
		}

		for _, detail := range dispositionDetails {
			detailIds = append(detailIds, detail.Id)
			fmt.Println("detailIds", detail.Id)
		}

		err = l.svcCtx.DispositionDetailModel.CloseOrOpenByIds(detailIds, types.DispositionOrDispositionDetailClose)
		if err != nil {
			return nil, errors.New(types.ErrBeforeCloseDispositionDetail)
		}
		rollback = true
	}

	err = l.svcCtx.DispositionModel.CloseOrOpen(req.Id, req.Status)
	if err != nil {
		if rollback {
			// 回滚
			err = l.svcCtx.DispositionDetailModel.CloseOrOpenByIds(detailIds, types.DispositionOrDispositionDetailOpen)
			if err != nil {
				return nil, errors.New(types.ErrRollbackDispositionDetail)
			}
		}
		return nil, errors.New(types.ErrCloseOrOpen)
	}

	return &types.DispositionCommonResp{
		Id:      req.Id,
		Message: "更新成功",
	}, nil
}
