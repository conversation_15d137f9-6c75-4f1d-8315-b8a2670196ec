package disposition

import (
	"context"
	"errors"
	"music/model/disposition"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type AddDispositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddDispositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddDispositionLogic {
	return &AddDispositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddDispositionLogic) AddDisposition(req *types.DispositionInfo) (*types.DispositionCommonResp, error) {

	var err error
	// 查询指定vid的配置列表
	dispositionInfo, err := l.svcCtx.DispositionModel.FindOneByOrderCode(req.OrderCode)
	if err != nil {
		return nil, errors.New(types.ErrQueryDisposition)
	}

	if dispositionInfo != nil {
		return nil, errors.New(types.ErrOrderCodeExist)
	}

	// status  默认开启
	req.Status = types.DispositionOrDispositionDetailOpen

	addDisposition := &disposition.Disposition{
		OrderCode: req.OrderCode,
		Vid:       req.Vid,
		Remark:    req.Remark,
		Status:    req.Status,
	}

	result, err := l.svcCtx.DispositionModel.Insert(addDisposition)
	if err != nil {
		return nil, errors.New(types.ErrInsertDisposition)
	}

	dispositionId, err := result.LastInsertId()
	if err != nil {
		return nil, errors.New(types.ErrInsertDisposition)
	}

	return &types.DispositionCommonResp{
		Id:      dispositionId,
		Message: "添加成功",
	}, nil
}
