package disposition

import (
	"context"
	"errors"
	"music/model/disposition"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UpdateDispositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDispositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDispositionLogic {
	return &UpdateDispositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDispositionLogic) UpdateDisposition(req *types.DispositionInfo) (*types.DispositionCommonResp, error) {
	// 查询指定vid的配置列表
	dispositionInfo, err := l.svcCtx.DispositionModel.FindOneByOrderCode(req.OrderCode)
	if err != nil {
		return nil, errors.New(types.ErrQueryDisposition)
	}

	if dispositionInfo != nil {
		if dispositionInfo.Id != req.Id {
			return nil, errors.New(types.ErrOrderCodeExist)
		}
	}

	updateDisposition := &disposition.Disposition{
		Id:        req.Id,
		OrderCode: req.OrderCode,
		Vid:       req.Vid,
		Remark:    req.Remark,
	}

	err = l.svcCtx.DispositionModel.Update(updateDisposition)
	if err != nil {
		return nil, errors.New(types.ErrUpdateDisposition)
	}

	return &types.DispositionCommonResp{
		Id:      req.Id,
		Message: "更新成功",
	}, nil
}
