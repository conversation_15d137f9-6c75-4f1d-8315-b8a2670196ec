package disposition

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListDispositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDispositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDispositionLogic {
	return &ListDispositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDispositionLogic) ListDisposition(req *types.ListDispositionReq) (*types.ListDispositionResp, error) {
	// 查询指定vid的配置列表
	dispositions, total, err := l.svcCtx.DispositionModel.Find(req.OrderCode, req.Vid, req.Status, req.OrderByCreateTime, req.OrderByUpdateTime, req.<PERSON>, req.PageSize)
	if err != nil {
		return nil, err
	}

	var result []types.DispositionInfo
	for _, disposition := range dispositions {
		result = append(result, types.DispositionInfo{
			Id:         disposition.Id,
			OrderCode:  disposition.OrderCode,
			Vid:        disposition.Vid,
			Remark:     disposition.Remark,
			Status:     disposition.Status,
			CreateTime: disposition.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime: disposition.UpdateTime.Format("2006-01-02 15:04:05"),
		})
	}

	return &types.ListDispositionResp{
		Total: total,
		List:  result,
	}, nil
}
