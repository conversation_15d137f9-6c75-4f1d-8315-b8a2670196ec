package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"music/internal/svc"
)

// 定义缓存键前缀，避免键冲突
const (
	// 过期时间设置
	DefaultCacheExpiry = 24 * time.Hour     // 默认过期时间：24小时
	ShortCacheExpiry   = 5 * time.Minute    // 短期缓存：5分钟
	LongCacheExpiry    = 7 * 24 * time.Hour // 长期缓存：7天

	// 前缀定义
	PrefixUser         = "user:"
	PrefixSong         = "song:"
	PrefixVerification = "verification:"
	PrefixRateLimit    = "rate_limit:"
)

// RedisUtils Redis工具类
type RedisUtils struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	redis  *redis.Redis
	logger logx.Logger
}

// NewRedisUtils 创建Redis工具类实例
func NewRedisUtils(ctx context.Context, svcCtx *svc.ServiceContext) *RedisUtils {
	return &RedisUtils{
		ctx:    ctx,
		svcCtx: svcCtx,
		redis:  svcCtx.Redis,
		logger: logx.WithContext(ctx),
	}
}

// 用户缓存相关方法 -----------------------------

// GetUserCache 获取用户缓存
func (r *RedisUtils) GetUserCache(userUuid string) (*UserCacheInfo, error) {
	key := fmt.Sprintf("%s%s", PrefixUser, userUuid)
	val, err := r.redis.Get(key)
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中
			return r.loadUserCache(userUuid)
		}
		return nil, err
	}

	// 解析缓存数据
	var userInfo UserCacheInfo
	if err := json.Unmarshal([]byte(val), &userInfo); err != nil {
		return nil, err
	}

	return &userInfo, nil
}

// loadUserCache 从数据库加载用户缓存
func (r *RedisUtils) loadUserCache(userUuid string) (*UserCacheInfo, error) {
	// 从数据库查询
	user, err := r.svcCtx.UserModel.FindOneByUuid(userUuid)
	if err != nil {
		return nil, err
	}

	// 构建缓存对象
	userInfo := &UserCacheInfo{
		Id:       user.Id,
		Uuid:     user.Uuid,
		Username: user.Username,
		Nickname: user.Nickname,
		Role:     user.Role,
		Status:   user.Status,
	}

	// 写入缓存
	if err := r.SetUserCache(userInfo); err != nil {
		r.logger.Errorf("缓存用户数据失败: %v", err)
	}

	return userInfo, nil
}

// SetUserCache 设置用户缓存
func (r *RedisUtils) SetUserCache(userInfo *UserCacheInfo) error {
	if userInfo == nil || userInfo.Uuid == "" {
		return errors.New("无效的用户信息")
	}

	key := fmt.Sprintf("%s%s", PrefixUser, userInfo.Uuid)
	data, err := json.Marshal(userInfo)
	if err != nil {
		return err
	}

	return r.redis.Setex(key, string(data), int(DefaultCacheExpiry/time.Second))
}

// DeleteUserCache 删除用户缓存
func (r *RedisUtils) DeleteUserCache(userUuid string) error {
	key := fmt.Sprintf("%s%s", PrefixUser, userUuid)
	_, err := r.redis.Del(key)
	return err
}

// 验证码相关方法 -----------------------------

// SetVerificationCode 设置验证码
func (r *RedisUtils) SetVerificationCode(mobile string, code string, expiry time.Duration) error {
	key := fmt.Sprintf("%s%s", PrefixVerification, mobile)
	return r.redis.Setex(key, code, int(expiry/time.Second))
}

// GetVerificationCode 获取验证码
func (r *RedisUtils) GetVerificationCode(mobile string) (string, error) {
	key := fmt.Sprintf("%s%s", PrefixVerification, mobile)
	return r.redis.Get(key)
}

// VerifyAndDeleteCode 验证并删除验证码
func (r *RedisUtils) VerifyAndDeleteCode(mobile, code string) (bool, error) {
	key := fmt.Sprintf("%s%s", PrefixVerification, mobile)
	val, err := r.redis.Get(key)
	if err != nil {
		if err == redis.Nil {
			return false, errors.New("验证码不存在或已过期")
		}
		return false, err
	}

	if val == code {
		// 验证成功，删除验证码
		_, err := r.redis.Del(key)
		if err != nil {
			r.logger.Errorf("删除验证码失败: %v", err)
		}
		return true, nil
	}

	return false, errors.New("验证码错误")
}

// 限流相关方法 -----------------------------

// CheckRateLimit 检查并限制访问频率
// 返回是否允许访问及剩余时间（秒）
func (r *RedisUtils) CheckRateLimit(key string, limit int, period time.Duration) (bool, int, error) {
	redisKey := fmt.Sprintf("%s%s", PrefixRateLimit, key)

	// 获取当前计数
	countStr, err := r.redis.Get(redisKey)
	if err != nil && err != redis.Nil {
		return false, 0, err
	}

	var count int
	if countStr != "" {
		count, _ = strconv.Atoi(countStr)
	}

	// 检查是否超出限制
	if count >= limit {
		// 获取过期时间
		ttl, err := r.redis.Ttl(redisKey)
		if err != nil {
			return false, 0, err
		}
		return false, ttl, nil
	}

	// 增加计数
	count++
	err = r.redis.Setex(redisKey, strconv.Itoa(count), int(period/time.Second))
	if err != nil {
		return false, 0, err
	}

	// 如果是第一次计数，设置过期时间
	if count == 1 {
		err = r.redis.Expire(redisKey, int(period/time.Second))
		if err != nil {
			r.logger.Errorf("设置过期时间失败: %v", err)
		}
	}

	return true, 0, nil
}

// 分布式锁相关方法 -----------------------------

// TryLock 尝试获取分布式锁
func (r *RedisUtils) TryLock(lockKey string, expiry time.Duration) (bool, error) {
	// 生成唯一值作为锁内容
	value := fmt.Sprintf("%d", time.Now().UnixNano())
	ok, err := r.redis.Setnx(lockKey, value)
	if err != nil {
		return false, err
	}

	if ok {
		// 获取锁成功，设置过期时间
		err = r.redis.Expire(lockKey, int(expiry/time.Second))
		if err != nil {
			// 设置过期时间失败，尝试释放锁
			r.Unlock(lockKey)
			return false, err
		}
		return true, nil
	}

	return false, nil
}

// Unlock 释放分布式锁
func (r *RedisUtils) Unlock(lockKey string) error {
	_, err := r.redis.Del(lockKey)
	return err
}

// 通用方法 -----------------------------

// Get 获取字符串值
func (r *RedisUtils) Get(key string) (string, error) {
	return r.redis.Get(key)
}

// Set 设置字符串值
func (r *RedisUtils) Set(key, value string) error {
	return r.redis.Set(key, value)
}

// SetWithExpiry 设置带过期时间的字符串值
func (r *RedisUtils) SetWithExpiry(key, value string, expiry time.Duration) error {
	return r.redis.Setex(key, value, int(expiry/time.Second))
}

// Delete 删除值
func (r *RedisUtils) Delete(key string) error {
	_, err := r.redis.Del(key)
	return err
}

// HashSet 设置哈希字段
func (r *RedisUtils) HashSet(key, field, value string) error {
	return r.redis.Hset(key, field, value)
}

// HashGet 获取哈希字段
func (r *RedisUtils) HashGet(key, field string) (string, error) {
	return r.redis.Hget(key, field)
}

// HashDelete 删除哈希字段
func (r *RedisUtils) HashDelete(key, field string) error {
	_, err := r.redis.Hdel(key, field)
	return err
}

// 定义缓存实体 -----------------------------

// UserCacheInfo 用户缓存信息
type UserCacheInfo struct {
	Id       int64  `json:"id"`
	Uuid     string `json:"uuid"`
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Role     int64  `json:"role"`
	Status   int64  `json:"status"`
}

// String 方法用于调试
func (u UserCacheInfo) String() string {
	data, _ := json.Marshal(u)
	return string(data)
}
