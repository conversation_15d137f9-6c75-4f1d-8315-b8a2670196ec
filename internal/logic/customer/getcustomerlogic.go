package customer

import (
	"context"
	"errors"
	"music/internal/middleware"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/customer"
)

type GetCustomerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCustomerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCustomerLogic {
	return &GetCustomerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCustomerLogic) GetCustomer(req *types.GetCustomerReq) (*types.GetCustomerResp, error) {
	var c *customer.Customer
	var err error

	if req.Id > 0 {
		c, err = l.svcCtx.CustomerModel.FindOne(req.Id)
	} else if req.Vid > 0 {
		c, err = l.svcCtx.CustomerModel.FindOneByVid(req.Vid)
	} else {
		return nil, errors.New("请提供客户ID或VID")
	}

	if err != nil {
		return nil, err
	}

	return &types.GetCustomerResp{
		Customer: types.CustomerInfo{
			Id:      c.Id,
			Name:    c.Name,
			Vid:     c.Vid,
			Cid:     c.Cid,
			Ip:      c.Ip,
			Appid:   c.Appid,
			Remark:  c.Remark,
			Status:  c.Status,
			EndTime: middleware.UnixToDate(c.EndTime),
		},
	}, nil
}
