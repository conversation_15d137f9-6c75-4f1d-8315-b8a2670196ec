package customer

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListVidLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListVidLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListVidLogic {
	return &ListVidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListVidLogic) ListVid(req *types.VidOrCidReq) (*types.VidOrCidResp, error) {

	vid, err := l.svcCtx.CustomerModel.FindVid(req.Name)
	if err != nil {
		return nil, errors.New(types.ErrCustomerVidNotFound)
	}

	return &types.VidOrCidResp{
		Vid: vid,
	}, nil
}
