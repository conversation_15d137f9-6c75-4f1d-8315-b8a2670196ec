package customer

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListCidLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListCidLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListCidLogic {
	return &ListCidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListCidLogic) ListCid(req *types.VidOrCidReq) (*types.VidOrCidResp, error) {

	cid, err := l.svcCtx.CustomerModel.FindCid(req.Name)
	if err != nil {
		return nil, errors.New(types.ErrCustomerCidNotFound)
	}

	return &types.VidOrCidResp{
		Cid: cid,
	}, nil
}
