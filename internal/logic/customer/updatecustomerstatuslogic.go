package customer

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UpdateCustomerStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateCustomerStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateCustomerStatusLogic {
	return &UpdateCustomerStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateCustomerStatusLogic) UpdateCustomerStatusLogic(req *types.UpdateCustomerStatusReq) (*types.UpdateCustomerResp, error) {
	var endTime int64 = 0
	var err error

	// 关闭状态 和 过期时间没有关联
	//if req.Status == types.CustomerClose {
	//	// 关闭客户，设置结束时间
	//	endTime, err = middleware.DateToUnix(time.Now().Format("2006-01-02"))
	//	if err != nil {
	//		return nil, err
	//	}
	//} else {
	customer, err := l.svcCtx.CustomerModel.FindOne(req.Id)
	if err != nil {
		return nil, errors.New(types.ErrNoCustomerInfo)
	}
	endTime = customer.EndTime
	//}

	err = l.svcCtx.CustomerModel.UpdateStatus(req.Id, req.Status, endTime)
	if err != nil {
		return nil, errors.New(types.ErrUpdateCustomerStatus)
	}

	return &types.UpdateCustomerResp{
		Success: true,
		Message: "更新客户状态成功",
	}, nil
}
