package customer

import (
	"context"
	"errors"
	"music/internal/middleware"
	"music/model/customer"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type AddCustomerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddCustomerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddCustomerLogic {
	return &AddCustomerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddCustomerLogic) AddCustomerLogic(req *types.AddCustomerInfo) (*types.UpdateCustomerResp, error) {
	var endTime int64 = 0
	var err error

	_, err = l.checkCustomer(req)
	if err != nil {
		return nil, err
	}

	// 暂时不限制
	//if req.Status == types.CustomerClose && req.EndTime == "" {
	//	return nil, errors.New(types.ErrNeedSetEndTime)
	//}
	//
	//if req.Status == types.CustomerOpen && req.EndTime != "" {
	//	return nil, errors.New(types.ErrSetEndTime)
	//}

	if req.EndTime != "" {
		endTime, err = middleware.TimeToUnix(req.EndTime)
		if err != nil {
			return nil, err
		}
	}

	/*
		// 关闭版权方，设置结束时间
				endTime, err = middleware.DateToUnix(time.Now().Format("2006-01-02"))
				if err != nil {
					return nil, errors.New(types.ErrFormatTime)
				}
	*/

	cu := &customer.Customer{
		Name:       req.Name,
		Status:     req.Status,
		Vid:        req.Vid,
		Cid:        req.Cid,
		Appid:      req.Appid,
		Remark:     req.Remark,
		Ip:         req.Ip,
		EndTime:    endTime,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}

	result, err := l.svcCtx.CustomerModel.Insert(cu)
	if err != nil {
		return nil, errors.New(types.ErrCreateCustomer)
	}

	customerId, err := result.LastInsertId()
	if err != nil {
		return nil, errors.New(types.ErrCreateCustomer)
	}

	return &types.UpdateCustomerResp{
		Id:      customerId,
		Success: true,
		Message: "创建客户成功",
	}, nil
}

func (l *AddCustomerLogic) checkCustomer(req *types.AddCustomerInfo) (bool, error) {
	// 检查客户名称是否已存在
	/*info, err := l.svcCtx.CustomerModel.FindOneByNameNotLike(req.Name)
	if err != nil {
		return false, errors.New(types.ErrQueryCustomerInfo)
	}

	if info != nil {
		return false, errors.New(types.ErrCustomerNameExists)
	}*/

	info, err := l.svcCtx.CustomerModel.FindOneByVid(req.Vid)
	if err != nil {
		return false, errors.New(types.ErrQueryCustomerInfo)
	}

	if info != nil {
		return false, errors.New(types.ErrCustomerVidExists)
	}

	/*	info, err = l.svcCtx.CustomerModel.FindOneByCid(req.Cid)
		if err != nil {
			return false, errors.New(types.ErrQueryCustomerInfo)
		}

		if info != nil {
			return false, errors.New(types.ErrCustomerCidExists)
		}*/

	return true, nil

}
