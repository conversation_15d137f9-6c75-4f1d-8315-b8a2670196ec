package customer

import (
	"context"
	"errors"
	"music/internal/middleware"
	"music/model/customer"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UpdateCustomerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateCustomerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateCustomerLogic {
	return &UpdateCustomerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateCustomerLogic) UpdateCustomerLogic(req *types.CustomerInfo) (*types.UpdateCustomerResp, error) {
	var endTime int64 = 0
	var err error

	_, err = l.checkCustomer(req)
	if err != nil {
		return nil, err
	}

	// 暂时不限制
	//if req.Status == types.CustomerClose && req.EndTime == "" {
	//	return nil, errors.New(types.ErrNeedSetEndTime)
	//}
	//
	//if req.Status == types.CustomerOpen && req.EndTime != "" {
	//	return nil, errors.New(types.ErrSetEndTime)
	//}

	if req.EndTime != "" {
		endTime, err = middleware.TimeToUnix(req.EndTime)
		if err != nil {
			return nil, err
		}
	}

	info := &customer.Customer{
		Id:         req.Id,
		Name:       req.Name,
		Cid:        req.Cid,
		Vid:        req.Vid,
		Remark:     req.Remark,
		Appid:      req.Appid,
		Ip:         req.Ip,
		Status:     req.Status,
		EndTime:    endTime,
		UpdateTime: time.Now(),
	}

	// 更新客户信息
	err = l.svcCtx.CustomerModel.Update(info)
	if err != nil {
		return nil, errors.New(types.ErrUpdateCustomer)
	}

	return &types.UpdateCustomerResp{
		Success: true,
		Message: "更新客户成功",
	}, nil
}

func (l *UpdateCustomerLogic) checkCustomer(req *types.CustomerInfo) (bool, error) {
	// 检查客户名称是否已存在
	/*info, err := l.svcCtx.CustomerModel.FindOneByNameNotLike(req.Name)
	if err != nil {
		return false, errors.New(types.ErrQueryCustomerInfo)
	}

	if info != nil {
		if info.Id != req.Id {
			return false, errors.New(types.ErrCustomerNameExists)
		}
	}*/

	info, err := l.svcCtx.CustomerModel.FindOneByVid(req.Vid)
	if err != nil {
		return false, errors.New(types.ErrQueryCustomerInfo)
	}

	if info != nil {
		if info.Id != 0 && info.Id != req.Id {
			return false, errors.New(types.ErrCustomerVidExists)
		}
	}

	/*info, err = l.svcCtx.CustomerModel.FindOneByCid(req.Cid)
	if err != nil {
		return false, errors.New(types.ErrQueryCustomerInfo)
	}

	if info != nil {
		if info.Id != 0 && info.Id != req.Id {
			return false, errors.New(types.ErrCustomerCidExists)
		}
	}*/

	return true, nil

}
