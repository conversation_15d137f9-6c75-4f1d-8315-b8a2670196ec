package customer

import (
	"context"
	"music/internal/middleware"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListCustomersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListCustomersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListCustomersLogic {
	return &ListCustomersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListCustomersLogic) ListCustomers(req *types.ListCustomerReq) (*types.ListCustomerResp, error) {

	beginEndTime, err := middleware.DateToUnix(req.BeginEndTime)
	if err != nil {
		return nil, err
	}

	endEndTime, err := middleware.DateToUnix(req.EndEndTime)
	if err != nil {
		return nil, err
	}

	// 查询客户列表
	customers, total, err := l.svcCtx.CustomerModel.Find(req.Name, req.Vid, req.Cid, beginEndTime, endEndTime, req.Status, req.Ip, req.OrderByCreateTime, req.OrderByUpdateTime, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	var result []types.CustomerInfo
	for _, customer := range customers {
		result = append(result, types.CustomerInfo{
			Id:         customer.Id,
			Name:       customer.Name,
			Vid:        customer.Vid,
			Cid:        customer.Cid,
			Ip:         customer.Ip,
			Appid:      customer.Appid,
			Remark:     customer.Remark,
			Status:     customer.Status,
			EndTime:    middleware.UnixToTime(customer.EndTime),
			CreateTime: customer.CreateTime.Format(time.DateTime),
			UpdateTime: customer.UpdateTime.Format(time.DateTime),
		})
	}

	return &types.ListCustomerResp{
		Total: total,
		List:  result,
	}, nil
}
