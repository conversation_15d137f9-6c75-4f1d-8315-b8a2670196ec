package disposition

import (
	"context"
	"errors"
	"music/internal/middleware"
	"music/model/disposition"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type AddDispositionDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddDispositionDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddDispositionDetailLogic {
	return &AddDispositionDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddDispositionDetailLogic) AddDispositionDetail(req *types.DispositionDetailInfo) (*types.DispositionCommonResp, error) {

	var err error
	// 查询指定vid的配置列表
	dispositionInfo, err := l.svcCtx.DispositionModel.FindOneByOrderCode(req.OrderCode)
	if err != nil {
		return nil, errors.New(types.ErrQueryDisposition)
	}

	if dispositionInfo == nil {
		return nil, errors.New(types.ErrOrderCodeNotExist)
	}

	endTime, err := middleware.TimeToUnix(req.EndTime)
	if err != nil {
		return nil, err
	}

	// status  默认开启
	req.Status = types.DispositionOrDispositionDetailOpen

	addDispositionDetail := &disposition.DispositionDetail{
		OrderCode:     req.OrderCode,
		Vid:           req.Vid,
		ResourceTypes: req.ResourceTypes,
		CpId:          req.CpId,
		BillingType:   req.BillingType,
		EndTime:       endTime,
		Remark:        req.Remark,
		Status:        req.Status,
	}

	result, err := l.svcCtx.DispositionDetailModel.Insert(addDispositionDetail)
	if err != nil {
		return nil, errors.New(types.ErrInsertDispositionDetail)
	}

	dispositionDetailId, err := result.LastInsertId()
	if err != nil {
		return nil, errors.New(types.ErrInsertDispositionDetail)
	}

	return &types.DispositionCommonResp{
		Id:      dispositionDetailId,
		Message: "添加成功",
	}, nil
}
