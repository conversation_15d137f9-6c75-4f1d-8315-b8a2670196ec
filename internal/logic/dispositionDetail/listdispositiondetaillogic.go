package disposition

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

type ListDispositionDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDispositionDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDispositionDetailLogic {
	return &ListDispositionDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDispositionDetailLogic) ListDispositionDetail(req *types.ListDispositionDetailReq) (*types.ListDispositionDetailResp, error) {

	dispositionDetails, total, err := l.svcCtx.DispositionDetailModel.Find(req.OrderCode, req.CpId, req.Status, req.OrderByCreateTime, req.OrderByUpdateTime, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	var result []types.DispositionDetailInfo
	for _, dispositionDetail := range dispositionDetails {
		endTime := middleware.UnixToTime(dispositionDetail.EndTime)
		result = append(result, types.DispositionDetailInfo{
			Id:            dispositionDetail.Id,
			OrderCode:     dispositionDetail.OrderCode,
			CpId:          dispositionDetail.CpId,
			ResourceTypes: dispositionDetail.ResourceTypes,
			Vid:           dispositionDetail.Vid,
			BillingType:   dispositionDetail.BillingType,
			EndTime:       endTime,
			Remark:        dispositionDetail.Remark,
			Status:        dispositionDetail.Status,
			CreateTime:    dispositionDetail.CreateTime.Format(time.DateTime),
			UpdateTime:    dispositionDetail.UpdateTime.Format(time.DateTime),
		})
	}

	return &types.ListDispositionDetailResp{
		Total: total,
		List:  result,
	}, nil
}
