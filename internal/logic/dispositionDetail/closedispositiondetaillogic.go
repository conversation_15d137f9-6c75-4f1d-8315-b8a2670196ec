package disposition

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
)

type CloseOrOpenDispositionDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCloseOrOpenDispositionDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloseOrOpenDispositionDetailLogic {
	return &CloseOrOpenDispositionDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CloseOrOpenDispositionDetailLogic) CloseOrOpenDispositionDetail(req *types.CloseOrOpenDispositionCommonReq) (*types.DispositionCommonResp, error) {

	dispositionDetailInfo, err := l.svcCtx.DispositionDetailModel.FindOne(req.Id)
	if err != nil {
		return nil, errors.New(types.ErrQueryDispositionDetail)
	}

	if dispositionDetailInfo == nil {
		return nil, errors.New(types.ErrDispositionDetailNotFound)
	}

	// 校验 权限 是否关闭
	disp, err := l.svcCtx.DispositionModel.FindOneByOrderCode(dispositionDetailInfo.OrderCode)
	if err != nil {
		return nil, errors.New(types.ErrQueryDisposition)
	}

	if disp.Status == types.DispositionOrDispositionDetailClose && req.Status == types.DispositionOrDispositionDetailOpen {
		return nil, errors.New(types.ErrDispositionIsClosedCannotOpenDetail)
	}

	err = l.svcCtx.DispositionDetailModel.CloseOrOpen(req.Id, req.Status)
	if err != nil {
		return nil, errors.New(types.ErrCloseOrOpen)
	}

	return &types.DispositionCommonResp{
		Id:      req.Id,
		Message: "更新成功",
	}, nil
}
