package disposition

import (
	"context"
	"errors"
	"music/internal/middleware"
	"music/model/disposition"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UpdateDispositionDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDispositionDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDispositionDetailLogic {
	return &UpdateDispositionDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDispositionDetailLogic) UpdateDispositionDetail(req *types.DispositionDetailInfo) (*types.DispositionCommonResp, error) {

	_, err := l.svcCtx.DispositionDetailModel.FindOne(req.Id)
	if err != nil {
		return nil, errors.New(types.ErrQueryDispositionDetail)
	}

	endTime, err := middleware.TimeToUnix(req.EndTime)
	if err != nil {
		return nil, err
	}

	updateDispositionDetail := &disposition.DispositionDetail{
		Id:            req.Id,
		Vid:           req.Vid,
		ResourceTypes: req.ResourceTypes,
		CpId:          req.CpId,
		BillingType:   req.BillingType,
		EndTime:       endTime,
		Remark:        req.Remark,
	}

	err = l.svcCtx.DispositionDetailModel.Update(updateDispositionDetail)
	if err != nil {
		return nil, errors.New(types.ErrUpdateDispositionDetail)
	}

	return &types.DispositionCommonResp{
		Id:      req.Id,
		Message: "更新成功",
	}, nil
}
