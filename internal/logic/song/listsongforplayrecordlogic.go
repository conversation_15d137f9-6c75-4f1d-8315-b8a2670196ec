package song

import (
	"context"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/song"
)

type ListSongForPlayRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListSongForPlayRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSongForPlayRecordLogic {
	return &ListSongForPlayRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListSongForPlayRecordLogic) ListSongsForPlayRecord(req *types.ListSongForPlayRecordReq) (*types.ListSongForHotResp, error) {
	var songs []types.SongInfoForHot
	var total int64
	var err error

	// 根据  Vid  查询 cpid
	customer, err := l.svcCtx.CustomerModel.FindOneByVid(req.Vid)
	if err != nil {
		return nil, errors.New(types.ErrNoCustomerInfo)
	}

	if customer == nil {
		return nil, errors.New(types.ErrNoCustomerInfo)
	}

	fmt.Printf("customer %+v\n", customer)

	// 执行查询
	songList, count, err := l.svcCtx.SongModel.FindByCpId(customer.Cid, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	total = int64(count)
	songs = l.convertSongList(songList)

	return &types.ListSongForHotResp{
		Total: total,
		List:  songs,
	}, nil
}

func (l *ListSongForPlayRecordLogic) convertSongList(songList []*song.Song) []types.SongInfoForHot {
	var songs []types.SongInfoForHot
	for _, s := range songList {
		songs = append(songs, types.SongInfoForHot{
			Id:           s.Id,
			ImportId:     s.ImportId,
			Type:         s.Type,
			VendorId:     s.VendorId,
			SongCode:     s.SongCode,
			Name:         s.Name,
			Singer:       s.Singer,
			VendorSongId: s.VendorSongId,
			CloseTime:    s.CloseTime,
			Status:       s.Status,
		})
	}
	return songs
}
