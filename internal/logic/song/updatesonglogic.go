package song

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/log"
	"music/model/song"
)

type UpdateSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateSongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateSongLogic {
	return &UpdateSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateSongLogic) UpdateSong(req *types.UpdateSongReq) (*types.UpdateSongResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 管理员权限验证
	if authUser.Role < 1 { // 假设角色1以上为管理员
		return &types.UpdateSongResp{
			Success: false,
			Message: "权限不足，需要管理员权限",
		}, nil
	}

	// 验证请求参数
	if req.Id <= 0 {
		return &types.UpdateSongResp{
			Success: false,
			Message: "歌曲ID不能为空",
		}, nil
	}

	if req.Name == "" {
		return &types.UpdateSongResp{
			Success: false,
			Message: "歌曲名称不能为空",
		}, nil
	}

	/*if req.Singer == "" {
		return &types.UpdateSongResp{
			Success: false,
			Message: "歌手名称不能为空",
		}, nil
	}*/

	// 查找歌曲
	oldSong, err := l.svcCtx.SongModel.FindOne(req.Id)
	if err != nil {
		if errors.Is(err, song.ErrSongNotFound) {
			return &types.UpdateSongResp{
				Success: false,
				Message: "歌曲不存在",
			}, nil
		}
		return nil, fmt.Errorf("查询歌曲失败: %w", err)
	}

	// 检查歌曲编号是否已被其他歌曲使用（如果歌曲编号发生变化）
	if req.SongCode != oldSong.SongCode && req.SongCode > 0 {
		existingSong, err := l.svcCtx.SongModel.FindOneBySongCode(req.SongCode)
		if err == nil && existingSong.Id != req.Id {
			return &types.UpdateSongResp{
				Success: false,
				Message: fmt.Sprintf("歌曲编号 %d 已被其他歌曲使用", req.SongCode),
			}, nil
		}
	} else {
		// 如果未指定新的编号，保持原编号
		req.SongCode = oldSong.SongCode
	}

	// 检查版权方是否存在
	if req.VendorId > 0 {
		if req.VendorId != oldSong.VendorId {
			_, err = l.svcCtx.CpModel.FindOne(req.VendorId)
			if err != nil {
				return &types.UpdateSongResp{
					Success: false,
					Message: "指定的版权方不存在",
				}, nil
			}
		}
		// 检查版权方是否有效  暂不限制
		//if vendor.Status != 1 {
		//	return &types.UpdateSongResp{
		//		Success: false,
		//		Message: "指定的版权方已被禁用",
		//	}, nil
		//}
	}

	// 记录修改的字段信息
	var changedFields []string
	if oldSong.Name != req.Name {
		changedFields = append(changedFields, "歌曲名称")
	}
	if oldSong.Singer != req.Singer {
		changedFields = append(changedFields, "歌手")
	}
	if oldSong.VendorId != req.VendorId {
		changedFields = append(changedFields, "版权方")
	}
	/*if oldSong.Status != req.Status {
		changedFields = append(changedFields, "状态")
	}*/

	// 构建更新数据
	songData := &song.Song{
		Id:                req.Id,
		ImportId:          req.ImportId,
		Type:              req.Type,
		VendorId:          req.VendorId,
		SongCode:          req.SongCode,
		Name:              req.Name,
		Singer:            req.Singer,
		VendorSongId:      req.VendorSongId,
		VendorReleaseTime: req.VendorReleaseTime,
		SongPath:          req.SongPath,
		DrmPath:           req.DrmPath,
		LicenseKey:        req.LicenseKey,
		PosterPath:        req.PosterPath,
		LyricPath:         req.LyricPath,
		LyricType:         req.LyricType,
		PitchType:         req.PitchType,
		Pitchs:            req.Pitchs,
		HighPart:          req.HighPart,
		HighPartType:      req.HighPartType,
		QualityLevel:      req.QualityLevel,
		TagIds:            req.TagIds,
		Duration:          req.Duration,
		HotNum:            req.HotNum,
		//Status:            req.Status,
	}

	// 如果歌曲状态修改为下架，设置下架时间
	if oldSong.Status == 1 && songData.Status == 0 {
		songData.CloseTime = time.Now().Unix() + 3600 // 1小时后正式下架
	} else if oldSong.Status == 0 && songData.Status == 1 {
		// 如果歌曲状态改为上架，清除下架时间
		songData.CloseTime = 0
	} else {
		// 保持原下架时间
		songData.CloseTime = oldSong.CloseTime
	}

	// 执行更新
	err = l.svcCtx.SongModel.Update(songData)
	if err != nil {
		return nil, fmt.Errorf("更新歌曲失败: %w", err)
	}

	// 记录操作日志
	oldValue, _ := json.Marshal(oldSong)
	newValue, _ := json.Marshal(songData)

	// 获取IP和用户代理
	ip := "未知"
	userAgent := "未知"
	if ipVal := l.ctx.Value("remoteAddr"); ipVal != nil {
		ip = ipVal.(string)
	}
	if uaVal := l.ctx.Value("userAgent"); uaVal != nil {
		userAgent = uaVal.(string)
	}

	// 创建描述性操作日志记录
	//changeDesc := "更新了歌曲信息"
	//if len(changedFields) > 0 {
	//	changeDesc = fmt.Sprintf("修改了歌曲的以下字段: %s", strings.Join(changedFields, ", "))
	//}

	// 创建操作日志
	operationLog := &log.OperationLog{
		UserUuid:   authUser.UserUuid,
		Username:   authUser.Username,
		Module:     "song",
		Operation:  "update",
		EntityId:   songData.Id,
		EntityName: songData.Name,
		OldValue:   string(oldValue),
		NewValue:   string(newValue),
		Ip:         ip,
		UserAgent:  userAgent,
	}

	_, err = l.svcCtx.OperationLogModel.Insert(operationLog)
	if err != nil {
		l.Logger.Error("记录操作日志失败", logx.Field("error", err))
	}

	// 记录详细日志
	l.Logger.Info("歌曲信息已更新",
		logx.Field("id", req.Id),
		logx.Field("name", req.Name),
		logx.Field("user", authUser.Username),
		logx.Field("changes", changedFields))

	return &types.UpdateSongResp{
		Success: true,
		Message: "更新成功",
	}, nil
}
