package song

import (
	"context"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"io/ioutil"
	"music/internal/svc"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/tealeg/xlsx"

	"music/internal/types"
	"music/model/song"
)

// ImportResult 导入结果结构
type ImportResult struct {
	TotalCount    int            // 总记录数
	SuccessCount  int            // 成功导入数
	FailedCount   int            // 失败数
	FailedRecords map[int]string // 失败详情，行号:原因
	ErrorMessage  string         // 整体错误信息
}

type ImportSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewImportSongLogic(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *ImportSongLogic {
	return &ImportSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

// ImportSong 导入歌曲
func (l *ImportSongLogic) ImportSong(req *types.ImportReq) (resp *types.ImportResp, err error) {
	fmt.Println("开始导入歌曲")
	file, header, err := l.r.FormFile("file")
	if err != nil {
		fmt.Println("获取文件失败:", err)
		return nil, errors.New(types.ErrReadFile)
	}
	defer file.Close()

	// 限制文件大小（例如 10MB）
	maxSize := int64(100 << 20) // 100MB
	if header.Size > maxSize {
		return nil, errors.New(types.ErrSizeFile)
	}

	// 读取文件内容
	fileBytes, err := ioutil.ReadAll(file)
	if err != nil {
		return nil, errors.New(types.ErrReadFile)
	}

	// 生成唯一文件名（防止覆盖）
	filename := generateUniqueName(header.Filename)
	// 检测 uploads 目录是否存在
	uploadDir := filepath.FromSlash(l.svcCtx.Config.File.UploadPath)
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, errors.New(types.ErrCreateDir)
	}
	savePath := path.Join(uploadDir, filename) // 保存到本地 uploads 目录
	defer os.Remove(savePath)                  // 删除临时文件

	// 确保 exports 目录存在
	exportDir := filepath.FromSlash(l.svcCtx.Config.File.ExportPath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return nil, errors.New(types.ErrCreateDir)
	}

	// 设置失败记录输出路径 - 使用原文件名+时间戳+_failed.xlsx
	fileExt := filepath.Ext(header.Filename)
	baseFilename := filepath.Base(header.Filename[:len(header.Filename)-len(fileExt)])
	timestamp := time.Now().Format("20060102150405")
	failedFileName := filepath.Join(filepath.Dir(header.Filename), fmt.Sprintf("%s_%s_失败%s", baseFilename, timestamp, fileExt))
	failedOutputPath := path.Join(exportDir, failedFileName)

	// 保存文件到本地
	if err := ioutil.WriteFile(savePath, fileBytes, 0644); err != nil {
		return nil, errors.New(types.ErrSaveFileToLocal)
	}

	// 调用导入功能
	result, err := ImportSongsFromExcel(l.svcCtx.SongModel, savePath, failedOutputPath)
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp = &types.ImportResp{
		TotalCount:   result.TotalCount,
		SuccessCount: result.SuccessCount,
		FailedCount:  result.FailedCount,
		//FailedFile:   failedFileName,
		Message: fmt.Sprintf("导入完成：总计%d条，成功%d条，失败%d条", result.TotalCount, result.SuccessCount, result.FailedCount),
	}

	if result.ErrorMessage != "" {
		resp.Message += fmt.Sprintf("，警告：%s", result.ErrorMessage)
	}

	if result.FailedCount > 0 {
		resp.FailedFile = failedFileName
		return resp, errors.New("导入操作完成，但有失败记录，请查看失败文件")
	}

	return resp, nil
}

// ImportSongsFromExcel 从Excel文件导入歌曲数据
func ImportSongsFromExcel(songModel song.SongModel, filePath, failedOutputPath string) (*ImportResult, error) {
	result := &ImportResult{
		FailedRecords: make(map[int]string),
	}

	// 检查文件是否为Excel格式
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != ".xlsx" && ext != ".xls" {
		return result, fmt.Errorf("不支持的文件格式: %s, 请使用Excel文件(.xlsx或.xls)", ext)
	}

	// 打开Excel文件
	xlFile, err := xlsx.OpenFile(filePath)
	if err != nil {
		return result, fmt.Errorf("无法打开Excel文件: %w", err)
	}

	// 确保至少有一个工作表
	if len(xlFile.Sheets) == 0 {
		return result, fmt.Errorf("Excel文件不包含任何工作表")
	}

	// 使用第一个工作表
	sheet := xlFile.Sheets[0]
	if len(sheet.Rows) < 2 { // 至少需要标题行和一行数据
		return result, fmt.Errorf("Excel工作表数据不足，至少需要标题行和一行数据")
	}

	// 读取标题行
	headerRow := sheet.Rows[0]
	if len(headerRow.Cells) < 3 { // 至少需要歌曲编号、歌曲名称和歌手
		return result, fmt.Errorf("Excel表头数据不足，至少需要歌曲编号、歌曲名称和歌手")
	}

	// 创建映射表
	headerMap := createHeaderMap()

	// 遍历数据行并解析
	var songs []*song.Song
	for rowIndex := 1; rowIndex < len(sheet.Rows); rowIndex++ {
		row := sheet.Rows[rowIndex]

		fmt.Println("正在解析第", rowIndex, "行数据")

		fmt.Printf("第 %d 行数据: %+v\n", rowIndex, row)
		// 跳过标题行
		if isEmptyRow(row) {
			continue
		}

		// 解析行数据为Song对象
		song, err := parseExcelRowToSong(row, headerMap)
		if err != nil {
			result.FailedRecords[rowIndex] = err.Error()
			continue
		}
		fmt.Printf("第 %d 行歌曲数据: %+v", rowIndex, song)

		songs = append(songs, song)
	}

	result.TotalCount = len(songs)

	if len(songs) == 0 {
		return result, fmt.Errorf("没有找到有效的歌曲数据")
	}

	// 批量导入到数据库
	successCount, failedSongs, err := songModel.BatchImport(songs)
	if err != nil {
		return result, fmt.Errorf("导入歌曲失败: %w", err)
	}

	result.SuccessCount = successCount
	result.FailedCount = len(failedSongs)

	// 如果有失败记录且指定了输出路径，记录失败详情
	if len(failedSongs) > 0 && failedOutputPath != "" {
		/*	if err := writeFailedRecordsToExcel(songs, failedSongs, sheet, failedOutputPath); err != nil {
			result.ErrorMessage = fmt.Sprintf("写入失败记录时出错: %s", err.Error())
		}*/
		if err := writeFailedRecordsToExcelFromRow(failedSongs, sheet, failedOutputPath); err != nil {
			result.ErrorMessage = fmt.Sprintf("写入失败记录时出错: %s", err.Error())
		}
	}

	return result, nil
}

// createHeaderMap 创建Excel表头到Song字段的映射
func createHeaderMap() map[string]string {
	// Excel列名到Song结构体字段的映射
	return map[string]string{
		"歌曲编号":      "SongCode",
		"歌曲类型":      "Type",
		"歌曲名称":      "Name",
		"歌手名称":      "Singer",
		"版权方ID":     "VendorId",
		"版权方歌曲ID":   "VendorSongId",
		"发布日期":      "VendorReleaseTime",
		"歌曲文件路径":    "SongPath",
		"歌曲时长":      "Duration",
		"歌曲DRM文件路径": "DrmPath",
		"DRM密钥":     "LicenseKey",
		"歌词文件路径":    "LyricPath",
		"歌词类型":      "LyricType",
		"副歌片段":      "HighPart",
		"副歌类型":      "HighPartType",
		"海报路径":      "PosterPath",
		"是否支持打分":    "PitchType",
		"打分类型":      "Pitchs",
		"码率":        "QualityLevel",
		"热度值":       "HotNum",
		"标签":        "TagIds",
	}
}

// isEmptyRow 检查是否为空行
func isEmptyRow(row *xlsx.Row) bool {
	if len(row.Cells) == 0 {
		return true
	}

	for _, cell := range row.Cells {
		if strings.TrimSpace(cell.String()) != "" {
			return false
		}
	}
	return true
}

// parseExcelRowToSong 解析Excel行为Song对象
func parseExcelRowToSong(row *xlsx.Row, headerMap map[string]string) (*song.Song, error) {
	s := &song.Song{}

	// 解析歌曲名称（必填）
	if len(row.Cells) > 0 {
		name := strings.TrimSpace(row.Cells[0].String())
		if name == "" {
			return nil, fmt.Errorf("歌曲名称为空")
		}
		s.Name = name
	} else {
		return nil, fmt.Errorf("歌曲名称为空")
	}

	//// 解析歌曲编号（必填）
	//if len(row.Cells) > 1 {
	//	songCode, err := row.Cells[1].Int64()
	//	if err != nil || songCode <= 1 {
	//		return nil, fmt.Errorf("歌曲编号无效: %v", err)
	//	}
	//	s.SongCode = songCode
	//} else {
	//	return nil, fmt.Errorf("歌曲编号为空")
	//}

	// 解析歌曲类型
	if len(row.Cells) > 1 {
		typeVal := row.Cells[1].String()
		if typeVal != "" {
			s.Type = types.SongTypeMap[typeVal]
		} else {
			s.Type = types.SongTypeMap["原唱&伴奏"]
		}
	}

	// 解析歌手名称
	if len(row.Cells) > 2 && row.Cells[2] != nil {
		s.Singer = strings.TrimSpace(row.Cells[2].String())
	}

	// 解析版权方ID
	if len(row.Cells) > 3 && row.Cells[3] != nil {
		vendorId, err := row.Cells[3].Int64()
		if err == nil {
			s.VendorId = vendorId
		}
	}

	// 解析版权方歌曲ID
	if len(row.Cells) > 4 && row.Cells[4] != nil {
		s.VendorSongId = strings.TrimSpace(row.Cells[4].String())
	}

	// 解析发布时间
	if len(row.Cells) > 5 && row.Cells[5] != nil {
		s.VendorReleaseTime = strings.TrimSpace(row.Cells[5].String())
	}

	// 解析歌曲文件路径
	if len(row.Cells) > 6 && row.Cells[6] != nil {
		s.SongPath = strings.TrimSpace(row.Cells[6].String())
	}

	// 解析歌曲时长
	if len(row.Cells) > 7 && row.Cells[7] != nil {
		duration, err := row.Cells[7].Int64()
		if err == nil {
			s.Duration = duration
		}
	}

	// 解析歌曲DRM文件路径
	if len(row.Cells) > 8 && row.Cells[8] != nil {
		s.DrmPath = strings.TrimSpace(row.Cells[8].String())
	}

	// 解析DRM密钥
	if len(row.Cells) > 9 && row.Cells[9] != nil {
		s.LicenseKey = strings.TrimSpace(row.Cells[9].String())
	}

	// 解析歌词文件路径
	if len(row.Cells) > 10 && row.Cells[10] != nil {
		s.LyricPath = strings.TrimSpace(row.Cells[10].String())
	}

	// 解析歌词类型
	if len(row.Cells) > 11 && row.Cells[11] != nil {
		lyricType := strings.TrimSpace(row.Cells[11].String())
		if lyricType != "" {
			s.LyricType = types.SongLyricTypeMap[lyricType]
		}
	}

	// 解析副歌片段
	if len(row.Cells) > 12 && row.Cells[12] != nil {
		s.HighPart = strings.TrimSpace(row.Cells[12].String())
	}

	// 解析副歌类型
	if len(row.Cells) > 13 && row.Cells[13] != nil {
		highPartType := row.Cells[13].String()
		if highPartType != "" {
			s.HighPartType = types.SongHighPartTypeMap[highPartType]
		} else {
			s.HighPartType = types.SongHighPartTypeMap["无副歌"]
		}
	}

	// 解析海报路径
	if len(row.Cells) > 14 && row.Cells[14] != nil {
		s.PosterPath = strings.TrimSpace(row.Cells[14].String())
	}

	// 解析打分类型
	if len(row.Cells) > 15 && row.Cells[15] != nil {
		pitchType := row.Cells[15].String()
		if pitchType == "是" {
			s.PitchType = 1
		} else if pitchType == "" || pitchType == "否" {
			s.PitchType = 2
		}

	}

	// 解析打分内容
	if len(row.Cells) > 16 && row.Cells[16] != nil {
		s.Pitchs = strings.TrimSpace(row.Cells[16].String())
	}

	// 解析码率
	if len(row.Cells) > 17 && row.Cells[17] != nil {
		s.QualityLevel = strings.TrimSpace(row.Cells[17].String())
	}

	// 解析热度值
	if len(row.Cells) > 18 && row.Cells[18] != nil {
		hotNum, err := row.Cells[18].Int64()
		if err == nil {
			s.HotNum = hotNum
		}
	}

	// 解析标签
	if len(row.Cells) > 19 && row.Cells[19] != nil {
		s.TagIds = strings.TrimSpace(row.Cells[19].String())
	}

	// 设置默认状态为上架
	s.Status = types.Up

	return s, nil
}

// writeFailedRecordsToExcel 将失败记录写入Excel文件
func writeFailedRecordsToExcel(songs []*song.Song, failedSongs map[int]string, originalSheet *xlsx.Sheet, outputPath string) error {
	// 创建新Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("失败记录")
	if err != nil {
		return err
	}

	// 复制原始表头并添加"失败原因"列
	headerRow := sheet.AddRow()
	if len(originalSheet.Rows) > 0 {
		for _, cell := range originalSheet.Rows[0].Cells {
			headerRow.AddCell().SetString(cell.String())
		}
	}
	headerRow.AddCell().SetString("失败原因")

	// 写入失败记录
	for idx, reason := range failedSongs {
		if idx >= 0 && idx < len(songs) {
			song := songs[idx]

			// 添加新行
			row := sheet.AddRow()

			// 添加歌曲名称
			row.AddCell().SetString(song.Name)

			// 添加歌曲编号
			//row.AddCell().SetString(strconv.Itoa(int(song.SongCode)))

			// 添加歌曲类型
			row.AddCell().SetInt64(song.Type)

			// 添加歌手
			row.AddCell().SetString(song.Singer)

			// 添加版权方ID
			row.AddCell().SetInt64(song.VendorId)

			// 添加版权方歌曲ID
			row.AddCell().SetString(song.VendorSongId)

			// 添加发布日期
			row.AddCell().SetString(song.VendorReleaseTime)

			// 添加歌曲文件路径
			row.AddCell().SetString(song.SongPath)

			// 添加歌曲时长
			row.AddCell().SetInt64(song.Duration)

			// 添加歌曲DRM文件路径
			row.AddCell().SetString(song.DrmPath)

			// 添加DRM密钥
			row.AddCell().SetString(song.LicenseKey)

			// 添加歌词文件路径
			row.AddCell().SetString(song.LyricPath)

			// 添加歌词类型
			row.AddCell().SetString(song.LyricType)

			// 添加副歌片段路径
			row.AddCell().SetString(song.HighPart)

			// 添加副歌类型
			row.AddCell().SetInt64(song.HighPartType)

			// 添加海报路径
			row.AddCell().SetString(song.PosterPath)

			// 是否支持打分
			row.AddCell().SetInt64(song.PitchType)

			// 添加打分类型
			row.AddCell().SetString(song.Pitchs)

			// 添加码率
			row.AddCell().SetString(song.QualityLevel)

			// 添加热度值
			row.AddCell().SetInt64(song.HotNum)

			// 添加标签
			row.AddCell().SetString(song.TagIds)

			// 添加失败原因
			row.AddCell().SetString(reason)
		}
	}

	// 保存文件
	return file.Save(outputPath)
}

func writeFailedRecordsToExcelFromRow(failedSongs map[int]string, originalSheet *xlsx.Sheet, outputPath string) error {
	// 创建新Excel文件
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("失败记录")
	if err != nil {
		return err
	}

	// 复制原始表头并添加"失败原因"列
	headerRow := sheet.AddRow()
	if len(originalSheet.Rows) > 0 {
		for _, cell := range originalSheet.Rows[0].Cells {
			headerRow.AddCell().SetString(cell.String())
		}
	}
	headerRow.AddCell().SetString("失败原因")

	// 写入失败记录
	for idx, reason := range failedSongs {

		if idx >= 0 && idx < len(originalSheet.Rows) {
			idx++
			retRow := originalSheet.Rows[idx]

			// 添加新行
			row := sheet.AddRow()
			row.Cells = retRow.Cells

			// 添加失败原因
			row.AddCell().SetString(reason)
		}
	}

	// 保存文件
	return file.Save(outputPath)
}

// 生成唯一文件名
func generateUniqueName(originalName string) string {
	timestamp := time.Now().UnixNano()
	ext := path.Ext(originalName)
	return fmt.Sprintf("%d%s", timestamp, ext)
}
