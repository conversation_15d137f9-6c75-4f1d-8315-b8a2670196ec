package song

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"music/internal/middleware"
	"music/model/user"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/song"
)

type ListSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListSongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSongLogic {
	return &ListSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListSongLogic) ListSongs(req *types.ListSongReq) (*types.ListSongResp, error) {
	var songs []types.SongInfo
	var total int64
	var err error

	// 查询条件
	filter := &song.Song{
		Name:         req.Name,
		Singer:       req.<PERSON>,
		VendorId:     req.VendorId,
		Status:       req.Status,
		SongCode:     req.SongCode,
		TagIds:       req.TagIds,
		Type:         req.Type,
		HighPartType: req.HighPartType,
		Pitchs:       req.Pitchs,
		LyricType:    req.LyricType,
		VendorSongId: req.VendorSongId,
	}

	var closeTimeBegin, closeTimeEnd int64
	if req.CloseTimeBegin != "" {
		closeTimeBegin, _ = middleware.TimeUTCToUnix(fmt.Sprintf("%s %s", req.CloseTimeBegin, "00:00:00"))
	}
	if req.CloseTimeEnd != "" {
		closeTimeEnd, _ = middleware.TimeUTCToUnix(fmt.Sprintf("%s %s", req.CloseTimeEnd, "23:59:59"))
	}

	// 执行查询
	songList, count, err := l.svcCtx.SongModel.Find(filter, closeTimeBegin, closeTimeEnd, req.CreateTimeBegin, req.CreateTimeEnd, req.OrderByCreateTime, req.OrderByUpdateTime, req.OrderByCloseTime, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	total = int64(count)
	songs = l.convertSongList(songList)

	// 批量获取所有需要查询的版权方ID
	vendorIds := make(map[int64]struct{})
	for _, s := range songs {
		if s.VendorId > 0 {
			vendorIds[s.VendorId] = struct{}{}
		}
	}

	// 批量查询版权方信息
	vendorMap := make(map[int64]string)
	if len(vendorIds) > 0 {
		ids := make([]int64, 0, len(vendorIds))
		for id := range vendorIds {
			ids = append(ids, id)
		}

		cps, err := l.svcCtx.CpModel.FindByIds(ids)
		if err == nil {
			for _, cp := range cps {
				vendorMap[cp.Id] = cp.Name
			}
		}
	}

	// 填充版权方名称
	for i := range songs {
		if name, ok := vendorMap[songs[i].VendorId]; ok {
			songs[i].VendorName = name
		}
	}

	// 获取当前用户设置的展示列
	var columns []types.SongDisplayColumn

	// 尝试从上下文获取认证用户
	if authUser, ok := l.ctx.Value(types.AuthUserKey).(*types.AuthUser); ok && authUser != nil {
		// 获取用户的展示列设置
		setting, err := l.svcCtx.UserDisplaySettingModel.FindByUserUuidAndModule(authUser.UserUuid, "song")
		if err == nil {
			// 解析设置
			err = json.Unmarshal([]byte(setting.DisplayColumns), &columns)
			if err != nil {
				l.Error("解析用户展示列设置失败", logx.Field("error", err))
				columns = GetDefaultSongDisplayColumns()
			}
		} else if errors.Is(err, user.ErrUserDisplaySettingNotFound) {
			// 使用默认设置
			columns = GetDefaultSongDisplayColumns()
		} else {
			l.Error("获取用户展示列设置失败", logx.Field("error", err))
		}
	}

	return &types.ListSongResp{
		Total:   total,
		List:    songs,
		Columns: columns,
	}, nil
}

func (l *ListSongLogic) convertSongList(songList []*song.Song) []types.SongInfo {
	var songs []types.SongInfo
	for _, s := range songList {
		var closeTime string
		if s.CloseTime != 0 {
			closeTime = time.Unix(s.CloseTime, 0).Format(time.DateTime)
		}
		songs = append(songs, types.SongInfo{
			Id:                s.Id,
			ImportId:          s.ImportId,
			Type:              s.Type,
			VendorId:          s.VendorId,
			SongCode:          s.SongCode,
			Name:              s.Name,
			Singer:            s.Singer,
			VendorSongId:      s.VendorSongId,
			VendorReleaseTime: s.VendorReleaseTime,
			SongPath:          s.SongPath,
			DrmPath:           s.DrmPath,
			LicenseKey:        s.LicenseKey,
			PosterPath:        s.PosterPath,
			LyricPath:         s.LyricPath,
			LyricType:         s.LyricType,
			PitchType:         s.PitchType,
			Pitchs:            s.Pitchs,
			HighPart:          s.HighPart,
			HighPartType:      s.HighPartType,
			QualityLevel:      s.QualityLevel,
			TagIds:            s.TagIds,
			Duration:          s.Duration,
			CloseTime:         closeTime,
			HotNum:            s.HotNum,
			Status:            s.Status,
			CreateTime:        s.CreateTime.Format(time.DateTime),
			UpdateTime:        s.UpdateTime.Format(time.DateTime),
		})
	}
	return songs
}
