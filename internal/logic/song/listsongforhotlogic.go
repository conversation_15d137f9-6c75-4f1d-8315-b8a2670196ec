package song

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/song"
)

type ListSongForHotLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListSongForHotLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSongForHotLogic {
	return &ListSongForHotLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListSongForHotLogic) ListSongsForHot(req *types.ListSongForHotReq) (*types.ListSongForHotResp, error) {
	var songs []types.SongInfoForHot
	var total int64
	var err error

	// 查询条件
	filter := &song.Song{
		Name:   req.Name,
		Status: req.Status,
	}

	//  获取 当前 榜单里面的歌曲
	hotSongList, err := l.svcCtx.SongHotModel.FindAllHotSongsByType(req.HotType)
	songCodes := make([]int64, 0)
	for _, hotSong := range hotSongList {
		songCodes = append(songCodes, hotSong.SongCode)
	}

	// 执行查询
	songList, count, err := l.svcCtx.SongModel.FindForHot(filter, songCodes, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	total = int64(count)
	songs = l.convertSongList(songList)

	return &types.ListSongForHotResp{
		Total: total,
		List:  songs,
	}, nil
}

func (l *ListSongForHotLogic) convertSongList(songList []*song.Song) []types.SongInfoForHot {
	var songs []types.SongInfoForHot
	for _, s := range songList {
		songs = append(songs, types.SongInfoForHot{
			Id:           s.Id,
			ImportId:     s.ImportId,
			Type:         s.Type,
			VendorId:     s.VendorId,
			SongCode:     s.SongCode,
			Name:         s.Name,
			Singer:       s.Singer,
			VendorSongId: s.VendorSongId,
			CloseTime:    s.CloseTime,
			Status:       s.Status,
		})
	}
	return songs
}
