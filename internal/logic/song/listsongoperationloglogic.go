package song

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListSongOperationLogLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListSongOperationLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListSongOperationLogLogic {
	return &ListSongOperationLogLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListSongOperationLogLogic) ListSongOperationLog(req *types.ListSongOperationLogReq) (*types.ListSongOperationLogResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 查询歌曲操作日志
	logs, count, err := l.svcCtx.OperationLogModel.FindByEntityId("song", req.SongId, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var list []types.OperationLogInfo
	for _, log := range logs {
		list = append(list, types.OperationLogInfo{
			Id:         log.Id,
			UserUuid:   log.UserUuid,
			Username:   log.Username,
			Module:     log.Module,
			Operation:  log.Operation,
			EntityId:   log.EntityId,
			EntityName: log.EntityName,
			OldValue:   log.OldValue,
			NewValue:   log.NewValue,
			Ip:         log.Ip,
			UserAgent:  log.UserAgent,
			CreateTime: log.CreateTime.Format(time.RFC3339), // 修复：将time.Time格式化为字符串
		})
	}

	return &types.ListSongOperationLogResp{
		Total: count,
		List:  list,
	}, nil
}
