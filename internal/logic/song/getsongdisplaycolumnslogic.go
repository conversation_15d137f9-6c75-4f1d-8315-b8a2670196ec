package song

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/user"
)

type GetSongDisplayColumnsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSongDisplayColumnsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSongDisplayColumnsLogic {
	return &GetSongDisplayColumnsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSongDisplayColumnsLogic) GetSongDisplayColumns(req *types.GetSongDisplayColumnsReq) (*types.GetSongDisplayColumnsResp, error) {
	// 获取当前用户UUID
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 获取默认的展示列设置
	defaultColumns := GetDefaultSongDisplayColumns()

	// 尝试获取用户自定义设置
	setting, err := l.svcCtx.UserDisplaySettingModel.FindByUserUuidAndModule(authUser.UserUuid, "song")
	if err != nil {
		if errors.Is(err, user.ErrUserDisplaySettingNotFound) {
			// 如果找不到用户设置，返回默认设置
			return &types.GetSongDisplayColumnsResp{
				Columns: defaultColumns,
			}, nil
		}
		return nil, err
	}

	// 解析用户设置
	var columns []types.SongDisplayColumn
	err = json.Unmarshal([]byte(setting.DisplayColumns), &columns)
	if err != nil {
		// 解析失败则返回默认设置
		l.Error("Failed to parse user display settings", logx.Field("error", err))
		return &types.GetSongDisplayColumnsResp{
			Columns: defaultColumns,
		}, nil
	}

	return &types.GetSongDisplayColumnsResp{
		Columns: columns,
	}, nil
}

// GetDefaultSongDisplayColumns 获取默认的歌曲展示列设置  默认 返回空数组
func GetDefaultSongDisplayColumns() []types.SongDisplayColumn {
	// 这里可以根据需要定义默认的展示列

	// 例如：[{"title":"歌曲名称","field":"name","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"歌曲编号","field":"songCode","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"歌曲类型","field":"type","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"版权方ID","field":"vendorId","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"版权方","field":"vendorName","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"版权方歌曲ID","field":"vendorSongId","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"发布日期","field":"vendorReleaseTime","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"歌曲文件","field":"songPath","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":"songPath_slot"}},{"title":"歌曲时长","field":"duration","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"歌曲DRM文件","field":"drmPath","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":"drmPath_slot"}},{"title":"DRM密钥","field":"licenseKey","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"歌词文件","field":"lyricPath","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":"lyricPath_slot"}},{"title":"歌词类型","field":"lyricType","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"副歌片段","field":"highPart","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":"highPart_slot"}},{"title":"副歌类型","field":"highPartType","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"海报","field":"posterPath","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":"posterPath_slot"}},{"title":"是否支持打分","field":"pitchType","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"打分类型","field":"pitchs","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"码率","field":"qualityLevel","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"热度值","field":"hotNum","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"标签","field":"tagIds","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":""}},{"title":"状态","field":"status","align":"left","width":100,"isShow":true,"sortable":false,"slots":{"default":"status_slot"}},{"title":"状态变更时间","field":"updateTime","align":"left","width":100,"isShow":true,"sortable":true,"slots":{"default":""}},{"title":"创建时间","field":"createTime","align":"left","width":100,"isShow":true,"sortable":true,"slots":{"default":""}},{"title":"最近更新时间","field":"updateTime","align":"left","width":100,"isShow":true,"sortable":true,"slots":{"default":""}}]
	return []types.SongDisplayColumn{
		{Title: "歌曲名称", Field: "name", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "歌曲编号", Field: "songCode", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "歌曲类型", Field: "type", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "type_slot"}},
		{Title: "歌手名称", Field: "singer", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "版权方ID", Field: "vendorId", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "版权方歌曲ID", Field: "vendorSongId", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "发布日期", Field: "vendorReleaseTime", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "歌曲文件", Field: "songPath", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "songPath_slot"}},
		{Title: "歌曲时长", Field: "duration", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "duration_slot"}},
		{Title: "歌曲DRM文件", Field: "drmPath", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "drmPath_slot"}},
		{Title: "DRM密钥", Field: "licenseKey", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "歌词文件", Field: "lyricPath", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "lyricPath_slot"}},
		{Title: "歌词类型", Field: "lyricType", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "lyricType_slot"}},
		{Title: "副歌片段", Field: "highPart", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "highPart_slot"}},
		{Title: "副歌类型", Field: "highPartType", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "highPartType_slot"}},
		{Title: "海报", Field: "posterPath", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "是否支持打分", Field: "pitchType", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "pitchType_slot"}},
		{Title: "打分类型", Field: "pitchs", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "码率", Field: "qualityLevel", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "热度值", Field: "hotNum", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "标签", Field: "tagIds", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: ""}},
		{Title: "状态", Field: "status", Align: "left", Width: 100, IsShow: true, Sortable: false, Slots: types.Slots{Default: "status_slot"}},
		{Title: "上架时间", Field: "createTime", Align: "left", Width: 100, IsShow: true, Sortable: true, Slots: types.Slots{Default: ""}},
		{Title: "下架时间", Field: "closeTime", Align: "left", Width: 100, IsShow: true, Sortable: true, Slots: types.Slots{Default: ""}},
		{Title: "最近更新时间", Field: "updateTime", Align: "left", Width: 100, IsShow: true, Sortable: true, Slots: types.Slots{Default: ""}},
	}
}
