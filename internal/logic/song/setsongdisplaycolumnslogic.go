package song

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/user"
)

type SetSongDisplayColumnsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetSongDisplayColumnsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetSongDisplayColumnsLogic {
	return &SetSongDisplayColumnsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetSongDisplayColumnsLogic) SetSongDisplayColumns(req *types.SetSongDisplayColumnsReq) (*types.SetSongDisplayColumnsResp, error) {
	// 获取当前用户UUID
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 检查请求参数
	if req.Columns == nil {
		return &types.SetSongDisplayColumnsResp{
			Success: false,
			Message: "列设置不能为空",
		}, nil
	}

	// 将列设置转换为JSON字符串
	columnsJSON, err := json.Marshal(req.Columns)
	if err != nil {
		return nil, err
	}

	// 查找是否已存在用户的歌曲展示列设置
	setting, err := l.svcCtx.UserDisplaySettingModel.FindByUserUuidAndModule(authUser.UserUuid, "song")
	if err != nil {
		if errors.Is(err, user.ErrUserDisplaySettingNotFound) {
			// 不存在则创建新的设置
			newSetting := &user.UserDisplaySetting{
				UserUuid:       authUser.UserUuid,
				Module:         "song",
				DisplayColumns: string(columnsJSON),
			}
			_, err = l.svcCtx.UserDisplaySettingModel.Insert(newSetting)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	} else {
		// 存在则更新设置
		setting.DisplayColumns = string(columnsJSON)
		err = l.svcCtx.UserDisplaySettingModel.Update(setting)
		if err != nil {
			return nil, err
		}
	}

	return &types.SetSongDisplayColumnsResp{
		Success: true,
		Message: "设置成功",
	}, nil
}
