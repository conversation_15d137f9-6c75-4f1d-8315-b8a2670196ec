package song

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"strconv"
	"time"

	"music/internal/svc"
	"music/internal/types"
	"music/model/log"
	"music/model/song"
)

type AddSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddSongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddSongLogic {
	return &AddSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddSongLogic) AddSong(req *types.CreateSongReq) (*types.CommonSongResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 管理员权限验证
	if authUser.Role < 1 { // 假设角色1以上为管理员
		return &types.CommonSongResp{
			Success: false,
			Message: "权限不足，需要管理员权限",
		}, fmt.Errorf("权限不足，需要管理员权限")
	}

	if req.Name == "" {
		return &types.CommonSongResp{
			Success: false,
			Message: "歌曲名称不能为空",
		}, fmt.Errorf("歌曲名称不能为空")
	}

	// 查找歌曲
	/*	oldSong, err := l.svcCtx.SongModel.FindByName(req.Name)
		if err != nil {
			if !errors.Is(err, song.ErrSongNotFound) {
				l.Logger.Error("查询歌曲失败", logx.Field("error", err))
				return nil, fmt.Errorf("查询歌曲失败: %+v", err)
			}
		}

		if oldSong != nil {
			return &types.CommonSongResp{
				Success: false,
				Message: "歌曲名称已存在",
			}, fmt.Errorf("歌曲名称已存在: %+v", req.Name)
		}*/

	req.ImportId, _ = strconv.ParseInt(time.Now().Format("2006010215"), 10, 64)

	// 当前时间时间戳 去除 首位1 末尾 加 0
	timestamp := time.Now().UnixMicro()
	// 转为字符串
	timestampStr := strconv.FormatInt(timestamp, 10)

	// 如果首位是 1，则去除
	if len(timestampStr) > 0 && timestampStr[0] == '1' {
		timestampStr = timestampStr[1:]
	}

	// 末尾添加 0
	timestampStr = timestampStr + "0"
	// 如果需要转回整数
	code, _ := strconv.ParseInt(timestampStr, 10, 64)

	req.SongCode = code

	// 检查歌曲编号是否已被其他歌曲使用（如果歌曲编号发生变化）
	existingSong, err := l.svcCtx.SongModel.FindOneBySongCode(req.SongCode)
	if err == nil && existingSong.Id != 0 {
		return &types.CommonSongResp{
			Success: false,
			Message: fmt.Sprintf("歌曲编号 %d 已被其他歌曲使用", req.SongCode),
		}, fmt.Errorf("歌曲编号 %d 已被其他歌曲使用", req.SongCode)
	}

	// 检查版权方是否存在
	if req.VendorId > 0 {
		_, err = l.svcCtx.CpModel.FindOne(req.VendorId)
		if err != nil {
			l.Logger.Error("查询版权方失败", logx.Field("error", err))
			return &types.CommonSongResp{
				Success: false,
				Message: "指定的版权方不存在",
			}, fmt.Errorf("指定的版权方不存在")
		}

		// 检查版权方是否有效  不做检验
		/*if vendor.Status != 1 {
			return &types.CommonSongResp{
				Success: false,
				Message: "指定的版权方已被禁用",
			}, fmt.Errorf("指定的版权方已被禁用")
		}*/
	}

	if req.Type == 0 {
		req.Type = 1
	}

	if req.PitchType != 1 {
		req.PitchType = 2
	}

	if req.HighPartType == 0 {
		req.HighPartType = 1
	}

	req.Status = types.Up

	// 构建更新数据
	songData := &song.Song{
		ImportId:          req.ImportId,
		Type:              req.Type,
		VendorId:          req.VendorId,
		SongCode:          req.SongCode,
		Name:              req.Name,
		Singer:            req.Singer,
		VendorSongId:      req.VendorSongId,
		VendorReleaseTime: req.VendorReleaseTime,
		SongPath:          req.SongPath,
		DrmPath:           req.DrmPath,
		LicenseKey:        req.LicenseKey,
		PosterPath:        req.PosterPath,
		LyricPath:         req.LyricPath,
		LyricType:         req.LyricType,
		PitchType:         req.PitchType,
		Pitchs:            req.Pitchs,
		HighPart:          req.HighPart,
		HighPartType:      req.HighPartType,
		QualityLevel:      req.QualityLevel,
		TagIds:            req.TagIds,
		Duration:          req.Duration,
		HotNum:            req.HotNum,
		Status:            req.Status,
	}

	// 执行更新
	result, err := l.svcCtx.SongModel.Insert(songData)
	if err != nil {
		l.Logger.Error("创建歌曲失败", logx.Field("error", err))
		return nil, errors.New(types.ErrCreateSong)
	}

	songId, err := result.LastInsertId()
	if err != nil {
		return nil, errors.New(types.ErrCreateSong)
	}

	// 记录操作日志
	newValue, _ := json.Marshal(songData)

	// 获取IP和用户代理
	ip := "未知"
	userAgent := "未知"
	if ipVal := l.ctx.Value("remoteAddr"); ipVal != nil {
		ip = ipVal.(string)
	}
	if uaVal := l.ctx.Value("userAgent"); uaVal != nil {
		userAgent = uaVal.(string)
	}

	// 创建描述性操作日志记录
	//changeDesc := "更新了歌曲信息"
	//if len(changedFields) > 0 {
	//	changeDesc = fmt.Sprintf("修改了歌曲的以下字段: %s", strings.Join(changedFields, ", "))
	//}

	// 创建操作日志
	operationLog := &log.OperationLog{
		UserUuid:   authUser.UserUuid,
		Username:   authUser.Username,
		Module:     "song",
		Operation:  "add",
		EntityId:   songData.Id,
		EntityName: songData.Name,
		NewValue:   string(newValue),
		Ip:         ip,
		UserAgent:  userAgent,
	}

	_, err = l.svcCtx.OperationLogModel.Insert(operationLog)
	if err != nil {
		l.Logger.Error("记录操作日志失败", logx.Field("error", err))
	}

	// 记录详细日志
	l.Logger.Info("歌曲信息创建成功",
		logx.Field("id", songId),
		logx.Field("name", req.Name),
		logx.Field("user", authUser.Username),
		logx.Field("newValue", string(newValue)))

	return &types.CommonSongResp{
		Id:      songId,
		Success: true,
		Message: "创建成功",
	}, nil
}
