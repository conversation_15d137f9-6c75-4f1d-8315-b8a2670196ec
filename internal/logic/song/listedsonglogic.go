package song

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/log"
)

type ListedSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListedSongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListedSongLogic {
	return &ListedSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListedSongLogic) ListedSong(req *types.ListedSongReq) (*types.ListedSongResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}
	logs, err := l.svcCtx.SongModel.ListedSongs(req.<PERSON>, req.Status)
	if err != nil {
		l.Logger.Error("更新歌曲状态失败", logx.Field("error", err))
		return nil, errors.New(types.ErrUpdateSongStatus)
	}

	go func(req []*log.OperationLog) {
		for _, v := range logs {
			v.UserUuid = authUser.UserUuid
			v.Username = authUser.Username
			_, err = l.svcCtx.OperationLogModel.Insert(v)
			if err != nil {
				l.Logger.Error("记录操作日志失败", logx.Field("error", err))
			}
		}
	}(logs)

	return &types.ListedSongResp{
		Success: true,
		Message: "歌曲状态更新成功",
	}, nil
}
