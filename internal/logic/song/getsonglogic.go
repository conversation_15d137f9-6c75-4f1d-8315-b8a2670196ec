package song

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	"music/model/song"
)

type GetSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSongLogic {
	return &GetSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSongLogic) GetSong(req *types.GetSongReq) (*types.GetSongResp, error) {
	var s *song.Song
	var err error

	if req.Id > 0 {
		s, err = l.svcCtx.SongModel.FindOne(req.Id)
	} else if req.SongCode > 0 {
		s, err = l.svcCtx.SongModel.FindOneBySongCode(req.SongCode)
	} else {
		return nil, errors.New("请提供歌曲ID或编码")
	}

	if err != nil {
		return nil, err
	}

	var closeTime string
	if s.CloseTime != 0 {
		closeTime = time.Unix(s.CloseTime, 0).Format(time.RFC3339)
	} else {
		closeTime = ""
	}

	// 封装返回数据
	songInfo := types.SongInfo{
		Id:                s.Id,
		ImportId:          s.ImportId,
		Type:              s.Type,
		VendorId:          s.VendorId,
		SongCode:          s.SongCode,
		Name:              s.Name,
		Singer:            s.Singer,
		VendorSongId:      s.VendorSongId,
		VendorReleaseTime: s.VendorReleaseTime,
		SongPath:          s.SongPath,
		DrmPath:           s.DrmPath,
		LicenseKey:        s.LicenseKey,
		PosterPath:        s.PosterPath,
		LyricPath:         s.LyricPath,
		LyricType:         s.LyricType,
		PitchType:         s.PitchType,
		Pitchs:            s.Pitchs,
		HighPart:          s.HighPart,
		HighPartType:      s.HighPartType,
		QualityLevel:      s.QualityLevel,
		TagIds:            s.TagIds,
		Duration:          s.Duration,
		CloseTime:         closeTime,
		HotNum:            s.HotNum,
		Status:            s.Status,
		CreateTime:        s.CreateTime.Format(time.RFC3339),
	}

	// 获取版权方名称
	if s.VendorId > 0 {
		cp, err := l.svcCtx.CpModel.FindOne(s.VendorId)
		if err == nil {
			songInfo.VendorName = cp.Name
		}
	}

	return &types.GetSongResp{
		Song: songInfo,
	}, nil
}
