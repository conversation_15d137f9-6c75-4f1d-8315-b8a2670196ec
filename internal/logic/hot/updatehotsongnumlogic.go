package hot

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/hot"
)

type UpdateHotSongNumLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateHotSongNumLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateHotSongNumLogic {
	return &UpdateHotSongNumLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *UpdateHotSongNumLogic) UpdateHotSongNum(req *types.HotSongInfo) (*types.AddAndUpdateHotSongResp, error) {
	// 查询 榜单是否存在
	oldSongHot, songHotErr := a.svcCtx.SongHotModel.FindOneByNumWithType(req.HotType, req.Num)
	if songHotErr != nil {
		return nil, hot.ErrAddSongHot
	}
	if oldSongHot != nil {
		if oldSongHot.Id != req.Id {
			return nil, hot.SongHotNumIsExist
		}
		if oldSongHot.Id == req.Id {
			return &types.AddAndUpdateHotSongResp{
				Id:      req.Id,
				Message: "排序号未发生变化",
			}, nil
		}
	}

	// 保存数据
	songHot := &hot.SongHot{
		Id:      req.Id,
		HotType: req.HotType,
		Num:     req.Num,
	}

	err := a.svcCtx.SongHotModel.UpdateNum(songHot)
	if err != nil {
		return nil, err
	}

	return &types.AddAndUpdateHotSongResp{
		Id:      req.Id,
		Message: "更新成功",
	}, nil

}
