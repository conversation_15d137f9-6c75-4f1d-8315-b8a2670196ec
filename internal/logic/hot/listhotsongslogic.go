package hot

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListHotSongsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListHotSongsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListHotSongsLogic {
	return &ListHotSongsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListHotSongsLogic) ListHotSongs(req *types.ListHotSongsReq) (*types.ListHotSongsResp, error) {
	// 检查榜单类型是否存在
	_, err := l.svcCtx.SongHotTypeModel.FindOneByType(req.HotType)
	if err != nil {
		return nil, errors.New(types.ErrBillboardNotFound)
	}

	// 查询热歌列表
	hotSongs, total, err := l.svcCtx.SongHotModel.Find(req.HotType, req.Name, req.OrderByCreateTime, req.OrderByUpdateTime, req.SongCode, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	var result []types.ListHotSongDetail
	for _, hotSong := range hotSongs {
		result = append(result, types.ListHotSongDetail{
			Id:         hotSong.Id,
			HotType:    hotSong.HotType,
			SongCode:   hotSong.SongCode,
			Vid:        hotSong.Vid,
			Num:        hotSong.Num,
			SongName:   hotSong.SongName,
			SongSinger: hotSong.SongSinger,
			UpdateTime: hotSong.UpdateTime.Format(time.DateTime),
			CreateTime: hotSong.CreateTime.Format(time.DateTime),
		})
	}

	return &types.ListHotSongsResp{
		Total: total,
		List:  result,
	}, nil
}
