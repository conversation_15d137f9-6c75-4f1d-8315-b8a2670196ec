package hot

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
)

type DelHotSongsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDelHotSongsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DelHotSongsLogic {
	return &DelHotSongsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *DelHotSongsLogic) DelHotSongs(req *types.DelHotSong) error {

	err := a.svcCtx.SongHotModel.Delete(req.Id)
	if err != nil {
		return err
	}

	return nil
}
