package hot

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/hot"
)

type AddHotSongsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddHotSongsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddHotSongsLogic {
	return &AddHotSongsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *AddHotSongsLogic) AddHotSongs(req *types.AddHotSongInfo) (*types.AddAndUpdateHotSongResp, error) {
	if req.HotType == nil || len(req.HotType) == 0 {
		return nil, errors.New("请选择榜单类型")
	}
	for _, hotType := range req.HotType {
		// 查询 歌曲 是否存在 选择的 歌单中
		countBySongCodeWithHotType, err := a.svcCtx.SongHotModel.CountBySongCodeWithHotType(req.SongCode, hotType)
		if err != nil {
			return nil, err
		}

		if countBySongCodeWithHotType > 0 {
			return nil, hot.SongHotHad
		}
		// 校验 num
		if hotType > 1 {
			songHot, songHotErr := a.svcCtx.SongHotModel.FindOneByNumWithType(hotType, req.Num)
			if songHotErr != nil {
				return nil, hot.ErrAddSongHot
			}
			if songHot != nil && songHot.Id > 0 {
				return nil, hot.SongHotNumIsExist
			}
		}

		// 保存数据
		songHot := &hot.SongHot{
			HotType:  hotType,
			SongCode: req.SongCode,
			Vid:      req.Vid,
			Num:      req.Num,
		}

		result, err := a.svcCtx.SongHotModel.Insert(songHot)
		if err != nil {
			return nil, hot.ErrAddSongHot
		}

		_, err = result.LastInsertId()
		if err != nil {
			return nil, hot.ErrAddSongHot
		}
	}

	return &types.AddAndUpdateHotSongResp{
		Id:      0,
		Message: "添加成功",
	}, nil
}
