package hot

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"music/model/hot"

	"music/internal/svc"
	"music/internal/types"
)

type CheckHotSongsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckHotSongsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckHotSongsLogic {
	return &CheckHotSongsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckHotSongsLogic) CheckHotSongNum(req *types.CheckHotSongNum) (*types.CheckHotSongNumResp, error) {
	for _, hotType := range req.HotType {
		if req.Num > 1 {
			// 校验 num
			if hotType > 1 {
				songHot, songHotErr := l.svcCtx.SongHotModel.FindOneByNumWithType(hotType, req.Num)
				if songHotErr != nil {
					return nil, hot.ErrAddSongHot
				}
				if songHot != nil && songHot.Id > 0 {
					return nil, hot.SongHotNumIsExist
				}
			}
		}
	}

	return &types.CheckHotSongNumResp{
		Success: true,
		Message: "校验通过",
	}, nil
}
