package hot

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListHotSongsNumLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListHotSongsNumLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListHotSongsNumLogic {
	return &ListHotSongsNumLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListHotSongsNumLogic) ListHotSongsNum(req *types.ListHotSongsNumReq) (*types.ListHotSongsNumResp, error) {
	// 检查榜单类型是否存在
	_, err := l.svcCtx.SongHotTypeModel.FindOneByType(req.HotType)
	if err != nil {
		return nil, errors.New(types.ErrBillboardNotFound)
	}

	// 查询热歌列表
	hotSongs, total, err := l.svcCtx.SongHotModel.FindNum(req.HotType)
	if err != nil {
		return nil, err
	}

	var result []types.ListHotSongsNum
	for _, hotSong := range hotSongs {
		result = append(result, types.ListHotSongsNum{
			SongCode: hotSong.SongCode,
			Num:      hotSong.Num,
			SongName: hotSong.SongName,
		})
	}

	return &types.ListHotSongsNumResp{
		Total: total,
		List:  result,
	}, nil
}
