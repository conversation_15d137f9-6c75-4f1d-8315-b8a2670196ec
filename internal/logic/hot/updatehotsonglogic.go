package hot

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/hot"
)

type UpdateHotSongLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateHotSongLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateHotSongLogic {
	return &UpdateHotSongLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *UpdateHotSongLogic) UpdateHotSong(req *types.HotSongInfo) (*types.AddAndUpdateHotSongResp, error) {
	// 查询 榜单是否存在
	oldSongHot, songHotErr := a.svcCtx.SongHotModel.FindOneByNumWithType(req.HotType, req.Num)
	if songHotErr != nil {
		return nil, hot.ErrAddSongHot
	}
	if oldSongHot != nil {
		if oldSongHot.Id != req.Id {
			return nil, hot.SongHotNumIsExist
		}
	}

	// 查询 歌曲是否存在
	oldSongHot, songHotErr = a.svcCtx.SongHotModel.FindOneBySongCodeWithType(req.SongCode, req.HotType)
	if songHotErr != nil {
		return nil, errors.New(types.ErrQuerySongInfo)
	}

	if oldSongHot != nil {
		if oldSongHot.Id != req.Id {
			return nil, hot.SongHotHad
		}
	}

	// 保存数据
	songHot := &hot.SongHot{
		Id:       req.Id,
		SongCode: req.SongCode,
		Vid:      req.Vid,
		Num:      req.Num,
	}

	err := a.svcCtx.SongHotModel.Update(songHot)
	if err != nil {
		return nil, err
	}

	return &types.AddAndUpdateHotSongResp{
		Id:      req.Id,
		Message: "更新成功",
	}, nil

}
