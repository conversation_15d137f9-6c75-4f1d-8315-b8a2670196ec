package user

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/user"
)

type UpdateUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateUserInfoLogic {
	return &UpdateUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateUserInfoLogic) UpdateUserInfo(req *types.UpdateUserInfoReq) (*types.UpdateUserInfoResp, error) {

	userInfo := new(user.User)
	var err error

	if req.UserId != 0 {
		userInfo, err = l.svcCtx.UserModel.FindOne(req.UserId)
		if err != nil {
			return nil, errors.New(types.ErrUserNotFound)
		}
	} else if req.UserUuid != "" {
		userInfo, err = l.svcCtx.UserModel.FindOneByUuid(req.UserUuid)
		if err != nil {
			return nil, errors.New(types.ErrUserNotFound)
		}
	} else {
		return nil, errors.New(types.ErrUserNotFound)
	}

	if userInfo.Id == 0 {
		return nil, errors.New(types.ErrUserNotFound)
	}

	userInfo.Username = req.Username
	userInfo.Phone = req.Phone
	userInfo.Role = req.Role
	userInfo.Remark = req.Remark

	err = l.svcCtx.UserModel.Update(userInfo)
	if err != nil {
		return nil, errors.New(types.ErrUpdateUserInfo)
	}

	return &types.UpdateUserInfoResp{
		Success: true,
		Message: "更新用户信息成功",
	}, nil
}
