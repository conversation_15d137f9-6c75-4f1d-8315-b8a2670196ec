package user

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/crypto/bcrypt"
	"music/internal/svc"
	"music/internal/types"
)

type UpdateUserPwLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateUserPwLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateUserPwLogic {
	return &UpdateUserPwLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateUserPwLogic) UpdatePw(req *types.ChangePasswordReq) (*types.ChangePasswordResp, error) {

	if req.OldPassword == req.NewPassword {
		return nil, errors.New(types.ErrOldPasswordSame)
	}

	user, err := l.svcCtx.UserModel.FindOne(req.UserId)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return nil, errors.New(types.ErrUserNotFound)
	}

	/*// 对密码进行哈希处理
	oldPassword, err := bcrypt.GenerateFromPassword([]byte(req.OldPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}*/

	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword))
	if err != nil {
		logx.Errorf("密码比对失败 %+v   %+v", err, req.OldPassword)
		return nil, errors.New(types.ErrOldPasswordIncorrect)
	}

	// 对新密码进行哈希处理
	newPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}
	// 更新用户密码
	user.Password = string(newPassword)
	err = l.svcCtx.UserModel.UpdatePw(req.UserId, string(newPassword))
	if err != nil {
		return nil, errors.New(types.ErrUpdatePw)
	}

	return &types.ChangePasswordResp{
		Success: true,
		Message: "更新密码成功",
	}, nil
}
