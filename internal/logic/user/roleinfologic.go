package user

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type RoleInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRoleInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RoleInfoLogic {
	return &RoleInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RoleInfoLogic) RoleInfo() (*types.RoleInfoResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(authUser.UserId)
	if err != nil {
		return nil, err
	}

	role := make([]string, 0)

	if user.Role != 0 {
		role = append(role, types.MapRole[user.Role])

	}

	return &types.RoleInfoResp{
		role,
	}, nil
}
