package user

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type DelUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDelUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DelUserLogic {
	return &DelUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DelUserLogic) DelUser(req *types.UserInfoReq) (*types.DelUserInfoResp, error) {

	// 删除用户信息
	err := l.svcCtx.UserModel.Delete(req.UserId)
	if err != nil {
		return nil, err
	}

	return &types.DelUserInfoResp{
		Success: true,
		Message: "用户删除成功",
	}, nil
}
