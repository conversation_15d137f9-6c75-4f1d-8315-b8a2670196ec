package user

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListUserLogic {
	return &ListUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListUserLogic) ListUser(req *types.ListUserReq) (*types.ListUserResp, error) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	// 只有管理员可以查看用户列表
	if authUser.Role < 1 {
		return nil, errors.New("权限不足")
	}

	// 查询用户列表
	users, count, err := l.svcCtx.UserModel.FindByConditions(req.Username, req.Phone, req.Role, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 转换结果
	var list []types.UserInfoItem
	for _, user := range users {
		// 查询最后登录记录
		var lastLoginTime int64
		latestLoginLog, err := l.svcCtx.UserLoginLogModel.FindLatestByUserId(user.Id)
		if err == nil && latestLoginLog != nil {
			lastLoginTime = latestLoginLog.CreateTime.Unix()
		}

		list = append(list, types.UserInfoItem{
			UserId:     user.Id,
			UserUuid:   user.Uuid,
			Username:   user.Username,
			Email:      user.Email,
			Phone:      user.Phone,
			Role:       user.Role,
			Status:     user.Status,
			LastLogin:  lastLoginTime,
			CreateTime: user.CreateTime.Format(time.DateTime),
			UpdateTime: user.UpdateTime.Format(time.DateTime),
			Remark:     user.Remark,
		})
	}

	return &types.ListUserResp{
		Total: count,
		List:  list,
	}, nil
}
