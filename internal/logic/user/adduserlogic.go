package user

import (
	"context"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/crypto/bcrypt"

	"music/internal/svc"
	"music/internal/types"
	"music/model/user"
)

type AddUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddUserLogic {
	return &AddUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddUserLogic) AddUser(req *types.RegisterReq) (*types.RegisterResp, error) {
	// 检查用户名是否已存在
	/*_, err := l.svcCtx.UserModel.FindOneByUsername(req.Username)
	if err == nil {
		return nil, user.ErrUserAlreadyExists
	}*/

	// 检查手机号是否已存在
	_, err := l.svcCtx.UserModel.FindOneByPhone(req.Phone)
	if err == nil {
		return nil, user.ErrPhoneAlreadyExists
	}

	if req.Password == "" {
		req.Password = user.DefaultPw
	}

	// 对密码进行哈希处理
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建新用户
	newUser := &user.User{
		Uuid:     uuid.New().String(), // 生成UUID
		Username: req.Username,
		Password: string(hashedPassword),
		Email:    req.Email,
		Phone:    req.Phone,
		Role:     req.Role, // 设置为普通用户 1普通用户，2管理员，3超级管理员
		Remark:   req.Remark,
		Status:   1, // 激活状态
	}

	result, err := l.svcCtx.UserModel.Insert(newUser)
	if err != nil {
		return nil, err
	}

	userId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &types.RegisterResp{
		UserId:   userId,
		UserUuid: newUser.Uuid,
		Username: newUser.Username,
		Success:  true,
		Message:  "注册成功",
	}, nil
}
