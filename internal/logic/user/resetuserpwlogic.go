package user

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/crypto/bcrypt"

	"music/internal/svc"
	"music/internal/types"
	userModel "music/model/user"
)

type ResetUserPwLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewResetUserPwLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ResetUserPwLogic {
	return &ResetUserPwLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ResetUserPwLogic) ResetPw(req *types.ResetPasswordReq) (*types.ResetPasswordResp, error) {

	user, err := l.svcCtx.UserModel.FindOne(req.UserId)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return nil, errors.New(types.ErrUserNotFound)
	}

	// 对新密码进行哈希处理
	newPassword, err := bcrypt.GenerateFromPassword([]byte(userModel.DefaultPw), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}
	// 更新用户密码
	user.Password = string(newPassword)
	err = l.svcCtx.UserModel.UpdatePw(req.UserId, string(newPassword))
	if err != nil {
		return nil, errors.New(types.ErrUpdatePw)
	}

	return &types.ResetPasswordResp{
		Success: true,
		Message: "重置密码成功",
	}, nil
}
