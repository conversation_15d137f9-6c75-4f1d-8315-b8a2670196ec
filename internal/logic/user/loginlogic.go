package user

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
	userModel "music/model/user"
)

type LoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginLogic) Login(req *types.LoginReq) (*types.LoginResp, error) {
	// 查找用户
	user, err := l.svcCtx.UserModel.FindOneByPhone(req.Phone)
	if err != nil {
		if errors.Is(err, userModel.ErrUserNotFound) {
			return nil, userModel.ErrUserPasswordInvalid
		}
		return nil, err
	}

	// 验证用户状态
	if user.Status != 1 {
		return nil, userModel.ErrUserStatusInvalid
	}

	// 验证密码
	if !user.VerifyPassword(req.Password) {
		return nil, userModel.ErrUserPasswordInvalid
	}

	// 生成JWT令牌
	tokenString, refreshAfter, err := l.generateToken(user)
	if err != nil {
		return nil, err
	}

	// 获取客户端IP
	ip := l.ctx.Value("remoteAddr").(string)
	userAgent := l.ctx.Value("userAgent").(string)

	// 记录登录日志
	_, err = l.svcCtx.UserLoginLogModel.Insert(&userModel.UserLoginLog{
		UserId:    user.Id,
		UserUuid:  user.Uuid,
		Ip:        ip,
		UserAgent: userAgent,
		Opt:       "login",
	})
	if err != nil {
		l.Logger.Error("记录登录日志失败", logx.Field("err", err))
	}

	return &types.LoginResp{
		UserId:       user.Id,
		UserUuid:     user.Uuid, // 返回UUID
		Username:     user.Username,
		AccessToken:  tokenString,
		RefreshAfter: refreshAfter,
		Role:         types.MapRole[user.Role],
	}, nil
}

func (l *LoginLogic) generateToken(user *userModel.User) (string, int64, error) {
	now := time.Now().Unix()
	accessExpire := l.svcCtx.Config.Auth.AccessExpire

	// 创建标准claims
	claims := jwt.MapClaims{
		"userId":   user.Id,
		"userUuid": user.Uuid,
		"username": user.Username,
		"role":     user.Role,
		"iat":      now,                 // 发行时间
		"exp":      now + accessExpire,  // 过期时间
		"jti":      uuid.New().String(), // 唯一标识符
	}

	// 添加指纹信息
	claims["fingerprint"] = l.generateFingerprint(user)

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(l.svcCtx.Config.Auth.AccessSecret))
	if err != nil {
		return "", 0, userModel.ErrGenerateTokenFailed
	}

	return tokenString, now + accessExpire, nil
}

// generateFingerprint 生成token指纹
func (l *LoginLogic) generateFingerprint(user *userModel.User) string {
	// 组合用户信息和时间戳
	data := fmt.Sprintf("%d:%s:%s:%d:%d",
		user.Id,
		user.Uuid,
		user.Username,
		user.Role,
		time.Now().Unix(),
	)

	// 使用SHA256生成指纹
	hash := sha256.Sum256([]byte(data))
	return base64.URLEncoding.EncodeToString(hash[:])
}
