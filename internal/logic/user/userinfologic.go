package user

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type UserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserInfoLogic {
	return &UserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UserInfoLogic) UserInfo(req *types.UserInfoReq) (*types.UserInfoResp, error) {
	// 从 JWT 中获取用户ID
	userId, ok := l.ctx.Value("userId").(int64)
	if !ok {
		return nil, errors.New("获取用户信息失败")
	}

	// 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(userId)
	if err != nil {
		return nil, err
	}

	return &types.UserInfoResp{
		UserId:   user.Id,
		Username: user.Username,
		Phone:    user.Phone,
		Role:     user.Role,
	}, nil
}
