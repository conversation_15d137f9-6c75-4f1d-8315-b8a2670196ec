package user

import (
	"context"
	userModel "music/model/user"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type LogoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLogoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LogoutLogic {
	return &LogoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LogoutLogic) Logout(req *types.LogoutReq) (*types.LogoutResp, error) {

	token := l.ctx.Value(types.Authorization)
	if token != nil {
		// Add token to blacklist in Redis with an expiry time matching the token's TTL
		_, err := l.svcCtx.Redis.SaddCtx(
			l.ctx,
			"token_blacklist:"+token.(string),
			"1",
			l.svcCtx.Config.Auth.AccessExpire, // Assuming this is in seconds
		)
		if err != nil {
			l.Logger.Errorf("Failed to blacklist token: %v", err)
			return nil, err
		}
		l.Logger.Info("Token added to blacklist")

		// 记录登出日志
		user := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)

		_, err = l.svcCtx.UserLoginLogModel.Insert(&userModel.UserLoginLog{
			UserId:    user.UserId,
			UserUuid:  user.UserUuid,
			Ip:        "",
			UserAgent: "",
			Opt:       "logout",
		})
	}

	// Additional cleanup operations could go here
	// For example, updating user's last logout time in database

	return &types.LogoutResp{
		Success: true,
		Message: "退出成功",
	}, nil
}
