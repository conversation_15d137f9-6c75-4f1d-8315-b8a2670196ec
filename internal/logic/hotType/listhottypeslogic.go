package hotType

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"music/internal/svc"
	"music/internal/types"
)

type ListHotTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListHotTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListHotTypesLogic {
	return &ListHotTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListHotTypesLogic) ListHotTypes(req *types.ListHotTypesReq) (*types.ListHotTypesResp, error) {
	// 查询所有热门榜单类型
	hotTypes, count, err := l.svcCtx.SongHotTypeModel.Find(req.HotType, req.Name, req.OrderByCreateTime, req.OrderByUpdateTime, req.Status, req.Page, req.PageSize)
	if err != nil {
		l.Logger.Errorf("ListHotTypes error: %v", err)
		return nil, errors.New(types.ErrQueryHotType)
	}

	var result []types.HotTypeInfo
	for _, hotType := range hotTypes {
		result = append(result, types.HotTypeInfo{
			Id:         hotType.Id,
			HotType:    hotType.HotType,
			Name:       hotType.Name,
			Status:     hotType.Status,
			Num:        hotType.Num,
			CreateTime: hotType.CreateTime.Format(time.DateTime),
			UpdateTime: hotType.UpdateTime.Format(time.DateTime),
		})
	}

	return &types.ListHotTypesResp{
		Total: count,
		List:  result,
	}, nil
}
