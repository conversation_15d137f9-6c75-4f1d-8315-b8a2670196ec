package hotType

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
)

type DelHotTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDelHotTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DelHotTypesLogic {
	return &DelHotTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *DelHotTypesLogic) DelHotTypes(req *types.HotTypeInfo) error {
	err := a.svcCtx.SongHotTypeModel.Delete(req.Id)
	if err != nil {
		return err
	}

	return nil
}
