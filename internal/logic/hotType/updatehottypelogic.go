package hotType

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/hot"
)

type UpdateHotTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateHotTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateHotTypesLogic {
	return &UpdateHotTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *UpdateHotTypesLogic) UpdateHotTypes(req *types.UpdateHotType) (*types.AddAndUpdateHotTypeResp, error) {
	// 查询 榜单是否存在
	countByNameWithHotType, err := a.svcCtx.SongHotTypeModel.CountByNameWithHotType(req.Name, req.HotType)
	if err != nil {
		return nil, err
	}

	if countByNameWithHotType > 0 {
		return nil, hot.SongHotTypeHad
	}

	if req.HotType > 255 || req.HotType < 0 {
		return nil, errors.New(types.ErrHotTypeIsInvalid)
	}

	// 保存数据
	songHotType := &hot.SongHotType{
		Id:      req.Id,
		Name:    req.Name,
		HotType: req.HotType,
	}

	err = a.svcCtx.SongHotTypeModel.Update(songHotType)
	if err != nil {
		return nil, err
	}

	return &types.AddAndUpdateHotTypeResp{
		Id:      req.Id,
		Success: true,
		Message: "更新成功",
	}, nil

}
