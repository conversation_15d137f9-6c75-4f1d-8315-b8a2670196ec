package hotType

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
)

type CloseOrOpenHotTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCloseOrOpenHotTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloseOrOpenHotTypesLogic {
	return &CloseOrOpenHotTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *CloseOrOpenHotTypesLogic) CloseOrOpenHotTypes(req *types.CloseOrOpenHotTypeReq) (*types.CloseOrOpenHotTypeResp, error) {
	// 查询 榜单是否存在
	info, err := a.svcCtx.SongHotTypeModel.FindOne(req.Id)
	if err != nil {
		return nil, errors.New(types.ErrBillboardNotFound)
	}
	var status int64
	if info.Status == types.Up {
		status = types.Down
	} else if info.Status == types.Down {
		status = types.Up
	}

	err = a.svcCtx.SongHotTypeModel.CloseOrOpen(req.Id, status)
	if err != nil {
		return nil, err
	}

	return &types.CloseOrOpenHotTypeResp{
		Success: true,
		Message: "更新成功",
	}, nil

}
