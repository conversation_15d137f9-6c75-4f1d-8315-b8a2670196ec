package hotType

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
	"music/model/hot"
)

type AddHotTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddHotTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddHotTypesLogic {
	return &AddHotTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (a *AddHotTypesLogic) AddHotTypes(req *types.AddHotType) (*types.AddAndUpdateHotTypeResp, error) {
	// 查询 榜单是否存在
	oldHotType, err := a.svcCtx.SongHotTypeModel.FindOneByType(req.HotType)
	if err != nil {
		return nil, err
	}

	if oldHotType != nil && oldHotType.Id > 0 {
		return nil, errors.New(types.ErrHotTypeIsExist)
	}

	oldHotType, err = a.svcCtx.SongHotTypeModel.FindOneByName(req.Name)
	if err != nil {
		return nil, err
	}

	if oldHotType != nil && oldHotType.Id > 0 {
		return nil, errors.New(types.ErrHotTypeNameIsExist)
	}

	if req.HotType > 255 || req.HotType < 0 {
		return nil, errors.New(types.ErrHotTypeIsInvalid)
	}

	// 保存数据
	songHotType := &hot.SongHotType{
		Name:    req.Name,
		HotType: req.HotType,
		Status:  hot.Opern,
	}

	result, err := a.svcCtx.SongHotTypeModel.Insert(songHotType)
	if err != nil {
		return nil, err
	}

	songHotTypeId, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &types.AddAndUpdateHotTypeResp{
		Id:      songHotTypeId,
		Success: true,
		Message: "添加成功",
	}, nil

}
