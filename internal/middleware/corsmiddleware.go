package middleware

import (
	"github.com/zeromicro/go-zero/core/logx"
	"net/http"
)

// CorsMiddleware 处理跨域请求的中间件
func CorsMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取Origin请求头
		origin := r.Header.Get("Origin")

		// 配置允许的域名
		//allowedOrigins := []string{
		//	"http://192.168.88.85:9000",  // 开发环境
		//	"http://www.allbymusic.com",  // 生产环境域名
		//	"https://www.allbymusic.com", // 生产环境域名
		//}

		// 验证Origin是否在允许列表中
		allowedOrigin := "*" // 默认允许所有源（不建议在生产环境使用）
		//for _, allowed := range allowedOrigins {
		//	if origin == allowed {
		//		allowedOrigin = origin
		//		break
		//	}
		//}

		// 设置CORS响应头
		headers := w.Header()
		headers.Set("Access-Control-Allow-Origin", allowedOrigin)
		headers.Set("Access-Control-Allow-Credentials", "false")
		headers.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		headers.Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, Accept, Origin, Cache-Control, X-Requested-With")
		headers.Set("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
		headers.Set("Access-Control-Max-Age", "86400") // 24小时缓存预检请求结果

		// 设置常用响应头
		headers.Set("Content-Type", "application/json")

		// 处理预检请求
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusNoContent)
			return
		}

		// 记录跨域请求日志
		logx.Infof("CORS请求 - Method: %s, Origin: %s, Path: %s, Referer: %s\n",
			r.Method, origin, r.URL.Path, r.Header.Get("Referer"))
		// 继续处理实际请求
		next(w, r)
	}
}
