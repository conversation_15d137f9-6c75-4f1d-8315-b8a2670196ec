package middleware

//
//import (
//	"context"
//	"errors"
//	"fmt"
//	"music/internal/types"
//	"net/http"
//	"time"
//
//	"github.com/golang-jwt/jwt/v4"
//	"github.com/zeromicro/go-zero/core/logx"
//	"github.com/zeromicro/go-zero/rest/httpx"
//)
//
//var (
//	// 定义明确的错误常量
//	ErrMissingAuthHeader    = errors.New("missing authorization header")
//	ErrInvalidAuthFormat    = errors.New("invalid authorization format")
//	ErrInvalidSigningMethod = errors.New("invalid signing method")
//	ErrInvalidTokenClaims   = errors.New("invalid token claims")
//	ErrTokenExpired         = errors.New("token has expired")
//
//	// 无权限
//	ErrNoPermission = errors.New("当前用户无权限")
//)
//
//const (
//	RoleCommon = iota + 1
//	RoleAdmin
//	RoleSuperAdmin
//)
//
//type AuthMiddleware struct {
//	Secret string
//}
//
//func NewAuthMiddleware(secret string) *AuthMiddleware {
//	if secret == "" {
//		logx.Error("JWT secret key is empty, authentication will fail")
//	}
//
//	return &AuthMiddleware{
//		Secret: secret,
//	}
//}
//
//func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
//	return func(w http.ResponseWriter, r *http.Request) {
//		// 统一错误响应函数
//		sendAuthError := func(err error, status int) {
//			logx.WithContext(r.Context()).Errorf("Authentication failed: %v", err)
//			w.WriteHeader(http.StatusUnauthorized)
//			httpx.OkJson(w, &Body{
//				Status: http.StatusUnauthorized,
//				Msg:    types.ErrUserAuthFailed,
//				Data:   nil,
//			})
//		}
//
//		// 从请求中获取JWT
//		tokenString := r.Header.Get(types.Authorization)
//		if tokenString == "" {
//			sendAuthError(ErrMissingAuthHeader, http.StatusUnauthorized)
//			return
//		}
//
//		// 将用户信息存储在上下文中
//		ctx := r.Context()
//
//		ctx = context.WithValue(ctx, types.Authorization, tokenString)
//
//		/*	// 移除 'Bearer ' 前缀
//			if len(tokenString) > 7 && strings.ToUpper(tokenString[0:7]) == "BEARER " {
//				tokenString = tokenString[7:]
//			} else {
//				sendAuthError(ErrInvalidAuthFormat, http.StatusUnauthorized)
//				return
//			}*/
//
//		// 解析JWT令牌
//		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
//			// 验证签名方法
//			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
//				return nil, fmt.Errorf("%w: %v", ErrInvalidSigningMethod, token.Header["alg"])
//			}
//			return []byte(m.Secret), nil
//		})
//
//		if err != nil {
//			// 检查是否为过期错误
//			var ve *jwt.ValidationError
//			if errors.As(err, &ve) && ve.Errors&jwt.ValidationErrorExpired != 0 {
//				sendAuthError(ErrTokenExpired, http.StatusUnauthorized)
//			}
//			return
//		}
//
//		// 验证令牌有效性
//		if !token.Valid {
//			sendAuthError(jwt.ErrInvalidKey, http.StatusUnauthorized)
//			return
//		}
//
//		// 提取声明
//		claims, ok := token.Claims.(jwt.MapClaims)
//		if !ok {
//			sendAuthError(ErrInvalidTokenClaims, http.StatusUnauthorized)
//			return
//		}
//
//		// 提取用户信息，使用辅助函数增强可读性
//		userId, userUuid, username, role, err := extractUserInfo(claims)
//		if err != nil {
//			sendAuthError(err, http.StatusUnauthorized)
//			return
//		}
//
//		authUser := &types.AuthUser{
//			UserId:   userId,
//			UserUuid: userUuid,
//			Username: username,
//			Role:     role,
//		}
//
//		// 检查令牌是否快过期，添加刷新标志
//		if exp, ok := claims["exp"].(float64); ok {
//			expTime := time.Unix(int64(exp), 0)
//			// 如果令牌将在30分钟内过期，添加刷新标志
//			if time.Until(expTime) < 30*time.Minute {
//				ctx = context.WithValue(ctx, "token_refresh_needed", true)
//			}
//		}
//
//		ctx = context.WithValue(ctx, types.AuthUserKey, authUser)
//		next(w, r.WithContext(ctx))
//	}
//}
//
//// 辅助函数 - 从声明中提取用户信息
//func extractUserInfo(claims jwt.MapClaims) (userId int64, userUuid, username string, role int64, err error) {
//	// 提取用户ID
//	if idValue, ok := claims["userId"].(float64); !ok {
//		return 0, "", "", 0, fmt.Errorf("无效的userId类型: %v", claims["userId"])
//	} else {
//		userId = int64(idValue)
//	}
//
//	// 提取UUID
//	if uuidValue, ok := claims["userUuid"].(string); !ok {
//		return 0, "", "", 0, fmt.Errorf("无效的userUuid类型: %v", claims["userUuid"])
//	} else {
//		userUuid = uuidValue
//	}
//
//	// 提取用户名
//	if nameValue, ok := claims["username"].(string); !ok {
//		return 0, "", "", 0, fmt.Errorf("无效的username类型: %v", claims["username"])
//	} else {
//		username = nameValue
//	}
//
//	// 提取角色
//	if roleValue, ok := claims["role"].(float64); !ok {
//		return 0, "", "", 0, fmt.Errorf("无效的role类型: %v", claims["role"])
//	} else {
//		role = int64(roleValue)
//	}
//
//	return userId, userUuid, username, role, nil
//}
