package middleware

import (
	"context"
	"github.com/zeromicro/go-zero/rest/httpx"
	"music/internal/types"
	"net/http"
)

type Body struct {
	Status int         `json:"status"`
	Msg    string      `json:"msg"`
	Data   interface{} `json:"data,omitempty"`
}

func Response(c context.Context, w http.ResponseWriter, resp interface{}, err error) {
	var body Body
	if err != nil {
		body.Status = types.ErrorCode
		body.Msg = err.Error()
	} else {
		body.Status = types.SuccessCode
		body.Msg = "OK"
		body.Data = resp
	}
	httpx.OkJsonCtx(c, w, body)
}

func ImportResponse(c context.Context, w http.ResponseWriter, resp interface{}, err error) {
	var body Body
	if err != nil {
		body.Status = types.FailCode
		body.Data = resp
		body.Msg = err.Error()
	} else {
		body.Status = types.SuccessCode
		body.Msg = "OK"
		body.Data = resp
	}
	httpx.OkJsonCtx(c, w, body)
}
