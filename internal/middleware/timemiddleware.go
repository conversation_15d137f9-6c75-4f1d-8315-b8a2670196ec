package middleware

import (
	"errors"
	"fmt"
	"music/internal/types"
	"time"
)

var MonthLimit = -6

func DefaultMonthToUnixMilli() int64 {
	// 获取当前时间（UTC）
	currentTime := time.Now().UTC()

	// 获取三个月前的月初（UTC）
	threeMonthsAgo := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, time.UTC).AddDate(0, MonthLimit, 0)

	// 返回三个月前月初的毫秒时间戳
	return threeMonthsAgo.UnixMilli()
}

func BeginDateToUnixMilli(date string) (int64, error) {

	if date == "" {
		return DefaultMonthToUnixMilli(), nil
	}

	// 解析日期字符串
	t, err := time.ParseInLocation("2006-01-02", date, time.UTC)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	if t.UnixMilli() < DefaultMonthToUnixMilli() {
		return 0, errors.New(types.ErrNotAllowThreeMonthAgo)
	}

	// 将时间转换为Unix时间戳
	return t.UnixMilli(), nil
}

func EndDateToUnixMilli(date string) (int64, error) {
	if date == "" {
		return time.Now().UnixMilli(), nil
	}

	// 解析日期字符串
	t, err := time.ParseInLocation("2006-01-02", date, time.UTC)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	//if t.UnixMilli() > time.Now().UnixMilli() {
	//	return 0, errors.New(types.ErrNoDataDuringTheSelectedTime)
	//}

	// 将时间转换为UnixMilli时间戳
	return t.AddDate(0, 0, 1).UnixMilli() - 1, nil
}

func MonthConvertToBeginAndEndToUnixMilli(month string) (int64, int64, error) {
	// 解析日期字符串，明确使用UTC时区
	t, err := time.Parse("2006-01", month)
	if err != nil {
		return 0, 0, errors.New(types.ErrFormatTime)
	}

	// 将解析后的时间转换为UTC时区
	t = t.UTC()

	if t.UnixMilli() < DefaultMonthToUnixMilli() {
		return 0, 0, errors.New(types.ErrNotAllowThreeMonthAgo)
	}

	// 获取当前月份的第一天（UTC时区）
	firstDayOfMonth := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.UTC)

	// 获取下个月的第一天（UTC时区）
	firstDayOfNextMonth := firstDayOfMonth.AddDate(0, 1, 0)

	// 将时间转换为Unix时间戳
	return firstDayOfMonth.UnixMilli(), firstDayOfNextMonth.UnixMilli() - 1, nil
}

func TimeToUnix(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 解析日期字符串
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	// 将时间转换为Unix时间戳
	return t.Unix(), nil
}

func TimeUTCToUnix(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 解析日期字符串
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	// 将时间转换为Unix时间戳
	return t.Unix(), nil
}

func DateToUnix(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 解析日期字符串
	t, err := time.Parse("2006-01-02 ", timeStr)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	// 将时间转换为Unix时间戳
	return t.Unix(), nil
}

func DateUTCToUnix(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 解析日期字符串
	t, err := time.Parse("2006-01-02 ", timeStr)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	// 将时间转换为Unix时间戳
	return t.UTC().Unix(), nil
}

func DateToUnixMilli(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 解析日期字符串
	t, err := time.Parse("2006-01-02", timeStr)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	// 将时间转换为Unix时间戳
	return t.AddDate(0, 0, 1).UnixMilli() - 1, nil
}

func UnixMilliToDate(unixTime int64) string {
	if unixTime == 0 {
		return ""
	}
	// 将Unix时间戳转换为时间对象
	t := time.UnixMilli(unixTime)

	// 格式化为字符串
	return t.Format("2006-01-02")
}

func UnixToDate(unixTime int64) string {
	if unixTime == 0 {
		return ""
	}
	// 将Unix时间戳转换为时间对象
	t := time.Unix(unixTime, 0)

	// 格式化为字符串
	return t.Format("2006-01-02")
}

func UnixToTime(unixTime int64) string {
	if unixTime == 0 {
		return ""
	}
	// 将Unix时间戳转换为时间对象
	t := time.Unix(unixTime, 0)

	// 格式化为字符串
	return t.Format("2006-01-02 15:04:05")
}

func UnixToUTCTime(unixTime int64) string {
	if unixTime == 0 {
		return ""
	}
	// 将Unix时间戳转换为时间对象
	t := time.Unix(unixTime, 0).UTC()

	// 格式化为字符串
	return t.Format("2006-01-02 15:04:05")
}

func UnixMilliToTime(unixTime int64) string {
	if unixTime == 0 {
		return ""
	}
	// 将Unix时间戳转换为时间对象
	t := time.UnixMilli(unixTime)

	// 格式化为字符串
	return t.Format("2006-01-02 15:04:05")
}

// convertDateToTimestampRange 将日期字符串转换为当天的开始和结束时间戳（毫秒）
func ConvertDateToTimestampRange(dateStr string) (int64, int64, error) {
	// 解析日期字符串
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("无效的日期格式: %w", err)
	}

	// 调整为UTC+8时区
	loc, _ := time.LoadLocation("Asia/Shanghai")
	date = date.In(loc)

	// 计算当天开始时间（00:00:00）
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, loc)
	// 计算当天结束时间（23:59:59.999）
	endOfDay := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 999000000, loc)

	// 转换为毫秒时间戳
	startTs := startOfDay.UnixNano() / 1000000
	endTs := endOfDay.UnixNano() / 1000000

	return startTs, endTs, nil
}

func ConvertDateToUTCTimestampRange(dateStr string) (int64, int64, error) {
	// 解析日期字符串为UTC时区
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("无效的日期格式: %w", err)
	}

	// 确保使用UTC时区
	date = date.UTC()

	// 计算当天开始时间（00:00:00 UTC）
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)

	// 计算当天结束时间（23:59:59.999 UTC）
	endOfDay := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 999000000, time.UTC)

	// 转换为毫秒时间戳
	startTs := startOfDay.UnixMilli()
	endTs := endOfDay.UnixMilli()

	// 打印调试信息
	fmt.Printf("日期 %s 在 UTC 时区的时间范围:\n", dateStr)
	fmt.Printf("开始时间: %s (%d)\n", startOfDay.Format(time.RFC3339), startTs)
	fmt.Printf("结束时间: %s (%d)\n", endOfDay.Format(time.RFC3339), endTs)

	return startTs, endTs, nil
}

func NowToUnixMilli() int64 {
	// 获取当前时间（UTC）
	currentTime := time.Now().UTC()

	// 返回当前时间的毫秒时间戳
	return currentTime.UnixMilli()
}

// startTime
func DefaultMonthToUnixStartTime(monthLimit int) int64 {
	// 获取当前时间（UTC）
	currentTime := time.Now().UTC()

	// 获取三个月前的月初（UTC）
	threeMonthsAgo := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, time.UTC).AddDate(0, monthLimit, 0)

	// 返回三个月前月初的毫秒时间戳
	return threeMonthsAgo.Unix()
}

func BeginDateToUnixStartTime(date string, monthLimit int) (int64, error) {

	if date == "" {
		return DefaultMonthToUnixMilli(), nil
	}

	// 解析日期字符串
	t, err := time.ParseInLocation("2006-01-02", date, time.UTC)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	if t.Unix() < DefaultMonthToUnixStartTime(monthLimit) {
		return 0, errors.New(types.ErrNotAllowThreeMonthAgo)
	}

	// 将时间转换为Unix时间戳
	return t.Unix(), nil
}

func EndDateToUnixStartTime(date string) (int64, error) {
	if date == "" {
		return time.Now().Unix(), nil
	}

	// 解析日期字符串
	t, err := time.ParseInLocation("2006-01-02", date, time.UTC)
	if err != nil {
		return 0, errors.New(types.ErrFormatTime)
	}

	// 将时间转换为Unix时间戳
	return t.AddDate(0, 0, 1).Unix() - 1, nil
}

func MonthConvertToBeginAndEndToUnixStartTime(month string, monthLimit int) (int64, int64, error) {
	// 解析日期字符串，明确使用UTC时区
	t, err := time.Parse("2006-01", month)
	if err != nil {
		return 0, 0, errors.New(types.ErrFormatTime)
	}

	// 将解析后的时间转换为UTC时区
	t = t.UTC()

	if t.Unix() < DefaultMonthToUnixStartTime(monthLimit) {
		return 0, 0, errors.New(types.ErrNotAllowThreeMonthAgo)
	}

	// 获取当前月份的第一天（UTC时区）
	firstDayOfMonth := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.UTC)

	// 获取下个月的第一天（UTC时区）
	firstDayOfNextMonth := firstDayOfMonth.AddDate(0, 1, 0)

	// 将时间转换为Unix时间戳
	return firstDayOfMonth.Unix(), firstDayOfNextMonth.Unix() - 1, nil
}

func ConvertDateToUTCTimestampRangeStartTime(dateStr string) (int64, int64, error) {
	// 解析日期字符串为UTC时区
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("无效的日期格式: %w", err)
	}

	// 确保使用UTC时区
	date = date.UTC()

	// 计算当天开始时间（00:00:00 UTC）
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)

	// 计算当天结束时间（23:59:59.999 UTC）
	endOfDay := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 999000000, time.UTC)

	// 转换为毫秒时间戳
	startTs := startOfDay.Unix()
	endTs := endOfDay.Unix()

	// 打印调试信息
	fmt.Printf("日期 %s 在 UTC 时区的时间范围:\n", dateStr)
	fmt.Printf("开始时间: %s (%d)\n", startOfDay.Format(time.RFC3339), startTs)
	fmt.Printf("结束时间: %s (%d)\n", endOfDay.Format(time.RFC3339), endTs)

	return startTs, endTs, nil
}

func NowToUnixStartTime() int64 {
	// 获取当前时间（UTC）
	currentTime := time.Now().UTC()

	// 返回当前时间的毫秒时间戳
	return currentTime.Unix()
}
