package middleware

import (
	"context"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"music/internal/types"
	"net/http"
	"strings"
	"time"

	"encoding/base64"
	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

var (
	// 定义明确的错误常量
	ErrMissingAuthHeader    = errors.New("missing authorization header")
	ErrInvalidAuthFormat    = errors.New("invalid authorization format")
	ErrInvalidSigningMethod = errors.New("invalid signing method")
	ErrInvalidTokenClaims   = errors.New("invalid token claims")
	ErrTokenExpired         = errors.New("token has expired")

	// 无权限
	ErrNoPermission = errors.New("当前用户无权限")
)

const (
	RoleCommon = iota + 1
	RoleAdmin
	RoleSuperAdmin
)

type AuthMiddleware struct {
	Secret string
	Redis  *redis.Redis
}

func NewAuthMiddleware(secret string, redisClient *redis.Redis) *AuthMiddleware {
	if secret == "" {
		logx.Error("JWT secret key is empty, authentication will fail")
	}

	return &AuthMiddleware{
		Secret: secret,
		Redis:  redisClient,
	}
}

// TokenValidationError 自定义token验证错误
type TokenValidationError struct {
	Message string
	Code    int
}

func (e *TokenValidationError) Error() string {
	return e.Message
}

func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 统一错误响应函数
		sendAuthError := func(err error, status int) {
			logx.WithContext(r.Context()).Errorf("Authentication failed: %v", err)
			w.WriteHeader(http.StatusUnauthorized)
			httpx.OkJson(w, &Body{
				Status: http.StatusUnauthorized,
				Msg:    types.ErrUserAuthFailed,
				Data:   nil,
			})
		}

		// 从请求中获取JWT
		tokenString := r.Header.Get(types.Authorization)
		if tokenString == "" {
			sendAuthError(ErrMissingAuthHeader, http.StatusUnauthorized)
			return
		}

		// 基本格式验证
		if !m.validateTokenFormat(tokenString) {
			sendAuthError(ErrInvalidAuthFormat, http.StatusUnauthorized)
			return
		}

		// 验证token
		claims, err := m.validateToken(tokenString)
		if err != nil {
			//var validationErr *TokenValidationError
			//if errors.As(err, &validationErr) {
			//	sendAuthError(err, validationErr.Code)
			//} else {
			sendAuthError(err, http.StatusUnauthorized)
			//}
			return
		}

		// 验证token完整性
		if err := m.validateTokenIntegrity(claims); err != nil {
			sendAuthError(err, http.StatusUnauthorized)
			return
		}

		// 提取用户信息
		authUser, err := m.extractUserFromClaims(claims)
		if err != nil {
			sendAuthError(err, http.StatusUnauthorized)
			return
		}

		// 将用户信息存储在上下文中
		ctx := context.WithValue(r.Context(), types.AuthUserKey, authUser)
		ctx = context.WithValue(ctx, types.Authorization, tokenString)

		// 检查token是否需要刷新
		if m.shouldRefreshToken(claims) {
			ctx = context.WithValue(ctx, "token_refresh_needed", true)
		}

		next(w, r.WithContext(ctx))
	}
}

// validateTokenFormat 验证token的基本格式
func (m *AuthMiddleware) validateTokenFormat(tokenString string) bool {
	// 验证token长度
	if len(tokenString) < 10 {
		return false
	}

	// 验证token格式（确保是三段式JWT）
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		return false
	}

	// 验证每个部分都是有效的Base64URL编码
	for _, part := range parts {
		if _, err := base64.RawURLEncoding.DecodeString(part); err != nil {
			return false
		}
	}

	return true
}

// validateToken 验证token并返回claims
func (m *AuthMiddleware) validateToken(tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, &TokenValidationError{
				Message: fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]),
				Code:    http.StatusUnauthorized,
			}
		}
		return []byte(m.Secret), nil
	})

	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, &TokenValidationError{
					Message: "token已过期",
					Code:    http.StatusUnauthorized,
				}
			}
			if ve.Errors&jwt.ValidationErrorSignatureInvalid != 0 {
				return nil, &TokenValidationError{
					Message: "token签名无效",
					Code:    http.StatusUnauthorized,
				}
			}
		}
		return nil, &TokenValidationError{
			Message: "token验证失败",
			Code:    http.StatusUnauthorized,
		}
	}

	if !token.Valid {
		return nil, &TokenValidationError{
			Message: "无效的token",
			Code:    http.StatusUnauthorized,
		}
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, &TokenValidationError{
			Message: "无效的token格式",
			Code:    http.StatusUnauthorized,
		}
	}

	return claims, nil
}

// validateTokenIntegrity 验证token的完整性
func (m *AuthMiddleware) validateTokenIntegrity(claims jwt.MapClaims) error {
	// 验证必要字段
	requiredFields := []string{"userId", "userUuid", "username", "role", "iat", "exp"}
	for _, field := range requiredFields {
		if _, exists := claims[field]; !exists {
			return &TokenValidationError{
				Message: fmt.Sprintf("token缺少必要字段: %s", field),
				Code:    http.StatusUnauthorized,
			}
		}
	}

	// 验证发行时间
	if iat, ok := claims["iat"].(float64); ok {
		issuedAt := time.Unix(int64(iat), 0)
		if issuedAt.After(time.Now()) {
			return &TokenValidationError{
				Message: "token发行时间无效",
				Code:    http.StatusUnauthorized,
			}
		}
	}

	// 验证角色值是否有效
	if role, ok := claims["role"].(float64); ok {
		validRoles := map[int64]bool{
			RoleCommon:     true,
			RoleAdmin:      true,
			RoleSuperAdmin: true,
		}
		if !validRoles[int64(role)] {
			return &TokenValidationError{
				Message: "无效的用户角色",
				Code:    http.StatusUnauthorized,
			}
		}
	}

	// 验证UUID格式
	if userUuid, ok := claims["userUuid"].(string); ok {
		if _, err := uuid.Parse(userUuid); err != nil {
			return &TokenValidationError{
				Message: "无效的用户UUID格式",
				Code:    http.StatusUnauthorized,
			}
		}
	}

	return nil
}

// extractUserFromClaims 从claims中提取用户信息
func (m *AuthMiddleware) extractUserFromClaims(claims jwt.MapClaims) (*types.AuthUser, error) {
	userId, ok := claims["userId"].(float64)
	if !ok {
		return nil, &TokenValidationError{
			Message: "无效的userId类型",
			Code:    http.StatusUnauthorized,
		}
	}

	userUuid, ok := claims["userUuid"].(string)
	if !ok {
		return nil, &TokenValidationError{
			Message: "无效的userUuid类型",
			Code:    http.StatusUnauthorized,
		}
	}

	username, ok := claims["username"].(string)
	if !ok {
		return nil, &TokenValidationError{
			Message: "无效的username类型",
			Code:    http.StatusUnauthorized,
		}
	}

	role, ok := claims["role"].(float64)
	if !ok {
		return nil, &TokenValidationError{
			Message: "无效的role类型",
			Code:    http.StatusUnauthorized,
		}
	}

	return &types.AuthUser{
		UserId:   int64(userId),
		UserUuid: userUuid,
		Username: username,
		Role:     int64(role),
	}, nil
}

// shouldRefreshToken 检查是否需要刷新token
func (m *AuthMiddleware) shouldRefreshToken(claims jwt.MapClaims) bool {
	if exp, ok := claims["exp"].(float64); ok {
		expTime := time.Unix(int64(exp), 0)
		// 如果token将在30分钟内过期，返回true
		return time.Until(expTime) < 30*time.Minute
	}
	return false
}
