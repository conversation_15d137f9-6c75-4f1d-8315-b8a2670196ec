package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"music/internal/types"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
)

type TokenValidator struct {
	secret string
}

func NewTokenValidator(secret string) *TokenValidator {
	return &TokenValidator{
		secret: secret,
	}
}

// ValidateTokenFingerprint 验证token指纹
func (tv *TokenValidator) ValidateTokenFingerprint(token *jwt.Token, userInfo *types.AuthUser) bool {
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return false
	}

	storedFingerprint, ok := claims["fingerprint"].(string)
	if !ok {
		return false
	}

	// 重新生成指纹
	data := fmt.Sprintf("%d:%s:%s:%d:%d",
		userInfo.UserId,
		userInfo.UserUuid,
		userInfo.Username,
		userInfo.Role,
		claims["iat"].(float64),
	)

	hash := sha256.Sum256([]byte(data))
	calculatedFingerprint := base64.URLEncoding.EncodeToString(hash[:])

	return storedFingerprint == calculatedFingerprint
}

// ValidateTokenClaims 验证token claims
func (tv *TokenValidator) ValidateTokenClaims(claims jwt.MapClaims) error {
	// 验证JTI (JWT ID)
	if jti, ok := claims["jti"].(string); ok {
		if _, err := uuid.Parse(jti); err != nil {
			return fmt.Errorf("invalid jti format")
		}
	} else {
		return fmt.Errorf("missing jti claim")
	}

	// 验证时间相关的claims
	now := time.Now().Unix()
	if iat, ok := claims["iat"].(float64); ok {
		if int64(iat) > now {
			return fmt.Errorf("token issued in the future")
		}
	} else {
		return fmt.Errorf("missing iat claim")
	}

	if exp, ok := claims["exp"].(float64); ok {
		if int64(exp) < now {
			return fmt.Errorf("token has expired")
		}
	} else {
		return fmt.Errorf("missing exp claim")
	}

	return nil
}
