package types

// 歌曲相关类型
type (
	SongInfo struct {
		Id                int64  `json:"id"`
		ImportId          int64  `json:"importId"`
		Type              int64  `json:"type"`
		VendorId          int64  `json:"vendorId,optional"`
		VendorName        string `json:"vendorName,optional"`
		SongCode          int64  `json:"songCode"`
		Name              string `json:"name"`
		Singer            string `json:"singer,optional"`
		VendorSongId      string `json:"vendorSongId,optional"`
		VendorReleaseTime string `json:"vendorReleaseTime,optional"`
		SongPath          string `json:"songPath,optional"`
		DrmPath           string `json:"drmPath,optional"`
		LicenseKey        string `json:"licenseKey,optional"`
		PosterPath        string `json:"posterPath,optional"`
		LyricPath         string `json:"lyricPath,optional"`
		LyricType         string `json:"lyricType,optional"`
		PitchType         int64  `json:"pitchType,optional"`
		Pitchs            string `json:"pitchs"`
		HighPart          string `json:"highPart,optional"`
		HighPartType      int64  `json:"highPartType,optional"`
		QualityLevel      string `json:"qualityLevel,optional"`
		TagIds            string `json:"tagIds,optional"`
		Duration          int64  `json:"duration,optional"`
		CloseTime         string `json:"closeTime,optional"`
		HotNum            int64  `json:"hotNum,optional"`
		Status            int64  `json:"status,optional"`
		CreateTime        string `json:"createTime"`
		UpdateTime        string `json:"updateTime"`
	}

	ListSongReq struct {
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		Name              string `json:"name,optional"`
		Singer            string `json:"singer,optional"`
		SongCode          int64  `json:"songCode,optional"`
		Type              int64  `json:"type,optional"`         // 歌曲类型 1原唱&伴奏，2伴奏，3原唱，4多音轨
		HighPartType      int64  `json:"highPartType,optional"` // 1无副歌，2机器校验，3人工校验
		TagIds            string `json:"tagIds,optional"`
		LyricType         string `json:"lyricType,optional"`
		Status            int64  `json:"status,default=99"` // 0:下架, 1:上架
		Pitchs            string `json:"pitchs,optional"`
		VendorId          int64  `json:"vendorId,optional"`
		VendorSongId      string `json:"vendorSongId,optional"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
		OrderByCloseTime  string `json:"orderByCloseTime,optional"`  // 1:升序，2:降序
		CloseTimeBegin    string `json:"closeTimeBegin,optional"`
		CloseTimeEnd      string `json:"closeTimeEnd,optional"`
		CreateTimeBegin   string `json:"createTimeBegin,optional"`
		CreateTimeEnd     string `json:"createTimeEnd,optional"`
	}

	ListSongResp struct {
		Total   int64               `json:"total"`
		List    []SongInfo          `json:"list"`
		Columns []SongDisplayColumn `json:"columns,optional"` // 用户设置的展示列
	}

	GetSongReq struct {
		Id       int64 `form:"id,optional"`
		SongCode int64 `form:"songCode,optional"`
	}

	GetSongResp struct {
		Song SongInfo `json:"song"`
	}

	// 歌曲可选展示列
	SongDisplayColumnOld struct {
		Key      string `json:"key"`      // 字段名称
		Title    string `json:"title"`    // 显示标题
		Visible  bool   `json:"visible"`  // 是否显示
		Sortable bool   `json:"sortable"` // 是否可排序
		Width    int    `json:"width"`    // 宽度（像素）
		Fixed    bool   `json:"fixed"`    // 是否固定列
	}

	SongDisplayColumn struct {
		Title    string `json:"title,optional"` // 显示标题
		Field    string `json:"field,optional"` // 字段
		Align    string `json:"align,optional"`
		Width    int    `json:"width,optional"`
		IsShow   bool   `json:"isShow,optional"`
		Sortable bool   `json:"sortable,optional"` // 是否可排序
		Slots    Slots  `json:"slots,optional"`    // 自定义插槽
	}

	Slots struct {
		Default string `json:"default"`
	}

	// 设置歌曲展示列请求
	SetSongDisplayColumnsReq struct {
		Columns []SongDisplayColumn `json:"columns"`
	}

	// 设置歌曲展示列响应
	SetSongDisplayColumnsResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 获取歌曲展示列请求
	GetSongDisplayColumnsReq struct {
	}

	// 获取歌曲展示列响应
	GetSongDisplayColumnsResp struct {
		Columns []SongDisplayColumn `json:"columns"`
	}

	// 创建歌曲请求
	CreateSongReq struct {
		ImportId          int64  `json:"importId,optional"`
		Type              int64  `json:"type,optional"`
		VendorId          int64  `json:"vendorId,optional"`
		SongCode          int64  `json:"songCode,optional"`
		Name              string `json:"name"`
		Singer            string `json:"singer,optional"`
		VendorSongId      string `json:"vendorSongId,optional"`
		VendorReleaseTime string `json:"vendorReleaseTime,optional"`
		SongPath          string `json:"songPath,optional"`
		DrmPath           string `json:"drmPath,optional"`
		LicenseKey        string `json:"licenseKey,optional"`
		PosterPath        string `json:"posterPath,optional"`
		LyricPath         string `json:"lyricPath,optional"`
		LyricType         string `json:"lyricType,optional"`
		PitchType         int64  `json:"pitchType,optional"`
		Pitchs            string `json:"pitchs,optional"`
		HighPart          string `json:"highPart,optional"`
		HighPartType      int64  `json:"highPartType,optional"`
		QualityLevel      string `json:"qualityLevel,optional"`
		TagIds            string `json:"tagIds,optional"`
		Duration          int64  `json:"duration,optional"`
		HotNum            int64  `json:"hotNum,optional"`
		Status            int64  `json:"status,optional"`
	}

	// 创建歌曲响应
	CreateSongResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 更新歌曲请求
	UpdateSongReq struct {
		Id                int64  `json:"id"`
		ImportId          int64  `json:"importId"`
		Type              int64  `json:"type,optional"`
		VendorId          int64  `json:"vendorId,optional"`
		VendorName        string `json:"vendorName,optional"`
		SongCode          int64  `json:"songCode"`
		Name              string `json:"name"`
		Singer            string `json:"singer,optional"`
		VendorSongId      string `json:"vendorSongId,optional"`
		VendorReleaseTime string `json:"vendorReleaseTime,optional"`
		SongPath          string `json:"songPath,optional"`
		DrmPath           string `json:"drmPath,optional"`
		LicenseKey        string `json:"licenseKey,optional"`
		PosterPath        string `json:"posterPath,optional"`
		LyricPath         string `json:"lyricPath,optional"`
		LyricType         string `json:"lyricType,optional"`
		PitchType         int64  `json:"pitchType,optional"`
		Pitchs            string `json:"pitchs"`
		HighPart          string `json:"highPart,optional"`
		HighPartType      int64  `json:"highPartType,optional"`
		QualityLevel      string `json:"qualityLevel,optional"`
		TagIds            string `json:"tagIds,optional"`
		Duration          int64  `json:"duration,optional"`
		HotNum            int64  `json:"hotNum,optional"`
	}

	UpdateSongResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 更新歌曲响应
	CommonSongResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 删除歌曲请求
	DeleteSongReq struct {
		Id int64 `json:"id"`
	}

	// 删除歌曲响应
	DeleteSongResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 更新歌曲状态请求
	UpdateSongStatusReq struct {
		Id     int64 `json:"id"`
		Status int64 `json:"status"` // 0:下架, 1:上架
	}

	// 更新歌曲状态响应
	UpdateSongStatusResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 批量操作歌曲请求
	BatchOperateSongReq struct {
		Ids       []int64 `json:"ids"`
		Operation string  `json:"operation"` // delete, changeStatus
		Status    int64   `json:"status,optional"`
	}

	// 批量操作歌曲响应
	BatchOperateSongResp struct {
		Success      bool   `json:"success"`
		SuccessCount int    `json:"successCount"`
		FailCount    int    `json:"failCount"`
		Message      string `json:"message,optional"`
	}

	// 查询导入任务状态请求
	GetImportTaskStatusReq struct {
		TaskId string `form:"taskId"`
	}

	// 查询导入任务状态响应
	GetImportTaskStatusResp struct {
		TaskId       string `json:"taskId"`
		Status       string `json:"status"` // pending, processing, completed, failed
		TotalCount   int    `json:"totalCount"`
		SuccessCount int    `json:"successCount"`
		FailCount    int    `json:"failCount"`
		Message      string `json:"message,optional"`
		FailReason   string `json:"failReason,optional"`
		Progress     int    `json:"progress"` // 0-100
	}

	// 上架 下架
	ListedSongReq struct {
		SongCodes []int64 `json:"songCodes"`
		Status    int64   `json:"status"` // 0:下架, 1:上架
	}

	ListedSongResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	ListSongForHotReq struct {
		Page     int64  `json:"page,default=1"`
		PageSize int64  `json:"pageSize,default=10"`
		Name     string `json:"name,optional"`
		HotType  int64  `json:"hotType,optional"` // 热度类型
		Status   int64  `json:"status,optional"`  // 0:下架, 1:上架
	}

	SongInfoForHot struct {
		Id           int64  `json:"id"`
		ImportId     int64  `json:"importId"`
		Type         int64  `json:"type"`
		VendorId     int64  `json:"vendorId"`
		VendorName   string `json:"vendorName,optional"`
		SongCode     int64  `json:"songCode"`
		Name         string `json:"name"`
		Singer       string `json:"singer"`
		VendorSongId string `json:"vendorSongId"`
		CloseTime    int64  `json:"closeTime"`
		Status       int64  `json:"status"`
	}

	ListSongForHotResp struct {
		Total int64            `json:"total"`
		List  []SongInfoForHot `json:"list"`
	}

	ListSongForPlayRecordReq struct {
		Vid      int64 `json:"vid,optional"`
		Page     int64 `json:"page,optional"`
		PageSize int64 `json:"pageSize,optional"`
	}
)

var (
	SongTypeMap = map[string]int64{
		"原唱&伴奏": 1,
		"伴奏":    2,
		"原唱":    3,
		"多音轨":   4,
	}

	SongHighPartTypeMap = map[string]int64{
		"无副歌":  1,
		"机器校验": 2,
		"人工校验": 3,
	}

	SongLyricTypeMap = map[string]string{
		"zip格式":             "0",
		"lrc格式":             "1",
		"vtt格式":             "2",
		"zip格式;lrc格式":       "0,1",
		"lrc格式;vtt格式":       "1,2",
		"zip格式;vtt格式":       "0,2",
		"zip格式;lrc格式;vtt格式": "0,1,2",
	}
)

const (
	Down int64 = iota
	Up
)
