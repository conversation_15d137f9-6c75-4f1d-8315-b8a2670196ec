package types

// 客户相关类型
type (
	CustomerInfo struct {
		Id         int64  `json:"id"`
		Name       string `json:"name,optional"`
		Vid        int64  `json:"vid,optional"`
		Cid        int64  `json:"cid,optional"`
		Ip         string `json:"ip,optional"`
		Appid      string `json:"appid,optional"`
		Remark     string `json:"remark,optional"`
		Status     int64  `json:"status,optional"`
		EndTime    string `json:"endTime,optional"`
		UpdateTime string `json:"updateTime,optional"`
		CreateTime string `json:"createTime,optional"`
	}

	ListCustomerReq struct {
		Name              string `json:"name,optional"`
		Vid               int64  `json:"vid,optional"`
		Cid               int64  `json:"cid,optional"`
		BeginEndTime      string `json:"beginTime,optional"`
		EndEndTime        string `json:"endTime,optional"`
		Status            int64  `json:"status,default=99"`
		Ip                string `json:"ip,optional"`
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
	}

	ListCustomerResp struct {
		Total int64          `json:"total"`
		List  []CustomerInfo `json:"list"`
	}

	GetCustomerReq struct {
		Id  int64 `form:"id,optional"`
		Vid int64 `form:"vid,optional"`
	}

	GetCustomerResp struct {
		Customer CustomerInfo `json:"customer"`
	}

	UpdateCustomerStatusReq struct {
		Id     int64 `json:"id"`
		Status int64 `json:"status"`
	}

	UpdateCustomerResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	AddCustomerInfo struct {
		Name    string `json:"name"`
		Vid     int64  `json:"vid"`
		Cid     int64  `json:"cid"`
		Ip      string `json:"ip"`
		Appid   string `json:"appid"`
		Remark  string `json:"remark"`
		Status  int64  `json:"status"`
		EndTime string `json:"endTime"`
	}

	AddCustomerResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	VidOrCidReq struct {
		Name string `json:"name,optional"`
	}

	VidOrCidResp struct {
		Vid []int64 `json:"vid"`
		Cid []int64 `json:"cid"`
	}
)

const (
	CustomerClose = iota
	CustomerOpen
)
