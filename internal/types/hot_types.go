package types

// 热歌榜单相关类型
type (
	HotTypeInfo struct {
		Id         int64  `json:"id"`
		HotType    int64  `json:"hotType"`
		Name       string `json:"name"`
		Num        int64  `json:"num"`
		Status     int64  `json:"status"`
		UpdateTime string `json:"updateTime"`
		CreateTime string `json:"createTime"`
	}

	HotSongInfo struct {
		Id         int64  `json:"id"`
		HotType    int64  `json:"hotType,optional"`
		SongCode   int64  `json:"songCode,optional"`
		Vid        int64  `json:"vid,optional"`
		Num        int64  `json:"num,optional"`
		SongName   string `json:"songName,optional"`
		SongSinger string `json:"songSinger,optional"`
	}

	ListHotTypesReq struct {
		HotType           int64  `json:"hotType,optional"`
		Name              string `json:"name,optional"`
		Status            int64  `json:"status,default=99"`
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
	}

	ListHotTypesResp struct {
		Total int64         `json:"total"`
		List  []HotTypeInfo `json:"list"`
	}

	ListHotSongsReq struct {
		HotType           int64  `json:"hotType,optional"`
		SongCode          int64  `json:"songCode,optional"`
		Name              string `json:"name,optional"`
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
	}

	ListHotSongDetail struct {
		Id         int64  `json:"id"`
		HotType    int64  `json:"hotType"`
		SongCode   int64  `json:"songCode"`
		Vid        int64  `json:"vid"`
		Num        int64  `json:"num"`
		SongName   string `json:"songName"`
		SongSinger string `json:"songSinger"`
		UpdateTime string `json:"updateTime"`
		CreateTime string `json:"createTime"`
	}

	ListHotSongsResp struct {
		Total int64               `json:"total"`
		List  []ListHotSongDetail `json:"list"`
	}

	AddHotType struct {
		HotType int64  `json:"hotType"`
		Name    string `json:"name"`
	}

	UpdateHotType struct {
		Id      int64  `json:"id"`
		HotType int64  `json:"hotType"`
		Name    string `json:"name"`
	}

	AddHotSongInfo struct {
		HotType  []int64 `json:"hotType"`
		SongCode int64   `json:"songCode"`
		Vid      int64   `json:"vid"`
		Num      int64   `json:"num"`
	}

	AddAndUpdateHotSongResp struct {
		Id      int64  `json:"id"`
		Message string `json:"message"`
	}

	AddAndUpdateHotTypeResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	CloseOrOpenHotTypeReq struct {
		Id int64 `json:"id"`
		//Status int64 `json:"status"`
	}

	CloseOrOpenHotTypeResp struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	ListHotSongsNumReq struct {
		HotType int64 `json:"hotType,optional"`
	}

	ListHotSongsNum struct {
		Num      int64  `json:"num"`
		SongCode int64  `json:"songCode"`
		SongName string `json:"songName"`
	}

	ListHotSongsNumResp struct {
		Total int64             `json:"total"`
		List  []ListHotSongsNum `json:"list"`
	}

	DelHotSong struct {
		Id int64 `json:"id"`
	}

	CheckHotSongNum struct {
		HotType []int64 `json:"hotType"`
		Num     int64   `json:"num"`
	}

	CheckHotSongNumResp struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
	}
)
