package types

import "time"

// 播放记录相关类型
type (
	// 播放记录信息
	PlayRecordInfo struct {
		Id           int64     `json:"id"`
		Lts          int64     `json:"lts"`
		Vid          int64     `json:"vid"`
		UniqueId     string    `json:"uniqueId"`
		CpId         int64     `json:"cpId"`
		SongCode     int64     `json:"songCode"`
		SongName     string    `json:"songName,optional"`   // 冗余字段，便于前端展示
		SongSinger   string    `json:"songSinger,optional"` // 冗余字段，便于前端展示
		SongDuration int       `json:"songDuration"`
		PlayDuration int       `json:"playDuration"`
		StartTime    int64     `json:"startTime"`
		EndTime      int64     `json:"endTime"`
		PlaybackPos  int       `json:"playbackPos"`
		PlayNum      int       `json:"playNum"`
		SceneType    int       `json:"sceneType"`
		PlayType     int       `json:"playType"`
		FreeType     int       `json:"freeType"`
		RecordType   int       `json:"recordType"`
		UseType      int       `json:"useType"`
		PushStatus   int       `json:"pushStatus"`
		PushTime     int64     `json:"pushTime"`
		CreateTime   time.Time `json:"createTime"`
	}

	// 添加播放记录请求
	CreatePlayRecordReq struct {
		Lts          int64  `json:"lts"`
		Vid          int64  `json:"vid"`
		UniqueId     string `json:"uniqueId"`
		RequestIp    string `json:"requestIp,optional"`
		CpId         int64  `json:"cpId"`
		SongCode     int64  `json:"songCode"`
		SongDuration int    `json:"songDuration"`
		PlayDuration int    `json:"playDuration"`
		StartTime    int64  `json:"startTime"`
		EndTime      int64  `json:"endTime"`
		PlaybackPos  int    `json:"playbackPos"`
		PlayNum      int    `json:"playNum"`
		SceneType    int    `json:"sceneType"`
		PlayType     int    `json:"playType"`
		FreeType     int    `json:"freeType"`
		RecordType   int    `json:"recordType"`
		UseType      int    `json:"useType"`
		Uid          string `json:"uid,optional"`
		ChannelId    string `json:"channelId,optional"`
	}

	// 添加播放记录响应
	CreatePlayRecordResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 查询播放记录请求 - 增加更多查询条件
	_ListPlayRecordReq struct {
		Vid        int64  `form:"vid,optional"`        // 客户ID
		SongCode   int64  `form:"songCode,optional"`   // 歌曲编号
		SongName   string `form:"songName,optional"`   // 歌曲名称
		Singer     string `form:"singer,optional"`     // 歌手名称
		CpId       int64  `form:"cpId,optional"`       // 版权方ID
		VendorName string `form:"vendorName,optional"` // 版权方名称
		PlayType   int    `form:"playType,optional"`   // 播放类型
		FreeType   int    `form:"freeType,optional"`   // 免费类型
		RecordType int    `form:"recordType,optional"` // 记录类型
		UseType    int    `form:"useType,optional"`    // 用户类型
		SceneType  int    `form:"sceneType,optional"`  // 场景类型
		PushStatus int    `form:"pushStatus,optional"` // 推送状态
		StartDate  string `form:"startDate,optional"`  // 开始日期 YYYY-MM-DD
		EndDate    string `form:"endDate,optional"`    // 结束日期 YYYY-MM-DD
		Page       int64  `form:"page,default=1"`
		PageSize   int64  `form:"pageSize,default=10"`
	}

	SumPlayRecordReq struct {
		StartDate    string `json:"startDate"` // 格式：YYYY-MM-DD
		EndDate      string `json:"endDate"`   // 格式：YYYY-MM-DD
		Month        string `json:"month"`     // 格式：YYYY-MM
		PlayType     int    `json:"playType,optional"`
		CustomerName string `json:"customerName,optional"`
		UseType      int64  `json:"useType,optional"`
		Vid          int64  `json:"vid,optional"`
		SongCode     int64  `json:"songCode,optional"`
		SongName     string `json:"songName,optional"`
		Page         int64  `json:"page,default=1"`
		PageSize     int64  `json:"pageSize,default=10"`
	}

	SumPlayRecord struct {
		// 日期
		LtsTime  string `json:"ltsTime"`
		PlayType int64  `json:"playType"`

		// 歌曲信息
		SongCode     int64  `json:"songCode"`
		SongName     string `json:"songName"`
		SongDuration int64  `json:"songDuration"`
		CpId         int64  `json:"cpId"`
		CpName       string `json:"cpName"`

		// 客户信息
		Vid          int64  `json:"vid"`
		CustomerName string `json:"customerName"`
		UseType      int64  `json:"useType"`

		// 统计数据
		PlayDurationTotal int64 `json:"playDurationTotal"`
		PlayNumTotal      int64 `json:"playNumTotal"`
	}

	// 查询播放记录响应
	SumPlayRecordResp struct {
		Total          int64           `json:"total"`
		List           []SumPlayRecord `json:"list"`
		LastUpdateTime string          `json:"lastUpdateTime"` // 最后更新时间
	}

	SumPlayRecordBySongReq struct {
		LtsTime  string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		PlayType int    `json:"playType"`
		SongCode int64  `json:"songCode,optional"`
		SongName string `json:"songName,optional"`
		Page     int64  `json:"page,default=1"`
		PageSize int64  `json:"pageSize,default=10"`
	}

	SumPlayRecordByVidReq struct {
		LtsTime      string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		PlayType     int    `json:"playType"`
		CustomerName string `json:"customerName,optional"`
		UseType      int64  `json:"useType,optional"`
		Vid          int64  `json:"vid,optional"`
		Page         int64  `json:"page,default=1"`
		PageSize     int64  `json:"pageSize,default=10"`
	}

	SumPlayRecordDetail struct {
		// 日期
		LtsTime  string `json:"ltsTime"`
		PlayType int64  `json:"playType"`

		// 歌曲信息
		SongCode     int64  `json:"songCode"`
		SongName     string `json:"songName"`
		SongDuration int64  `json:"songDuration"`
		CpId         int64  `json:"cpId"`
		CpName       string `json:"cpName"`

		// 客户信息
		Vid          int64  `json:"vid"`
		CustomerName string `json:"customerName"`
		UseType      int64  `json:"useType"`

		// 播放信息
		PlayDuration int64  `json:"playDuration"`
		StartTime    string `json:"startTime"`
		EndTime      string `json:"endTime"`
		PlayBackPos  int64  `json:"playBackPos"`
		PlayNum      int64  `json:"playNum"`
		SceneType    int64  `json:"sceneType"`
		RecordType   int64  `json:"recordType"`
		FreeType     int64  `json:"freeType"`
	}

	SumPlayRecordDetailResp struct {
		Total int64                 `json:"total"`
		List  []SumPlayRecordDetail `json:"list"`
	}

	SumPlayRecordDetailBySongReq struct {
		LtsTime      string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		CustomerName string `json:"customerName,optional"`
		UseType      int64  `json:"useType,optional"`
		SceneType    int64  `json:"sceneType,optional"`
		FreeType     int64  `json:"freeType,optional"`
		PlayType     int64  `json:"playType"`
		Vid          int64  `json:"vid,optional"`
		Page         int64  `json:"page,default=1"`
		PageSize     int64  `json:"pageSize,default=10"`
	}

	SumPlayRecordDetailByVidReq struct {
		LtsTime   string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		SongName  string `json:"songName,optional"`
		SongCode  int64  `json:"songCode,optional"`
		CpId      int64  `json:"cpId,optional"`
		Vid       int64  `json:"vid,optional"`
		UseType   int64  `json:"useType,optional"`
		SceneType int64  `json:"sceneType,optional"`
		FreeType  int64  `json:"freeType,optional"`
		PlayType  int64  `json:"playType"`
		Page      int64  `json:"page,default=1"`
		PageSize  int64  `json:"pageSize,default=10"`
	}

	PlayRecordSongListReq struct {
		LtsTime  string `json:"ltsTime"` // 上次查询时间
		PlayType int    `json:"playType"`
		Vid      int64  `json:"vid"`
		Page     int64  `json:"page,optional"`
		PageSize int64  `json:"pageSize,optional"`
	}
)
