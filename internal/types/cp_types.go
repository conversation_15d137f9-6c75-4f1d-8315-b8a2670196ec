package types

// 版权方相关类型
type (
	CpInfo struct {
		Id         int64  `json:"id"`
		Name       string `json:"name"`
		Remark     string `json:"remark"`
		Status     int64  `json:"status"`
		EndTime    string `json:"endTime"`
		UpdateTime string `json:"updateTime"`
		CreateTime string `json:"createTime"`
	}

	ListCpReq struct {
		Name              string `json:"name,optional"`
		BeginEndTime      string `json:"beginEndTime,optional"`
		EndEndTime        string `json:"endEndTime,optional"`
		Status            int64  `json:"status,default=99"`
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
	}

	ListCpResp struct {
		Total int64    `json:"total"`
		List  []CpInfo `json:"list"`
	}

	GetCpReq struct {
		Id int64 `form:"id"`
	}

	GetCpResp struct {
		Cp CpInfo `json:"cp"`
	}

	UpdateCpStatusReq struct {
		Id     int64 `json:"id"`
		Status int64 `json:"status"`
	}

	AddCpInfo struct {
		Name    string `json:"name"`
		Remark  string `json:"remark,optional"`
		Status  int64  `json:"status,default=0"`
		EndTime string `json:"endTime,optional"`
	}

	AddOrUpdateCpResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	UpdateCpInfo struct {
		Id      int64  `json:"id"`
		Name    string `json:"name"`
		Remark  string `json:"remark,optional"`
		Status  int64  `json:"status,optional"`
		EndTime string `json:"endTime,optional"`
	}
)

const (
	CpClose = iota
	CpOpen
)
