package types

// 操作日志相关类型
type (
	// 查询歌曲操作日志请求
	ListSongOperationLogReq struct {
		SongId   int64 `form:"songId"`
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	// 操作日志信息
	OperationLogInfo struct {
		Id         int64  `json:"id"`
		UserUuid   string `json:"userUuid"`
		Username   string `json:"username"`
		Module     string `json:"module"`
		Operation  string `json:"operation"`
		EntityId   int64  `json:"entityId"`
		EntityName string `json:"entityName"`
		OldValue   string `json:"oldValue,optional"`
		NewValue   string `json:"newValue,optional"`
		Ip         string `json:"ip"`
		UserAgent  string `json:"userAgent"`
		CreateTime string `json:"createTime"`
	}

	// 查询歌曲操作日志响应
	ListSongOperationLogResp struct {
		Total int64              `json:"total"`
		List  []OperationLogInfo `json:"list"`
	}
)

var (
	// 操作日志模块
	LogOperationUpdate = "update"
	LogOperationCreate = "create"
	LogOperationDelete = "delete"
	LogOperationListed = "listed" // 上架

)
