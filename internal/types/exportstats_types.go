package types

// 导出播放记录统计请求
//type ExportPlayRecordStatsReq struct {
//	StartMonth string `json:"startMonth"` // 开始月份，格式: YYYY-MM
//	EndMonth   string `json:"endMonth"`   // 结束月份，格式: YYYY-MM
//}

type ExportPlayRecordStatsReq struct {
	Month        string   `json:"month,optional"`     // 格式：YYYY-MM
	BeginDate    string   `json:"beginDate,optional"` // 格式：YYYY-MM-DD
	EndDate      string   `json:"endDate,optional"`   // 格式：YYYY-MM-DD
	PlayType     int      `json:"playType,optional"`  // 播放类型 1:DRM, 2:明文, 3:副歌
	Vid          int64    `json:"vid,optional"`       // 客户ID
	CustomerName string   `json:"customerName,optional"`
	UseType      int64    `json:"useType,optional"`
	Dates        []string `json:"dates,optional"`       // 日期列表
	ExportType   int      `json:"exportType,default=1"` // 1:按月份导出  2:按日期范围导出  3:按日期导出
}

// 导出播放记录统计响应
type ExportPlayRecordStatsResp struct {
	Success     bool   `json:"success"`     // 是否成功
	Message     string `json:"message"`     // 消息
	FileName    string `json:"fileName"`    // 文件名
	FileSize    int64  `json:"fileSize"`    // 文件大小(字节)
	DownloadUrl string `json:"downloadUrl"` // 下载URL
	RecordCount int    `json:"recordCount"` // 记录数量
}

// 每日播放统计
type DailyPlayStat struct {
	Date         string // 日期，格式: YYYY-MM-DD
	Vid          int64  // 客户ID
	Cid          int64
	PlayCount    int64  // 播放次数
	CustomerName string // 客户名称(从customer表获取)
}

type ExportCustomerInfo struct {
	Cid  int64
	Name string
}

// 时区类型
type TimeZoneType string

const (
	UTC TimeZoneType = "UTC"
	BJ  TimeZoneType = "BJ"
)
