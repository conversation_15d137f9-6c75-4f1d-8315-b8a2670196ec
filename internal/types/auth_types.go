package types

// 认证相关类型
type (
	// 注册请求
	RegisterReq struct {
		Username string `json:"username"`
		Password string `json:"password,optional"`
		Email    string `json:"email,optional"`
		Phone    string `json:"phone,optional"`
		Role     int64  `json:"role,default=1"` // 默认角色为普通用户
		Remark   string `json:"remark,optional"`
	}

	// 注册响应
	RegisterResp struct {
		UserId   int64  `json:"userId"`
		UserUuid string `json:"userUuid"`
		Username string `json:"username"`
		Success  bool   `json:"success"`
		Message  string `json:"message,optional"`
	}

	// 登录请求
	LoginReq struct {
		Phone    string `json:"phone"`
		Password string `json:"password"`
	}

	// 登录响应
	LoginResp struct {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
		UserId       int64  `json:"userId"`
		UserUuid     string `json:"userUuid"` // 新增UUID
		Username     string `json:"username"`
		Role         string `json:"role"`
	}

	// 登出请求
	LogoutReq struct {
	}

	// 登出响应
	LogoutResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 刷新Token请求
	RefreshTokenReq struct {
		RefreshToken string `form:"refreshToken"`
	}

	// 刷新Token响应
	RefreshTokenResp struct {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
	}

	// 用户信息请求
	UserInfoReq struct {
		UserId   int64  `json:"userId,optional"`
		UserUuid string `json:"userUuid,optional"`
	}

	// 用户信息响应
	UserInfoResp struct {
		UserId      int64  `json:"userId"`
		UserUuid    string `json:"userUuid"` // 新增UUID
		Username    string `json:"username"`
		Email       string `json:"email,optional"`
		Phone       string `json:"phone,optional"`
		Role        int64  `json:"role"`
		Permissions string `json:"permissions"`
		Status      int64  `json:"status"`
		LastLogin   int64  `json:"lastLogin,optional"` // 最后登录时间
		Remark      string `json:"remark"`
	}

	// 修改密码请求
	ChangePasswordReq struct {
		UserId      int64  `json:"userId"`
		OldPassword string `json:"oldPassword"`
		NewPassword string `json:"newPassword"`
	}

	ResetPasswordReq struct {
		UserId int64 `json:"userId"`
	}

	// 修改密码响应
	ChangePasswordResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 重置密码响应
	ResetPasswordResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 发送验证码请求
	SendVerificationCodeReq struct {
		Username string `json:"username"`
		Type     string `json:"type"`    // email或phone
		Purpose  string `json:"purpose"` // reset_password, register, etc.
	}

	// 发送验证码响应
	SendVerificationCodeResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 修改用户信息请求
	UpdateUserInfoReq struct {
		UserId   int64  `json:"userId"`
		UserUuid string `json:"userUuid,optional"` // 新增UUID
		Username string `json:"username,optional"`
		Remark   string `json:"remark,optional"`
		Phone    string `json:"phone,optional"`
		Role     int64  `json:"role,optional"`
	}

	// 修改用户信息响应
	UpdateUserInfoResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 查询登录日志请求
	ListLoginLogReq struct {
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	// 登录日志信息
	LoginLogInfo struct {
		Id         int64  `json:"id"`
		UserId     int64  `json:"userId"`
		UserUuid   string `json:"userUuid"`
		Username   string `json:"username"`
		Ip         string `json:"ip"`
		Location   string `json:"location,optional"` // IP地理位置
		UserAgent  string `json:"userAgent"`
		DeviceType string `json:"deviceType,optional"` // 设备类型: web, ios, android
		CreateTime string `json:"createTime"`
	}

	// 查询登录日志响应
	ListLoginLogResp struct {
		Total int64          `json:"total"`
		List  []LoginLogInfo `json:"list"`
	}

	// 验证令牌有效性请求
	ValidateTokenReq struct {
	}

	// 验证令牌有效性响应
	ValidateTokenResp struct {
		Valid    bool   `json:"valid"`
		UserUuid string `json:"userUuid,optional"`
		Message  string `json:"message,optional"`
	}

	// 查询用户列表请求
	ListUserReq struct {
		Username string `json:"username,optional"`
		Phone    string `json:"phone,optional"`
		Role     int64  `json:"role,optional"`
		Page     int64  `json:"page,default=1"`
		PageSize int64  `json:"pageSize,default=10"`
	}

	// 查询用户列表响应
	ListUserResp struct {
		Total int64          `json:"total"`
		List  []UserInfoItem `json:"list"`
	}

	// 用户列表项
	UserInfoItem struct {
		UserId     int64  `json:"userId"`
		UserUuid   string `json:"userUuid"`
		Username   string `json:"username"`
		Nickname   string `json:"nickname"`
		Email      string `json:"email,optional"`
		Phone      string `json:"phone,optional"`
		Role       int64  `json:"role"`
		Status     int64  `json:"status"`
		LastLogin  int64  `json:"lastLogin,optional"`
		Remark     string `json:"remark"`
		CreateTime string `json:"createTime"`
		UpdateTime string `json:"updateTime"`
	}

	// 新增用户
	AddUserInfo struct {
		Username string `json:"username"`
		Phone    string `json:"phone,optional"`
		Role     int64  `json:"role"`
		Remark   string `json:"remark"`
	}

	DelUserInfoResp struct {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	RoleInfoResp struct {
		Role []string `json:"role"`
	}
)

// JWT相关类型定义
type (
	// JWT自定义声明结构
	JwtClaims struct {
		UserId   int64  `json:"userId"`
		UserUuid string `json:"userUuid"` // 新增UUID
		Username string `json:"username"`
		Role     int64  `json:"role"`
	}

	// 用于中间件传递的用户信息
	AuthUser struct {
		UserId   int64
		UserUuid string // 新增UUID
		Username string
		Role     int64
	}
)

// 上下文中用户信息的键
const (
	Authorization     = "Authorization"
	AuthUserKey       = "authUser"
	Token             = "token"
	ErrUserAuthFailed = "用户认证失败"
)

var MapRole = map[int64]string{
	1: "normal",
	2: "admin",
	3: "superAdmin",
}
