package types

type CommonResp struct {
	Status int         `json:"status"`
	Msg    string      `json:"msg"`
	Data   interface{} `json:"data"`
}

const (
	ErrorCode   = 1
	SuccessCode = 0
	FailCode    = 2
)

const (
	ErrFormatTime = "时间解析错误"
)

// 播放量错误
const (
	// 所选时间段内没有数据
	ErrNoDataDuringTheSelectedTime = "所选时间段内没有数据"
	// 未知类型
	ErrNotPlayType = "未知播放类型"

	ErrQuerySumPlayRecord = "播放记录统计失败"

	ErrQuerySongPlayRecord       = "查询歌曲播放记录失败"
	ErrQuerySongPlayRecordDetail = "查询歌曲播放记录详情失败"

	ErrInsertSongName     = "补充歌曲名称失败"
	ErrInsertCpName       = "补充版权方名称失败"
	ErrInsertCustomerName = "补充客户名称失败"

	ErrNotAllowThreeMonthAgo  = "无法查询三个月前的数据"
	ErrNotExportThreeMonthAgo = "无法导出三个月前的数据"
)

// 歌曲错误
const (
	// 未查询到相关歌曲信息
	ErrNoSongInfo = "未查询到相关歌曲信息"

	// 查询歌曲信息失败
	ErrQuerySongInfo = "查询歌曲信息失败"

	// 更新歌曲状态失败
	ErrUpdateSongStatus = "更新歌曲状态失败"

	// 创建歌曲失败
	ErrCreateSong = "创建歌曲失败"

	// 当前榜单已存在相同歌曲
	ErrSongExists = "当前榜单已存在相同歌曲"
)

// 客户
const (
	// 未查询到相关客户信息
	ErrNoCustomerInfo = "未查询到相关客户信息"

	ErrQueryCustomerInfo = "查询客户信息失败"

	ErrUpdateCustomerStatus = "更新客户状态失败"

	ErrUpdateCustomer = "更新客户信息失败"

	ErrCreateCustomer = "创建客户失败"

	ErrCustomerVidNotFound = "查询客户应用vid失败"

	ErrCustomerCidNotFound = "查询客户cid失败"

	ErrCustomerNameExists = "客户名称已存在"
	ErrCustomerCidExists  = "客户cid已存在"
	ErrCustomerVidExists  = "客户vid已存在"

	ErrQueryCustomerPlayRecord       = "查询客户播放记录失败"
	ErrQueryCustomerPlayRecordDetail = "查询客户播放记录详情失败"
)

// 版权方
const (
	// 未查询到相关版权方信息
	ErrNoCpInfo = "未查询到相关版权方信息"
	// 查询版权方信息失败
	ErrQueryCpInfo = "查询版权方信息失败"
	// 更新版权方状态失败
	ErrUpdateCpStatus = "更新版权方状态失败"
	// 创建版权方失败
	ErrCreateCp = "创建版权方失败"
	// 当前状态下不允许设置过期时间
	ErrSetEndTime = "当前状态下不允许设置过期时间"
	// 当前状态下需要设置过期时间
	ErrNeedSetEndTime = "当前状态下需要设置过期时间"

	ErrUpdateCp = "更新版权方失败"

	ErrCpNameExists = "版权方名字已存在"
)

// 榜单
const (
	// 榜单不存在
	ErrBillboardNotFound = "榜单不存在"

	//查询榜单类型失败
	ErrQueryHotType = "查询榜单类型失败"

	ErrHotTypeIsExist = "榜单类型已存在"

	ErrHotTypeNameIsExist = "榜单类型名称已存在"

	ErrHotTypeIsInvalid = "无效的榜单类型"
)

// 用户
const (
	ErrUserNotFound         = "用户不存在"
	ErrOldPasswordIncorrect = "旧密码不正确"
	ErrUpdatePw             = "更新密码失败"
	ErrUpdateUserInfo       = "更新用户信息失败"
	ErrOldPasswordSame      = "新旧密码不能相同"
)

// 权限
const (
	ErrInsertDisposition = "创建权限配置失败"

	ErrDispositionNotFound = "权限配置不存在"

	ErrQueryDisposition = "查询权限配置失败"

	ErrOrderCodeExist = "权限编号已存在"

	ErrOrderCodeNotExist = "权限编号不存在"

	ErrUpdateDisposition

	ErrCloseOrOpen = "更新权限配置状态失败"

	ErrBeforeCloseDispositionDetail = "预处理权限子配置失败"

	ErrRollbackDispositionDetail = "回滚权限子配置失败"

	ErrQueryDispositionDetail = "查询权限子配置失败"

	ErrInsertDispositionDetail = "创建权限子配置失败"

	ErrUpdateDispositionDetail = "更新权限子配置失败"

	ErrDispositionDetailNotFound = "权限子配置不存在"

	ErrQueryDispositionDetailByOrderCode = "查询权限子配置失败"

	ErrDispositionIsClosedCannotOpenDetail = "权限已关闭，无法开启子配置"
)

// 文件上传
const (
	ErrReadFile        = "文件解析失败"
	ErrSizeFile        = "文件大小超过限制"
	ErrSaveFileToLocal = "文件本地保存失败"
	ErrCreateDir       = "创建文件目录失败"
)

const (
	ErrLtsTime  = "时间参数缺失"
	ErrPlayType = "格式参数缺失"
)
