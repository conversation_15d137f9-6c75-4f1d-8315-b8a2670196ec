package types

// 权限配置相关类型
type (
	DispositionInfo struct {
		Id         int64  `json:"id,optional"`
		OrderCode  string `json:"orderCode"`
		Vid        int64  `json:"vid"`
		Remark     string `json:"remark,optional"`
		Status     int64  `json:"status,default=0,optional"`
		CreateTime string `json:"createTime,optional"`
		UpdateTime string `json:"updateTime,optional"`
	}

	DispositionDetailInfo struct {
		Id            int64  `json:"id,optional"`
		OrderCode     string `json:"orderCode"`
		Vid           int64  `json:"vid,default=0,optional"`
		ResourceTypes string `json:"resourceTypes,optional"`
		CpId          int64  `json:"cpId,default=1"`
		CpName        string `json:"cpName,optional"`
		BillingType   int64  `json:"billingType,default=2"`
		EndTime       string `json:"endTime,optional"`
		Remark        string `json:"remark,optional"`
		Status        int64  `json:"status,default=1"`
		CreateTime    string `json:"createTime,optional"`
		UpdateTime    string `json:"updateTime,optional"`
	}

	ListDispositionByVidReq struct {
		Vid      int64 `json:"vid,optional"`
		Page     int64 `json:"page,default=1"`
		PageSize int64 `json:"pageSize,default=10"`
	}

	ListDispositionResp struct {
		Total int64             `json:"total"`
		List  []DispositionInfo `json:"list"`
	}

	ListDispositionReq struct {
		Vid               int64  `json:"vid,optional"`
		OrderCode         string `json:"orderCode,optional"`
		Status            int64  `json:"status,default=99"`
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
	}

	GetValidDispositionDetailReq struct {
		Vid int64 `json:"vid"`
	}

	DispositionCommonResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	CloseOrOpenDispositionCommonReq struct {
		Id     int64 `json:"id"`
		Status int64 `json:"status"`
	}

	ListDispositionDetailReq struct {
		OrderCode         string `json:"orderCode,optional"`
		CpId              int64  `json:"cpId,optional"`
		Status            int64  `json:"status,default=99"`
		Page              int64  `json:"page,default=1"`
		PageSize          int64  `json:"pageSize,default=10"`
		OrderByCreateTime string `json:"orderByCreateTime,optional"` // 1:升序，2:降序
		OrderByUpdateTime string `json:"orderByUpdateTime,optional"` // 1:升序，2:降序
	}

	ListDispositionDetailResp struct {
		Total int64                   `json:"total"`
		List  []DispositionDetailInfo `json:"list"`
	}
)

const (
	DispositionOrDispositionDetailClose = iota
	DispositionOrDispositionDetailOpen
)
