package types

type AssociationPlayRecord struct {
	RequestUuid               string `json:"requestUuid"`               // 请求唯一 ID  uniqueId
	SongCode                  int64  `json:"songCode"`                  // 歌曲编号-音集协 songCode
	SongLibrary               int64  `json:"songLibrary"`               // 曲库 ID  不传
	SongName                  string `json:"songName"`                  // 歌曲名 song 表中的 name  从 song 查
	SongWordAuthor            string `json:"songWordAuthor"`            // 歌曲词作者
	SongTuneAuthor            string `json:"songTuneAuthor"`            // 歌曲曲作者
	SongSinger                string `json:"songSinger"`                // 演唱者 song 表中的 singer 从 song 查
	PlatId                    int64  `json:"platId"`                    // 调用平台唯一标识  传默认值 1
	PlatName                  string `json:"platName"`                  // 调用平台名称 传默认值 1
	ClientId                  int64  `json:"clientId"`                  // 客户端唯一标识（平台客户）  不传
	ClientName                string `json:"clientName"`                // 客户端名称（平台客户）
	RoomType                  int64  `json:"roomType"`                  // 房间类型  1-秀场直播  2-语聊房 3-VR  不传，用 sceneType 字段替代
	RoomId                    string `json:"roomId"`                    // 房间唯一标识（客户端内） 不传，有隐私风险
	RoomName                  string `json:"roomName"`                  // 房间名称（客户端内） 不传，有隐私风险
	RoomMasterId              string `json:"roomMasterId"`              // 房间主人 ID（主播） 不传，有隐私风险
	RoomMasterName            string `json:"roomMasterName"`            // 房间主任名称（主播） 不传，有隐私风险
	AnchorOrPlatform          int64  `json:"anchorOrPlatform"`          // 主播调用还是平台调用  1-主播调用 2-平台调用  传默认值 2，目前没有 1 的业务模式
	MonthlyGear               int64  `json:"monthlyGear"`               // 包月档位 主播调用时为必填  1-按次调用 2-按时长调用  传默认值 1，目前没有 2的业务模式，后续也不会有
	PlayBeginTime             string `json:"playBeginTime"`             // 播放开始时间  2024/5/13 10:38:00 startTime转成 UTF+8 传给音集协
	PlayDuration              int64  `json:"playDuration"`              // 播放时长  playDuration
	PlayMode                  int64  `json:"playMode"`                  // 播放模式  播放模式 1.原唱；2.伴唱；3.原唱+伴唱  默认传 1
	IsClip                    int64  `json:"isClip"`                    // 是否是 clip 片段 0 不传，对计量计费没用处
	IsDrm                     int64  `json:"isDrm"`                     // 是否是 DRM 0 不传，对计量计费没用处
	OriginalPlayDuration      int64  `json:"originalPlayDuration"`      // 原唱时长  songDuration
	AccompanimentPlayDuration int64  `json:"accompanimentPlayDuration"` // 伴唱时长  不传，无论是原唱还是伴唱，歌曲时长都用originalPlayDuration字段
	IsCallSuccess             int64  `json:"isCallSuccess"`             // 是否调用成功 1.成功；2.失败   传默认值 1，目前计量传过去的都是成功过的
	Status                    int64  `json:"status"`                    // 状态 1.有效；2.无效  传默认值 1，计量传过去都是有效的
	CallTime                  string `json:"callTime"`                  // 调用时间 2024/5/13 10:38:00 startTime转成 UTF+8 传给音集协
	IsFirstThirtySeconds      int64  `json:"isFirstThirtySeconds"`      // 是否是前30秒 是否是前 30s 0.不是；1.是 传默认值 0，不传，这个没有用
	EffectiveUseNum           int64  `json:"effectiveUseNum"`           // 计费次数（账单统计以此为准）playNum  本次调用的有效次数，例如播放多次都根据计量规则算好了是多次 满足免费策略，例如全曲播放 16s 免费，该字段自动就是 0 省去了 freeType 字段，如果 playNum=0 那就是一次免费调用
	MusicUserType             int64  `json:"musicUserType"`             // 音乐使用类型 1-K歌 2-背景音乐 3-副歌 不传 用 sceneType 字段替代
	Cid                       int64  `json:"cid"`                       // 客户 id cid
	Vid                       int64  `json:"vid"`                       // 客户项目 id vid  VID做唯一标识，不用 Client Name
	CompanyName               string `json:"companyName"`               // 客户名称-声网客户名称全称 companyName
	PlayBeginTimeUTC          string `json:"playBeginTime-UTC"`         // 播放开始时间-UTC 时间  2024/5/13 2:38:00  startTime 留这个字段方便后续对账
	SceneType                 int64  `json:"sceneType"`                 // 场景类型 sceneType
}

/*
roomType 为 2 并且 musicUserType 为 1 ===== 1.语聊-全曲场景
roomType 为 2 并且 musicUserType 为 3 ===== 2.语聊-片段场景
roomType 为 3 并且 musicUserType 为 1 ===== 3.语聊-VR场景
roomType 为 2 并且 musicUserType 为 2 ===== 4.秀场-背景音乐场景
roomType 为 1 并且 musicUserType 为 1 ===== 5.秀场-K歌场景
*/
