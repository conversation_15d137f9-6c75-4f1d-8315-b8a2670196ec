package service

import (
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"music/internal/config"
)

type YjxOssClient struct {
	OssClient       *oss.Client
	Bucket          *oss.Bucket
	PlatformOssPath string
	AnchorOssPath   string
	LicenseOssPath  string
}

func NewYjxOssClient(conf *config.Config) (*YjxOssClient, error) {
	// 创建OSS客户端
	client, err := oss.New(conf.YjxOss.EndPoint, conf.YjxOss.AccessKeyId, conf.YjxOss.AccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(conf.YjxOss.BucketName)

	if err != nil {
		return nil, fmt.Errorf("获取存储桶失败: %w", err)
	}

	return &YjxOssClient{
		OssClient:       client,
		Bucket:          bucket,
		PlatformOssPath: conf.YjxOss.PlatformOssPath,
		AnchorOssPath:   conf.YjxOss.AnchorOssPath,
		LicenseOssPath:  conf.YjxOss.LicenseOssPath,
	}, nil
}

func (c *YjxOssClient) UploadFile(filePath, ossPath string) error {
	// 上传文件到OSS
	err := c.Bucket.PutObjectFromFile(ossPath, filePath)
	if err != nil {
		return fmt.Errorf("上传文件到OSS失败: %w", err)
	}
	return nil
}
