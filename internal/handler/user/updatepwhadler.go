package user

import (
	"music/internal/logic/user"
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/svc"
	"music/internal/types"
)

func UpdatePwHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ChangePasswordReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewUpdateUserPwLogic(r.Context(), svcCtx)
		resp, err := l.UpdatePw(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
