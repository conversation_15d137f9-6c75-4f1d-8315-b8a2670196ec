package user

import (
	"music/internal/logic/user"
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/svc"
	"music/internal/types"
)

func DelUserHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UserInfoReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := user.NewDelUserLogic(r.Context(), svcCtx)
		resp, err := l.Del<PERSON>ser(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
