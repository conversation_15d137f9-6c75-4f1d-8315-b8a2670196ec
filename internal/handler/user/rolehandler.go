package user

import (
	"music/internal/logic/user"
	"music/internal/middleware"
	"net/http"

	"music/internal/svc"
)

func RoleInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		//var req types.UserInfoReq
		//if err := httpx.Parse(r, &req); err != nil {
		//	httpx.ErrorCtx(r.Context(), w, err)
		//	return
		//}

		l := user.NewRoleInfoLogic(r.Context(), svcCtx)
		resp, err := l.RoleInfo()
		middleware.Response(r.Context(), w, resp, err)
	}
}
