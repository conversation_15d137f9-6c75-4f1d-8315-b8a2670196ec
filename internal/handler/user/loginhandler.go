package user

import (
	"context"
	"music/internal/logic/user"
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/svc"
	"music/internal/types"
)

func LoginHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.LoginReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		// 将IP和UA信息放入上下文
		ctx := context.WithValue(r.Context(), "remoteAddr", r.RemoteAddr)
		ctx = context.WithValue(ctx, "userAgent", r.UserAgent())

		l := user.NewLoginLogic(ctx, svcCtx)
		resp, err := l.Login(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
