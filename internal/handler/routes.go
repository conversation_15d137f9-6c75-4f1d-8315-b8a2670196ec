package handler

import (
	"music/internal/handler/dispositionDetail"
	"music/internal/handler/file"
	"music/internal/handler/hot"
	"music/internal/handler/hotType"
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"music/internal/handler/cp"
	"music/internal/handler/customer"
	"music/internal/handler/disposition"
	"music/internal/handler/playrecord"
	"music/internal/handler/song"
	"music/internal/handler/user"
	"music/internal/svc"
)

// RegisterHandlers 注册API路由处理函数
func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	// 健康检查接口 - 无需认证
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/health",
			Handler: HealthHandler,
		},
	)

	// 认证相关路由
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/auth/login",
				Handler: user.LoginHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/auth/register",
				Handler: user.RegisterHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/auth/logout",
					Handler: user.LogoutHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/api/auth/user/info",
					Handler: user.InfoHandler(serverCtx),
				},
			}...,
		),
	)

	// 用户管理相关路由
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/users",
					Handler: user.ListUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/users/add",
					Handler: user.AddUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/user/update/pw",
					Handler: user.UpdatePwHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/user/update/info",
					Handler: user.UpdateUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/user/role",
					Handler: user.RoleInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/user/del",
					Handler: user.DelUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/user/reset/pw",
					Handler: user.ResetPwHandler(serverCtx),
				},
			}...,
		),
	)

	// 歌曲相关路由
	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/songs",
					Handler: song.ListSongsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/songs/listed",
					Handler: song.ListedSongsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/song/add",
					Handler: song.AddSongHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/song/update",
					Handler: song.UpdateSongHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/song/display-columns",
					Handler: song.SetSongDisplayColumnsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/api/song/operation-logs",
					Handler: song.ListSongOperationLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/song/add-hot",
					Handler: song.ListSongForHotHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/song/playrecord/list",
					Handler: song.ListSongForPlayRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/song/import",
					Handler: song.ImportSongsHandler(serverCtx),
				},
			}...,
		),
	)

	// 榜单相关路由
	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/types",
					Handler: hotType.ListHotTypesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/type/add",
					Handler: hotType.AddHotTypesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/type/del",
					Handler: hotType.DelHotTypesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/type/update",
					Handler: hotType.UpdateHotTypesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/type/status",
					Handler: hotType.CloseOrOpenHotTypesHandler(serverCtx),
				},
			}...,
		),
	)

	// 热歌相关路由
	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/songs",
					Handler: hot.ListHotSongsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/song/add",
					Handler: hot.AddHotSongsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/song/del",
					Handler: hot.DelHotSongsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/song/update/num",
					Handler: hot.UpdateHotSongNumHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/song/update",
					Handler: hot.UpdateHotSongsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/song/nums",
					Handler: hot.ListHotSongsNumHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/hot/song/num/check",
					Handler: hot.CheckHotSongsNumHandler(serverCtx),
				},
			}...,
		),
	)

	// 版权方相关路由
	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/cps",
					Handler: cp.ListCpsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/api/cp",
					Handler: cp.GetCpHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/cp/update-status",
					Handler: cp.UpdateCpStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/cp/update",
					Handler: cp.UpdateCpHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/cp/add",
					Handler: cp.AddCpHandler(serverCtx),
				},
			}...,
		),
	)

	// 客户相关路由
	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/customers",
					Handler: customer.ListCustomersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/customer/update-status",
					Handler: customer.UpdateCustomerStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/customer/update",
					Handler: customer.UpdateCustomerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/customer/add",
					Handler: customer.AddCustomerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/customer/vids",
					Handler: customer.ListVidHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/customer/cids",
					Handler: customer.ListCidHandler(serverCtx),
				},
			}...,
		),
	)

	// 权限配置相关路由
	server.AddRoutes(

		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/list",
					Handler: disposition.ListDispositionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/status",
					Handler: disposition.CloseDispositionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/add",
					Handler: disposition.AddDispositionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/update",
					Handler: disposition.UpdateDispositionHandler(serverCtx),
				},

				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/detail/list",
					Handler: dispositionDetail.ListDispositionDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/detail/status",
					Handler: dispositionDetail.CloseDispositionDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/detail/add",
					Handler: dispositionDetail.AddDispositionDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/disposition/detail/update",
					Handler: dispositionDetail.UpdateDispositionDetailHandler(serverCtx),
				},
			}...,
		),
	)

	// 播放记录相关路由
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecords/lts",
					Handler: playrecord.ListPlayRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecord/lts/customer",
					Handler: playrecord.ListCustomerPlayRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecord/lts/customer/detail",
					Handler: playrecord.ListCustomerPlayRecordDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecord/lts/song",
					Handler: playrecord.ListSongPlayRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecord/lts/song/detail",
					Handler: playrecord.ListSongPlayRecordDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecord/lts/song/list",
					Handler: playrecord.ListSongForPlayRecordHandler(serverCtx),
				},

				// 导出
				{
					Method:  http.MethodPost,
					Path:    "/api/playrecord/export_stats",
					Handler: playrecord.ExportPlayRecordStatsHandler(serverCtx),
				},
			}...,
		),
	)

	server.AddRoutes(
		//rest.WithMiddlewares(
		//[]rest.Middleware{serverCtx.Auth},
		[]rest.Route{
			// 下载
			{
				Method:  http.MethodGet,
				Path:    "/api/file/download",
				Handler: file.DownloadHandler,
			},
		},
		//),
	)
}
