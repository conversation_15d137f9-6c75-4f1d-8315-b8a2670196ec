package hotType

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hotType"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func ListHotTypesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListHotTypesReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hotType.NewListHotTypesLogic(r.Context(), svcCtx)
		resp, err := l.ListHotTypes(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
