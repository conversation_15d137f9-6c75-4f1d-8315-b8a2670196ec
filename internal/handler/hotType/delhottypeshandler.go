package hotType

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hotType"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func DelHotTypesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.HotTypeInfo
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hotType.NewDelHotTypesLogic(r.Context(), svcCtx)
		err := l.DelHotTypes(&req)
		middleware.Response(r.Context(), w, nil, err)
	}
}
