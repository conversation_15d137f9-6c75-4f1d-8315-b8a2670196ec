package hotType

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hotType"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func UpdateHotTypesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateHotType
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hotType.NewUpdateHotTypesLogic(r.Context(), svcCtx)
		resp, err := l.UpdateHotTypes(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
