package hotType

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hotType"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func AddHotTypesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddHotType
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hotType.NewAddHotTypesLogic(r.Context(), svcCtx)
		resp, err := l.AddHotTypes(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
