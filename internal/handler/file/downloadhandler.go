package file

import (
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
)

func DownloadHandler(w http.ResponseWriter, r *http.Request) {
	// 确保 exports 目录存在
	exportDir := "./file/exports"
	if err := os.Mkdir<PERSON>ll(exportDir, 0755); err != nil {
		http.Error(w, "创建导出目录失败", http.StatusInternalServerError)
		return
	}

	encodedFilename := r.URL.Query().Get("file")

	filename, err := url.QueryUnescape(encodedFilename)
	if err != nil {
		http.Error(w, "文件名解码失败", http.StatusBadRequest)
		return
	}

	fmt.Println("encodedFilename", encodedFilename)

	if filename == "" {
		http.Error(w, "文件名不能为空", http.StatusBadRequest)
		return
	}

	// 安全检查：防止目录遍历
	if strings.Contains(filename, "..") {
		http.Error(w, "非法的文件名", http.StatusBadRequest)
		return
	}

	// 文件路径
	filePath := filepath.Join(exportDir, filename)
	filePath = filepath.Clean(filePath)

	fmt.Println("filePath", filePath)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "文件不存在", http.StatusNotFound)
		return
	}

	// 设置响应头
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	// 提供文件下载
	http.ServeFile(w, r, filePath)
}
