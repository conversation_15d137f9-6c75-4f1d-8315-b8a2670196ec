package cp

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/cp"
	"music/internal/svc"
	"music/internal/types"
)

func GetCpHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetCpReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := cp.NewGetCpLogic(r.Context(), svcCtx)
		resp, err := l.GetCp(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
