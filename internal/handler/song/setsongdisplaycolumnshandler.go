package song

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/song"
	"music/internal/svc"
	"music/internal/types"
)

func SetSongDisplayColumnsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SetSongDisplayColumnsReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := song.NewSetSongDisplayColumnsLogic(r.Context(), svcCtx)
		resp, err := l.SetSongDisplayColumns(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
