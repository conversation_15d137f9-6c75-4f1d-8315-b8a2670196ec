package song

import (
	"music/internal/logic/song"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
	"net/http"
)

func ImportSongsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ImportReq
		/*if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}*/

		l := song.NewImportSongLogic(r.Context(), svcCtx, r)
		resp, err := l.ImportSong(&req)
		middleware.ImportResponse(r.Context(), w, resp, err)
	}
}
