package song

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/song"
	"music/internal/svc"
	"music/internal/types"
)

func ListSongForHotHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListSongForHotReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := song.NewListSongForHotLogic(r.Context(), svcCtx)
		resp, err := l.ListSongsForHot(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
