package playrecord

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/playrecord"
	"music/internal/svc"
	"music/internal/types"
)

func ListCustomerPlayRecordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SumPlayRecordByVidReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := playrecord.NewListPlayRecordGroupByVidLogic(r.Context(), svcCtx)
		resp, err := l.ListPlayRecordGroupByVid(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
