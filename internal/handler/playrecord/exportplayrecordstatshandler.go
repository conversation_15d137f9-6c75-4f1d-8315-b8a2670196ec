package playrecord

import (
	"music/internal/middleware"
	"net/http"

	"music/internal/logic/playrecord"
	"music/internal/svc"
	"music/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ExportPlayRecordStatsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ExportPlayRecordStatsReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.Error(w, err)
			return
		}

		l := playrecord.NewExportPlayRecordStatsLogic(r.Context(), svcCtx)
		//resp, err := l.ExportPlayRecordStats(&req)
		resp, err := l.ExportPlayRecordStatsV2(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
