package playrecord

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/playrecord"
	"music/internal/svc"
	"music/internal/types"
)

func CreatePlayRecordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreatePlayRecordReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		// 从请求中获取IP信息
		if req.RequestIp == "" {
			req.RequestIp = r.RemoteAddr
		}

		l := playrecord.NewCreatePlayRecordLogic(r.Context(), svcCtx)
		resp, err := l.CreatePlayRecord(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
