package hot

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hot"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func AddHotSongsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddHotSongInfo
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hot.NewAddHotSongsLogic(r.Context(), svcCtx)
		resp, err := l.AddHotSongs(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
