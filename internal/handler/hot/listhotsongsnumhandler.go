package hot

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hot"
	"music/internal/svc"
	"music/internal/types"
)

func ListHotSongsNumHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListHotSongsNumReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hot.NewListHotSongsNumLogic(r.Context(), svcCtx)
		resp, err := l.ListHotSongsNum(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
