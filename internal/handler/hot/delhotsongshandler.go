package hot

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hot"
	"music/internal/svc"
	"music/internal/types"
)

func DelHotSongsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DelHotSong
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hot.NewDelHotSongsLogic(r.Context(), svcCtx)
		err := l.DelHotSongs(&req)
		middleware.Response(r.Context(), w, nil, err)
	}
}
