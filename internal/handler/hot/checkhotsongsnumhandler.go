package hot

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/hot"
	"music/internal/svc"
	"music/internal/types"
)

func CheckHotSongsNumHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CheckHotSongNum
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := hot.NewCheckHotSongsLogic(r.Context(), svcCtx)
		resp, err := l.CheckHotSongNum(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
