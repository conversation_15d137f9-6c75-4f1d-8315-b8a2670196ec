package handler

import (
	"music/internal/middleware"
	"net/http"
	"time"
)

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string `json:"status"`
	Version   string `json:"version"`
	Timestamp string `json:"timestamp"`
}

// HealthHandler 处理健康检查请求
func HealthHandler(w http.ResponseWriter, r *http.Request) {
	resp := &HealthResponse{
		Status:    "ok",
		Version:   "1.0.0",
		Timestamp: time.Now().Format(time.RFC3339),
	}
	middleware.Response(r.Context(), w, resp, nil)
}

// 在routes.go中添加如下行来注册此处理函数:
// server.AddRoute(
//   rest.Route{
//     Method:  http.MethodGet,
//     Path:    "/api/health",
//     Handler: HealthHandler,
//   },
// )
