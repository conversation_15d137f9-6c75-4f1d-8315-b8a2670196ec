package customer

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/customer"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func UpdateCustomerHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CustomerInfo
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := customer.NewUpdateCustomerLogic(r.Context(), svcCtx)
		resp, err := l.UpdateCustomerLogic(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
