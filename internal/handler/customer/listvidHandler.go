package customer

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/customer"
	"music/internal/svc"
	"music/internal/types"
)

func ListVidHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.VidOrCidReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := customer.NewListVidLogic(r.Context(), svcCtx)
		resp, err := l.ListVid(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
