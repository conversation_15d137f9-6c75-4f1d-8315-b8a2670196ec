package customer

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/customer"
	"music/internal/svc"
	"music/internal/types"
)

func UpdateCustomerStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateCustomerStatusReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := customer.NewUpdateCustomerStatusLogic(r.Context(), svcCtx)
		resp, err := l.UpdateCustomerStatusLogic(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
