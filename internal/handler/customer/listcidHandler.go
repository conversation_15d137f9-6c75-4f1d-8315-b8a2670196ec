package customer

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/customer"
	"music/internal/svc"
	"music/internal/types"
)

func ListCidHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.VidOrCidReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := customer.NewListCidLogic(r.Context(), svcCtx)
		resp, err := l.ListCid(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
