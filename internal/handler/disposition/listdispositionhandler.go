package disposition

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/disposition"
	"music/internal/svc"
	"music/internal/types"
)

func ListDispositionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListDispositionReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := disposition.NewListDispositionLogic(r.Context(), svcCtx)
		resp, err := l.ListDisposition(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
