package disposition

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/disposition"
	"music/internal/svc"
	"music/internal/types"
)

func AddDispositionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DispositionInfo
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := disposition.NewAddDispositionLogic(r.Context(), svcCtx)
		resp, err := l.AddDisposition(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
