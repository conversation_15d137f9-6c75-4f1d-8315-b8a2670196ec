package disposition

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"music/internal/logic/disposition"
	"music/internal/svc"
	"music/internal/types"
)

func CloseDispositionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CloseOrOpenDispositionCommonReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := disposition.NewCloseOrOpenDispositionLogic(r.Context(), svcCtx)
		resp, err := l.CloseOrOpenDisposition(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
