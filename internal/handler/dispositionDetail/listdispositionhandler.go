package dispositionDetail

import (
	"music/internal/middleware"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	dispositionDetail "music/internal/logic/dispositionDetail"
	"music/internal/svc"
	"music/internal/types"
)

func ListDispositionDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListDispositionDetailReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := dispositionDetail.NewListDispositionDetailLogic(r.Context(), svcCtx)
		resp, err := l.ListDispositionDetail(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
