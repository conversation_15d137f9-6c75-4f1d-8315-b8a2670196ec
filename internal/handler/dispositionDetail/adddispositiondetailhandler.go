package dispositionDetail

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	dispositionDetail "music/internal/logic/dispositionDetail"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
)

func AddDispositionDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DispositionDetailInfo
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}

		l := dispositionDetail.NewAddDispositionDetailLogic(r.Context(), svcCtx)
		resp, err := l.AddDispositionDetail(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}
