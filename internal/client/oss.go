package client

import (
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"music/internal/config"
	"time"
)

type YjxOssClient struct {
	OssClient       *oss.Client
	Bucket          *oss.Bucket
	PlatformOssPath string
	AnchorOssPath   string
	LicenseOssPath  string
}

func NewYjxOssClient(conf *config.Config) (*YjxOssClient, error) {
	// 创建OSS客户端
	client, err := oss.New(conf.YjxOss.EndPoint, conf.YjxOss.AccessKeyId, conf.YjxOss.AccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(conf.YjxOss.BucketName)

	if err != nil {
		return nil, fmt.Errorf("获取存储桶失败: %w", err)
	}

	return &YjxOssClient{
		OssClient:       client,
		Bucket:          bucket,
		PlatformOssPath: conf.YjxOss.PlatformOssPath,
		AnchorOssPath:   conf.YjxOss.AnchorOssPath,
		LicenseOssPath:  conf.YjxOss.LicenseOssPath,
	}, nil
}

func (c *YjxOssClient) UploadFile(filePath, ossPath string) error {
	// 上传文件到OSS
	err := c.Bucket.PutObjectFromFile(ossPath, filePath)
	if err != nil {
		return fmt.Errorf("上传文件到OSS失败: %w", err)
	}
	return nil
}

// UploadFileToPlatform 上传文件到平台目录
func (c *YjxOssClient) UploadFileToplatform(localFilePath, fileName string) (string, error) {
	// 构建OSS路径，使用正斜杠
	ossPath := c.PlatformOssPath + fileName

	// 上传文件
	err := c.UploadFile(localFilePath, ossPath)
	if err != nil {
		return "", err
	}

	return ossPath, nil
}

// GeneratePresignedURL 生成预签名URL用于下载
func (c *YjxOssClient) GeneratePresignedURL(ossPath string, expireTime time.Duration) (string, error) {
	// 生成预签名URL，默认1小时过期
	signedURL, err := c.Bucket.SignURL(ossPath, oss.HTTPGet, int64(expireTime.Seconds()))
	if err != nil {
		return "", fmt.Errorf("生成预签名URL失败: %w", err)
	}
	return signedURL, nil
}

// GetPublicURL 获取公共访问URL（如果bucket是公共读的话）
func (c *YjxOssClient) GetPublicURL(ossPath string) string {
	// 构建公共访问URL
	// 格式: https://{bucket}.{endpoint}/{object}
	endpoint := c.OssClient.Config.Endpoint
	bucketName := c.Bucket.BucketName
	return fmt.Sprintf("https://%s.%s/%s", bucketName, endpoint, ossPath)
}
