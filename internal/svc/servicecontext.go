package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
	"music/internal/middleware"
	"music/internal/client"

	"music/internal/config"
	"music/model/cp"
	"music/model/customer"
	"music/model/disposition"
	"music/model/hot"
	"music/model/log"
	"music/model/playrecord"
	"music/model/song"
	"music/model/user"
)

type ServiceContext struct {
	Config                  config.Config
	Auth                    rest.Middleware
	Redis                   *redis.Redis
	OssClient               *client.YjxOssClient
	UserModel               user.UserModel
	UserLoginLogModel       user.UserLoginLogModel
	UserDisplaySettingModel user.UserDisplaySettingModel
	SongModel               song.SongModel
	SongHotModel            hot.SongHotModel
	SongHotTypeModel        hot.SongHotTypeModel
	CpModel                 cp.CpModel
	CustomerModel           customer.CustomerModel
	DispositionModel        disposition.DispositionModel
	DispositionDetailModel  disposition.DispositionDetailModel
	OperationLogModel       log.OperationLogModel
	PlayRecordModel         playrecord.PlayRecordModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	connOut := sqlx.NewMysql(c.MysqlOut.DataSource)
	connIn := sqlx.NewMysql(c.MysqlIn.DataSource)

	// 使用Redis客户端
	rds := redis.MustNewRedis(c.Redis)

	// 初始化OSS客户端
	ossClient, err := client.NewYjxOssClient(&c)
	if err != nil {
		panic("初始化OSS客户端失败: " + err.Error())
	}

	return &ServiceContext{
		Config:                  c,
		Auth:                    middleware.NewAuthMiddleware(c.Auth.AccessSecret, rds).Handle,
		//Auth:                    middleware.NewAuthMiddleware(c.Auth.AccessSecret).Handle,
		Redis:                   rds,
		OssClient:               ossClient,
		UserModel:               user.NewUserModel(connIn),
		UserLoginLogModel:       user.NewUserLoginLogModel(connIn),
		UserDisplaySettingModel: user.NewUserDisplaySettingModel(connIn),
		SongModel:               song.NewSongModel(connOut),
		SongHotModel:            hot.NewSongHotModel(connOut),
		SongHotTypeModel:        hot.NewSongHotTypeModel(connOut),
		CpModel:                 cp.NewCpModel(connOut),
		CustomerModel:           customer.NewCustomerModel(connOut),
		DispositionModel:        disposition.NewDispositionModel(connOut),
		DispositionDetailModel:  disposition.NewDispositionDetailModel(connOut),
		OperationLogModel:       log.NewOperationLogModel(connIn),
		PlayRecordModel:         playrecord.NewPlayRecordModel(connIn),
	}
}
