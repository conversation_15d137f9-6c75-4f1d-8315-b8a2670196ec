version: '3'
services:
  jenkins:
    image: jenkins/jenkins:2.426.3-lts-jdk11    # 更新到指定版本
    container_name: jenkins
    restart: always
    user: root
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      - /data/jenkins_home:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
    environment:
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=true