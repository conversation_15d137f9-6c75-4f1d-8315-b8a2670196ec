pipeline {
    agent any
    
    environment {
        // Gitee 配置
        GITEE_CREDS = 'gitee-ssh-key'  // Jenkins 凭据 ID
        BACKEND_REPO = '*************:music_15/music.git'
        FRONTEND_REPO = '*************:music_15/music-vue.git'
        
        // 阿里云配置
        ALIYUN_REGISTRY = 'registry.cn-guangzhou.aliyuncs.com'
        ALIYUN_NAMESPACE = 'music-basic'
        DOCKER_REGISTRY_CREDS = 'aliyun-docker-registry'  // Jenkins 凭据 ID
        
        // 版本信息
        VERSION = sh(script: 'date +%Y%m%d_%H%M%S', returnStdout: true).trim()
        PREVIOUS_VERSION = ''
    }

    stages {
        stage('Checkout') {
            parallel {
                stage('Backend Code') {
                    steps {
                        dir('backend') {
                            git(
                                url: "${BACKEND_REPO}",
                                branch: 'master',
                                credentialsId: "${GITEE_CREDS}"
                            )
                        }
                    }
                }
                stage('Frontend Code') {
                    steps {
                        dir('frontend') {
                            git(
                                url: "${FRONTEND_REPO}",
                                branch: 'master',
                                credentialsId: "${GITEE_CREDS}"
                            )
                        }
                    }
                }
            }
        }

        stage('Prepare') {
            steps {
                script {
                    // 登录阿里云容器镜像服务
                    withCredentials([usernamePassword(
                        credentialsId: "${DOCKER_REGISTRY_CREDS}",
                        usernameVariable: 'DOCKER_USER',
                        passwordVariable: 'DOCKER_PASSWORD'
                    )]) {
                        sh """
                            docker login ${ALIYUN_REGISTRY} \
                                -u ${DOCKER_USER} \
                                -p ${DOCKER_PASSWORD}
                        """
                    }
                    
                    // 获取当前运行版本用于回滚
                    PREVIOUS_VERSION = sh(
                        script: "docker ps | grep music-api | awk '{print \$2}' | cut -d':' -f2",
                        returnStdout: true
                    ).trim()
                }
            }
        }

        stage('Backup') {
            steps {
                // 执行备份
                sh '''
                    # 设置备份时间戳
                    BACKUP_TIME=$(date +%Y%m%d_%H%M%S)
                    BACKUP_DIR="/data/music/backup/${BACKUP_TIME}"
                    
                    # 创建备份目录
                    mkdir -p ${BACKUP_DIR}

                    # 备份上传文件
                    tar -czf "${BACKUP_DIR}/uploads.tar.gz" /data/music/upload
                    
                    # 备份配置文件
                    cp -r /etc/music/config "${BACKUP_DIR}/config"
                    
                    # 压缩备份
                    cd /data/music/backup
                    tar -czf "backup_${BACKUP_TIME}.tar.gz" "${BACKUP_TIME}"
                    rm -rf "${BACKUP_TIME}"
                    
                    # 清理30天前的备份
                    find /data/music/backup -name "backup_*.tar.gz" -mtime +30 -delete
                '''
            }
        }

        stage('Build') {
            parallel {
                stage('Backend Build') {
                    steps {
                        dir('backend') {
                            sh """
                                # 构建后端镜像
                                docker build \
                                    --build-arg PROMETHEUS_ENABLED=true \
                                    -t ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/music-api:${VERSION} \
                                    -f deploy/backend/docker/Dockerfile .
                                
                                # 推送镜像到阿里云
                                docker push ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/music-api:${VERSION}
                            """
                        }
                    }
                }
                
                stage('Frontend Build') {
                    steps {
                        dir('frontend') {
                            sh """
                                # 构建前端镜像
                                docker build \
                                    -t ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/music-web:${VERSION} \
                                    -f deploy/frontend/docker/Dockerfile .
                                
                                # 推送镜像到阿里云
                                docker push ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/music-web:${VERSION}
                            """
                        }
                    }
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    try {
                        // 更新环境变量文件
                        sh """
                            cat > deploy/backend/docker/.env << EOF
                            VERSION=${VERSION}
                            ALIYUN_REGISTRY=${ALIYUN_REGISTRY}
                            ALIYUN_NAMESPACE=${ALIYUN_NAMESPACE}
                            EOF
                        """
                        
                        // 部署服务
                        sh """
                            cd deploy/backend/docker
                            docker-compose down
                            docker-compose up -d
                            
                            cd ../../frontend/docker
                            docker-compose down
                            docker-compose up -d
                        """
                        
                        // 等待服务启动
                        sleep 30
                        
                        // 健康检查
                        sh '''
                            # 检查后端健康状态
                            if ! curl -sf http://localhost:8888/api/health; then
                                echo "Backend health check failed"
                                exit 1
                            fi
                            
                            # 检查前端是否可访问
                            if ! curl -sf http://localhost:80; then
                                echo "Frontend health check failed"
                                exit 1
                            fi
                        '''
                    } catch (Exception e) {
                        error "部署失败，准备回滚: ${e.message}"
                    }
                }
            }
        }

        stage('Monitor Setup') {
            steps {
                dir('deploy/monitoring') {
                    sh """
                        # 部署监控服务
                        docker-compose up -d
                        
                        # 等待服务启动
                        sleep 10
                        
                        # 检查监控服务
                        curl -sf http://localhost:9090/-/healthy || exit 1  # Prometheus
                        curl -sf http://localhost:3000/api/health || exit 1 # Grafana
                        curl -sf http://localhost:9200/_cluster/health || exit 1 # Elasticsearch
                    """
                }
            }
        }
    }

    post {
        failure {
            script {
                // 回滚到之前版本
                if (PREVIOUS_VERSION) {
                    echo "开始回滚到版本: ${PREVIOUS_VERSION}"
                    sh """
                        cd deploy/backend/docker
                        export VERSION=${PREVIOUS_VERSION}
                        docker-compose down
                        docker-compose up -d
                    """
                }
                
                // 发送钉钉通知
                sh """
                    curl 'https://oapi.dingtalk.com/robot/send?access_token=your-token' \
                        -H 'Content-Type: application/json' \
                        -d '{
                            "msgtype": "text",
                            "text": {
                                "content": "❌ 部署失败！\n环境：生产环境\n版本：${VERSION}\n已回滚到版本：${PREVIOUS_VERSION}"
                            }
                        }'
                """
            }
        }
        
        success {
            // 发送成功通知
            sh """
                curl 'https://oapi.dingtalk.com/robot/send?access_token=your-token' \
                    -H 'Content-Type: application/json' \
                    -d '{
                        "msgtype": "text",
                        "text": {
                            "content": "✅ 部署成功！\n环境：生产环境\n版本：${VERSION}\n监控面板：http://grafana:3000\n日志系统：http://kibana:5601"
                        }
                    }'
            """
        }
        
        always {
            // 清理工作区
            cleanWs()
        }
    }
}