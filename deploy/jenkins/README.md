# Jenkins 部署配置说明

## 前置条件

1. Jenkins 凭据配置
   - 添加 Gitee SSH 密钥 (ID: gitee-ssh-key)
   - 添加阿里云容器镜像服务账号 (ID: aliyun-docker-registry)
   - 添加钉钉机器人 Token

2. Jenkins 插件要求
   - Git
   - Pipeline
   - Credentials
   - Docker Pipeline
   - Workspace Cleanup

## 配置步骤

1. 创建多分支流水线项目
   - 新建项目 -> 多分支流水线
   - 配置分支源：Gitee
   - 添加凭据：选择已配置的 Gitee SSH 密钥
   - 配置仓库URL：*************:music_15/music.git
   - 保存配置

2. 环境变量配置
   - 在 Jenkins 系统配置中添加全局环境变量
   - 配置阿里云相关信息
   - 配置服务器信息

3. 构建触发器配置
   - 配置 Gitee WebHook
   - 设置定时构建（可选）

## 部署流程

1. 代码检出
   - 从 Gitee 拉取后端代码
   - 从 Gitee 拉取前端代码

2. 准备阶段
   - 登录阿里云容器镜像服务
   - 获取当前运行版本（用于回滚）

3. 备份
   - 备份数据库
   - 备份上传文件
   - 备份配置文件

4. 构建
   - 并行构建后端和前端
   - 推送镜像到阿里云

5. 部署
   - 更新环境变量
   - 部署服务
   - 健康检查

6. 监控设置
   - 部署监控服务
   - 检查监控服务状态

## 回滚机制

- 自动回滚：部署失败时自动回滚到上一个版本
- 手动回滚：可通过 Jenkins 参数化构建触发回滚

## 监控告警

- Prometheus：性能指标收集
- Grafana：可视化和告警
- ELK：日志收集和分析

## 通知机制

- 钉钉通知：部署成功/失败通知
- 邮件通知（可选）：详细部署报告