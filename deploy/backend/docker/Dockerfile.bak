# 构建阶段
FROM golang:1.21-alpine AS builder

WORKDIR /build

# 安装依赖
RUN apk add --no-cache git ca-certificates tzdata && \
    update-ca-certificates

# 复制源代码
COPY . .

# 下载依赖
RUN go mod download

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o music-service main.go

# 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata && \
    update-ca-certificates && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder /build/music-service /app/
COPY --from=builder /build/etc/service_prod.yaml /app/etc/api.yaml

# 创建必要的目录
RUN mkdir -p /app/file/upload /app/file/export /app/logs

EXPOSE 8888

ENV TZ=Asia/Shanghai

CMD ["/app/music-service", "-f", "/app/etc/api.yaml"]