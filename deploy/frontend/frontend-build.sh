#!/bin/bash

# 设置环境变量
export VERSION=$(date +%Y%m%d_%H%M%S)
export ALIYUN_REGISTRY="registry.cn-guangzhou.aliyuncs.com"
export ALIYUN_NAMESPACE="music-basic"
export DOCKER_USER="allbymusicchino"
export DOCKER_PASSWORD="Allbymusic163"

# 清理工作目录
rm -rf *

# 登录阿里云容器服务
docker login ${ALIYUN_REGISTRY} \
    -u ${DOCKER_USER} \
    -p ${DOCKER_PASSWORD}

# 构建并推送镜像
docker build \
    -t ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/music-web:${VERSION} \
    -f deploy/frontend/docker/Dockerfile .

docker push ${ALIYUN_REGISTRY}/${ALIYUN_NAMESPACE}/music-web:${VERSION}

# 部署服务
cd deploy/frontend/docker

# 备份当前版本号
PREVIOUS_VERSION=$(docker ps | grep music-web | awk '{print $2}' | cut -d':' -f2)
echo ${PREVIOUS_VERSION} > previous_version.txt

# 更新环境变量
cat > .env << EOF
VERSION=${VERSION}
ALIYUN_REGISTRY=${ALIYUN_REGISTRY}
ALIYUN_NAMESPACE=${ALIYUN_NAMESPACE}
EOF

# 重启服务
docker-compose down
docker-compose up -d

# 等待服务启动
sleep 30

# 健康检查
if ! curl -sf http://localhost:80; then
    echo "前端服务健康检查失败，准备回滚..."
    export VERSION=${PREVIOUS_VERSION}
    docker-compose down
    docker-compose up -d
    exit 1
fi

# 清理旧镜像
docker image prune -f

echo "前端部署成功！版本: ${VERSION}"