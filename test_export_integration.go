package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"music/internal/client"
	"music/internal/config"
)

func main() {
	fmt.Println("=== 导出功能集成测试 ===")

	// 1. 测试配置加载
	fmt.Println("1. 测试配置...")
	conf := &config.Config{
		File: struct {
			UploadPath string `json:",default=./file/upload"`
			ExportPath string `json:",default=./file/exports"`
		}{
			UploadPath: "./file/upload",
			ExportPath: "./file/exports",
		},
		YjxOss: struct {
			EndPoint        string
			AccessKeyId     string
			AccessKeySecret string
			BucketName      string
			PlatformOssPath string
			AnchorOssPath   string
			LicenseOssPath  string
		}{
			EndPoint:        "http://oss-cn-beijing.aliyuncs.com",
			AccessKeyId:     "LTAI5tRJpJKEkr54ceE5nuYK",
			AccessKeySecret: "******************************",
			BucketName:      "shengwangshuju",
			PlatformOssPath: "shengwangshiyongshuju/platform/",
			AnchorOssPath:   "shengwangshiyongshuju/host/",
			LicenseOssPath:  "shengwangshiyongshuju/drm/",
		},
	}
	fmt.Printf("✓ 配置加载成功\n")
	fmt.Printf("  - ExportPath: %s\n", conf.File.ExportPath)
	fmt.Printf("  - PlatformOssPath: %s\n", conf.YjxOss.PlatformOssPath)

	// 2. 测试OSS客户端初始化
	fmt.Println("\n2. 测试OSS客户端初始化...")
	ossClient, err := client.NewYjxOssClient(conf)
	if err != nil {
		log.Fatalf("❌ 创建OSS客户端失败: %v", err)
	}
	fmt.Printf("✓ OSS客户端创建成功\n")

	// 3. 测试导出目录创建
	fmt.Println("\n3. 测试导出目录...")
	exportDir := filepath.FromSlash(conf.File.ExportPath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		log.Fatalf("❌ 创建导出目录失败: %v", err)
	}
	fmt.Printf("✓ 导出目录创建成功: %s\n", exportDir)

	// 4. 模拟生成临时文件
	fmt.Println("\n4. 模拟生成导出文件...")
	tempDirName := fmt.Sprintf("playRecordStats_%s", time.Now().Format("20060102150405"))
	tempDir := filepath.Join(exportDir, tempDirName)
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		log.Fatalf("❌ 创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建模拟的Excel文件
	testExcelPath := filepath.Join(tempDir, "test_export_UTC.xlsx")
	testContent := "模拟的Excel导出数据 - " + time.Now().Format("2006-01-02 15:04:05")
	if err := os.WriteFile(testExcelPath, []byte(testContent), 0644); err != nil {
		log.Fatalf("❌ 创建测试Excel文件失败: %v", err)
	}
	fmt.Printf("✓ 模拟Excel文件创建成功: %s\n", testExcelPath)

	// 5. 模拟创建ZIP文件
	fmt.Println("\n5. 模拟创建ZIP文件...")
	zipFileName := fmt.Sprintf("export_%s.zip", time.Now().Format("20060102150405"))
	zipFilePath := filepath.Join(exportDir, zipFileName)
	zipContent := "模拟的ZIP文件内容 - " + time.Now().Format("2006-01-02 15:04:05")
	if err := os.WriteFile(zipFilePath, []byte(zipContent), 0644); err != nil {
		log.Fatalf("❌ 创建ZIP文件失败: %v", err)
	}
	defer os.Remove(zipFilePath) // 清理
	fmt.Printf("✓ ZIP文件创建成功: %s\n", zipFilePath)

	// 6. 测试上传到OSS
	fmt.Println("\n6. 测试上传到OSS...")
	ossPath, err := ossClient.UploadFileToPlatform(zipFilePath, zipFileName)
	if err != nil {
		log.Fatalf("❌ 上传文件到OSS失败: %v", err)
	}
	fmt.Printf("✓ 文件上传成功，OSS路径: %s\n", ossPath)

	// 7. 测试生成下载URL
	fmt.Println("\n7. 测试生成下载URL...")
	downloadUrl, err := ossClient.GeneratePresignedURL(ossPath, 24*time.Hour)
	if err != nil {
		log.Fatalf("❌ 生成下载URL失败: %v", err)
	}
	fmt.Printf("✓ 下载URL生成成功\n")
	fmt.Printf("  URL: %s\n", downloadUrl)

	// 8. 测试获取公共URL
	fmt.Println("\n8. 测试公共URL...")
	publicUrl := ossClient.GetPublicURL(ossPath)
	fmt.Printf("✓ 公共URL: %s\n", publicUrl)

	fmt.Println("\n=== 所有测试通过！导出功能集成正常 ===")
	fmt.Println("\n完整流程总结:")
	fmt.Println("1. ✓ 配置加载")
	fmt.Println("2. ✓ OSS客户端初始化")
	fmt.Println("3. ✓ 导出目录创建")
	fmt.Println("4. ✓ Excel文件生成")
	fmt.Println("5. ✓ ZIP文件打包")
	fmt.Println("6. ✓ 上传到OSS PlatformOssPath")
	fmt.Println("7. ✓ 生成预签名下载URL")
	fmt.Println("8. ✓ 获取公共访问URL")
}
